#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Description : NIO新车购买倾向数据集重组织工具
               将数据重新划分为训练集/验证集/测试集(70%/15%/15%)
"""
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import json
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetReorganizer:
    def __init__(self, 
                 data_dir="data/dataset_nio_new_car_v15",
                 output_dir="data/processed",
                 train_date="20240430",
                 test_date="20240531",
                 random_state=42):
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.train_date = train_date
        self.test_date = test_date
        self.random_state = random_state
        
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
    def load_data(self):
        """加载原始数据分区"""
        logger.info(f"加载训练日期分区: {self.train_date}")
        train_part = pd.read_parquet(f"{self.data_dir}/datetime={self.train_date}")
        
        logger.info(f"加载测试日期分区: {self.test_date}")
        test_part = pd.read_parquet(f"{self.data_dir}/datetime={self.test_date}")
        
        try:
            logger.info(f"加载评估文件: 20240531_随机采样1%.parquet")
            eval_part = pd.read_parquet(f"{self.data_dir}/20240531_随机采样1%.parquet")
        except:
            logger.warning("评估文件不存在，将只使用主分区数据")
            eval_part = None
        
        # 合并所有数据
        all_data = []
        if train_part is not None:
            train_part['original_partition'] = 'train'
            all_data.append(train_part)
        
        if test_part is not None:
            test_part['original_partition'] = 'test'
            all_data.append(test_part)
            
        if eval_part is not None:
            eval_part['original_partition'] = 'eval'
            all_data.append(eval_part)
            
        # 创建合并数据前先复制，避免产生高度碎片化警告
        all_data = [df.copy() for df in all_data]
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 日志记录数据情况
        logger.info(f"合并后总数据量: {len(combined_data)}")
        logger.info(f"特征数量: {len(combined_data.columns)}")
        
        # 确认标签列存在
        label_col = "purchase_days_nio_new_car_total"
        if label_col not in combined_data.columns:
            raise ValueError(f"标签列 {label_col} 不存在")
            
        # 处理标签列
        try:
            # 复制数据框避免产生碎片化警告
            combined_data = combined_data.copy()
            combined_data['label'] = pd.to_numeric(combined_data[label_col], errors='coerce')
            combined_data['label'] = (combined_data['label'] > 0).astype(int)
        except:
            logger.warning("无法将标签转换为数值，尝试字符串判断")
            combined_data['label'] = combined_data[label_col].apply(
                lambda x: 1 if str(x).strip() != '0' and str(x).strip() != '' and str(x).lower() != 'nan' else 0
            )
        
        self.data = combined_data
        logger.info(f"正样本率: {combined_data['label'].mean()*100:.2f}%")
        
        return combined_data
    
    def fix_data_types(self):
        """修复数据类型问题，确保特征类型与数据字典一致"""
        logger.info("修复数据类型...")
        
        # 加载数据字典以获取正确的类型信息
        try:
            dict_path = "data/configs/dataset_nio_new_car_v15.json"
            if os.path.exists(dict_path):
                with open(dict_path, 'r', encoding='utf-8') as f:
                    data_dict = json.load(f)
                logger.info(f"已加载数据字典: {len(data_dict)}个特征")
            else:
                logger.warning("数据字典文件不存在，将尝试自动推断类型")
                data_dict = {}
        except:
            logger.warning("无法加载数据字典，将尝试自动推断类型")
            data_dict = {}
            
        # 确保所有可能导致类型转换问题的列都使用正确的类型
        if not hasattr(self, 'data'):
            logger.error("请先加载数据")
            return False
            
        # 保存原始数据副本
        df = self.data.copy()
        
        # 处理所有特征列
        for col in df.columns:
            # 跳过标签和元数据列
            if col in ['label', 'dataset', 'original_partition']:
                continue
                
            # 从数据字典获取数据类型信息
            if col in data_dict:
                data_type = data_dict[col].get('data_type', 'unknown')
            else:
                # 如果在数据字典中没有，尝试推断
                if "_seq" in col:
                    data_type = "string"  # 序列型特征
                elif df[col].dtype == 'object' and df[col].notna().any():
                    # 尝试转换为数值型，如果失败则保持字符串
                    try:
                        pd.to_numeric(df[col], errors='raise')
                        data_type = "double"
                    except:
                        data_type = "string"
                else:
                    data_type = "unknown"
            
            # 根据数据类型进行处理
            if data_type == "string":
                # 确保字符串类型
                df[col] = df[col].astype(str)
            elif data_type == "double" or data_type == "float":
                # 尝试转换为浮点型，错误时填充为NaN
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except:
                    logger.warning(f"无法将列 {col} 转换为数值类型，保持原样")
            elif data_type == "int" or data_type == "integer":
                # 尝试转换为整型，错误时填充为NaN再转为整型
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
                except:
                    logger.warning(f"无法将列 {col} 转换为整数类型，保持原样")
            
            # 对于存在混合类型可能导致parquet写入错误的特殊处理
            try:
                # 检测是否为可能造成问题的混合类型
                if df[col].dtype == 'object' and not all(isinstance(x, (str, type(None))) for x in df[col].dropna()):
                    logger.warning(f"列 {col} 包含混合类型，强制转换为字符串")
                    df[col] = df[col].astype(str)
            except:
                logger.warning(f"处理列 {col} 时出错，强制转换为字符串")
                df[col] = df[col].astype(str)
        
        # 更新数据
        self.data = df
        logger.info("数据类型修复完成")
        return True
    
    def split_data(self, test_size_final=0.15, val_size=0.15):
        """按70/15/15比例进行分层抽样划分"""
        if not hasattr(self, 'data'):
            self.load_data()
            
        # 确保数据类型正确
        self.fix_data_types()
            
        data = self.data
        
        # 先分离出测试集
        train_val, test = train_test_split(
            data, 
            test_size=test_size_final, 
            random_state=self.random_state,
            stratify=data['label']
        )
        
        # 再从剩余数据中分离出验证集
        val_relative_size = val_size / (1 - test_size_final)
        train, val = train_test_split(
            train_val, 
            test_size=val_relative_size, 
            random_state=self.random_state,
            stratify=train_val['label']
        )
        
        logger.info(f"划分结果:")
        logger.info(f"训练集: {len(train)}行, 正样本率: {train['label'].mean()*100:.2f}%")
        logger.info(f"验证集: {len(val)}行, 正样本率: {val['label'].mean()*100:.2f}%")
        logger.info(f"测试集: {len(test)}行, 正样本率: {test['label'].mean()*100:.2f}%")
        
        # 保存数据集标识
        train = train.copy()
        val = val.copy()
        test = test.copy()
        
        train['dataset'] = 'train'
        val['dataset'] = 'validation'
        test['dataset'] = 'test'
        
        self.train_data = train
        self.val_data = val
        self.test_data = test
        
        return train, val, test
    
    def save_datasets(self, format='parquet'):
        """保存重组织后的数据集"""
        if not all(hasattr(self, attr) for attr in ['train_data', 'val_data', 'test_data']):
            self.split_data()
            
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
            
        if format == 'parquet':
            logger.info("保存训练集...")
            self.train_data.to_parquet(f"{self.output_dir}/train.parquet", index=False)
            logger.info("保存验证集...")
            self.val_data.to_parquet(f"{self.output_dir}/validation.parquet", index=False)
            logger.info("保存测试集...")
            self.test_data.to_parquet(f"{self.output_dir}/test.parquet", index=False)
        elif format == 'csv':
            logger.info("保存训练集...")
            self.train_data.to_csv(f"{self.output_dir}/train.csv", index=False)
            logger.info("保存验证集...")
            self.val_data.to_csv(f"{self.output_dir}/validation.csv", index=False)
            logger.info("保存测试集...")
            self.test_data.to_csv(f"{self.output_dir}/test.csv", index=False)
        else:
            raise ValueError(f"不支持的格式: {format}")
            
        logger.info(f"数据集已保存到: {self.output_dir}")
        
        # 创建数据集说明文件
        dataset_info = {
            "dataset_version": "v1.0",
            "creation_date": pd.Timestamp.now().strftime("%Y-%m-%d"),
            "source_partitions": {
                "train_date": self.train_date,
                "test_date": self.test_date
            },
            "statistics": {
                "total_samples": len(self.data),
                "train_samples": len(self.train_data),
                "validation_samples": len(self.val_data),
                "test_samples": len(self.test_data),
                "positive_rate": {
                    "overall": float(self.data['label'].mean()),
                    "train": float(self.train_data['label'].mean()),
                    "validation": float(self.val_data['label'].mean()),
                    "test": float(self.test_data['label'].mean())
                }
            },
            "feature_count": len(self.data.columns) - 3,  # 排除label, dataset, original_partition
            "split_method": "stratified_sampling",
            "split_ratio": "70/15/15"
        }
        
        with open(f"{self.output_dir}/dataset_info.json", 'w', encoding='utf-8') as f:
            json.dump(dataset_info, f, indent=2, ensure_ascii=False)
            
        logger.info(f"数据集信息已保存到: {self.output_dir}/dataset_info.json")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='NIO新车购买倾向数据集重组织工具')
    parser.add_argument('--data_dir', type=str, default='data/dataset_nio_new_car_v15', help='原始数据目录')
    parser.add_argument('--output_dir', type=str, default='data/processed', help='输出数据目录')
    parser.add_argument('--train_date', type=str, default='20240430', help='训练分区日期')
    parser.add_argument('--test_date', type=str, default='20240531', help='测试分区日期')
    parser.add_argument('--random_state', type=int, default=42, help='随机种子')
    parser.add_argument('--format', type=str, default='parquet', choices=['parquet', 'csv'], help='输出文件格式')
    
    args = parser.parse_args()
    
    reorganizer = DatasetReorganizer(
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        train_date=args.train_date,
        test_date=args.test_date,
        random_state=args.random_state
    )
    
    reorganizer.load_data()
    reorganizer.split_data()
    reorganizer.save_datasets(format=args.format)
    
    logger.info("数据集重组织完成")

if __name__ == "__main__":
    main()