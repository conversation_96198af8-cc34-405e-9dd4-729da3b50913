"""
ESM2 Multi-task Model Prediction Script

This script demonstrates how to load and use a trained ESM2 model
for making predictions on new data.
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
import logging
from sklearn.preprocessing import StandardScaler, LabelEncoder

# Import model loading function
from train_esm2 import load_esm2_model, prepare_input_data, load_and_process_adult_data_for_esm2

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def predict_with_esm2(model, X, feature_config):
    """Make predictions with ESM2 model.
    
    Args:
        model: Trained ESM2 model
        X: Input features (numpy array)
        feature_config: Feature configuration dictionary
    
    Returns:
        Dictionary of task predictions
    """
    logger.info(f"Making predictions on {X.shape[0]} samples...")
    inputs = prepare_input_data(X, feature_config)
    predictions = model.predict(inputs)
    
    # Format predictions nicely
    results = {}
    for task_name, preds in predictions.items():
        results[task_name] = preds.flatten()
    
    return results

def prepare_single_sample(sample, feature_config, encoders=None):
    """Prepare a single sample for prediction.
    
    Args:
        sample: Dictionary of feature values
        feature_config: Feature configuration
        encoders: Dictionary of encoders for categorical features (optional)
    
    Returns:
        Prepared sample as numpy array
    """
    processed_sample = []
    
    # Process numerical features
    for col in feature_config['numerical_cols']:
        if col in sample:
            processed_sample.append(sample[col])
        else:
            # Use a default value (0) if feature is missing
            processed_sample.append(0)
    
    # Process categorical features
    for col in feature_config['categorical_cols']:
        if col in sample and encoders and col in encoders:
            # Encode using provided encoder
            try:
                encoded_val = encoders[col].transform([sample[col]])[0]
            except ValueError:
                # If value not seen during training, use default
                encoded_val = 0
            processed_sample.append(encoded_val)
        else:
            # Use default value
            processed_sample.append(0)
    
    return np.array(processed_sample).reshape(1, -1)

def main():
    """Main function for demonstrating ESM2 prediction."""
    # Settings
    data_path = "../../data/adult/adult.data" 
    model_dir = "../../models/esm2"
    
    # Check if model exists
    if not os.path.exists(os.path.join(model_dir, "esm2_model.keras")):
        logger.error(f"Model not found at {model_dir}/esm2_model.keras.")
        logger.error("Please train the model first using train_esm2.py.")
        return
    
    # Load model
    logger.info("Loading trained ESM2 model...")
    model = load_esm2_model(model_dir)
    if model is None:
        logger.error("Failed to load model.")
        return
    
    # Load test data
    logger.info("Loading test data...")
    try:
        (_, _), (X_test, y_test_dict), feature_config = load_and_process_adult_data_for_esm2(data_path)
        logger.info(f"Loaded {X_test.shape[0]} test samples.")
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return
    
    # Make predictions on test data
    predictions = predict_with_esm2(model, X_test[:5], feature_config)
    
    # Print predictions for first 5 samples
    logger.info("Predictions for 5 samples:")
    task_names = list(predictions.keys())
    
    # Create a pandas DataFrame for pretty printing
    df_results = pd.DataFrame()
    for task in task_names:
        df_results[task] = predictions[task][:5]
        # Add true labels if available
        if task in y_test_dict:
            df_results[f"{task}_true"] = y_test_dict[task][:5]
    
    print(df_results.to_string(float_format="{:.4f}".format))
    
    # Example of preparing a custom sample
    logger.info("\nExample of prediction on a custom sample:")
    sample = {
        'age': 45,
        'workclass': 'Private',
        'education': 'Bachelors',
        'marital_status': 'Married-civ-spouse',
        'occupation': 'Exec-managerial',
        'gender': 'Male',
        'capital_gain': 0,
        'hours_per_week': 50
    }
    
    logger.info(f"Custom sample: {sample}")
    
    # In a real application, we would save the encoders during training
    # Here, we'll extract encoders from the dataset to encode our custom sample
    logger.info("Extracting encoders from training data to encode custom sample...")
    
    # Create encoders from the dataset
    encoders = {}
    categorical_cols = feature_config['categorical_cols']
    df = pd.read_csv(data_path, names=[
        'age', 'workclass', 'fnlwgt', 'education', 'education_num', 
        'marital_status', 'occupation', 'relationship', 'race', 'gender',
        'capital_gain', 'capital_loss', 'hours_per_week', 'native_country', 'income_50k'
    ], sep=',\s+', engine='python', na_values='?')
    df = df.dropna()
    
    for col in categorical_cols:
        encoders[col] = LabelEncoder()
        encoders[col].fit(df[col])
    
    # Fill in missing features in the sample with defaults
    for col in feature_config['numerical_cols']:
        if col not in sample:
            sample[col] = 0
    
    for col in categorical_cols:
        if col not in sample:
            sample[col] = df[col].iloc[0]  # Use the first value as default
    
    # Prepare the custom sample
    processed_sample = []
    
    # Standardization for numerical features
    # In a real application, we'd save the scaler during training
    scaler = StandardScaler()
    df_numerical = df[feature_config['numerical_cols']]
    scaler.fit(df_numerical)
    
    # Process numerical features with standardization
    numerical_values = []
    for col in feature_config['numerical_cols']:
        numerical_values.append(sample[col])
    
    # Transform all numerical features together
    # Create a DataFrame with the same column names to avoid warnings
    sample_df = pd.DataFrame([numerical_values], columns=feature_config['numerical_cols'])
    scaled_numerical = scaler.transform(sample_df)[0]
    
    # Add to processed sample
    for value in scaled_numerical:
        processed_sample.append(value)
    
    # Process categorical features
    for col in categorical_cols:
        try:
            encoded_val = encoders[col].transform([sample[col]])[0]
        except ValueError:
            logger.warning(f"Value '{sample[col]}' not seen during training for feature '{col}'. Using default.")
            encoded_val = 0
        processed_sample.append(encoded_val)
    
    # Convert to numpy array and reshape
    custom_X = np.array(processed_sample).reshape(1, -1)
    
    # Make prediction
    custom_predictions = predict_with_esm2(model, custom_X, feature_config)
    
    # Print results
    logger.info("Predictions for custom sample:")
    for task, preds in custom_predictions.items():
        logger.info(f"  {task}: {preds[0]:.4f}")
    
    # Interpret predictions
    ctr_prob = custom_predictions['ctr_output'][0]
    ctavr1_prob = custom_predictions['ctavr1_output'][0]
    ctcvr_prob = custom_predictions['ctcvr_output'][0]
    
    logger.info("\nProbability Interpretation:")
    logger.info(f"  CTR (P(Click)): {ctr_prob:.4f} ({ctr_prob*100:.2f}%)")
    
    # Conditional probability of Action1 given Click
    if ctr_prob > 0:
        conditional_avr1 = ctavr1_prob / ctr_prob
    else:
        conditional_avr1 = 0
    logger.info(f"  P(Action1 | Click): {conditional_avr1:.4f} ({conditional_avr1*100:.2f}%)")
    
    # Conditional probability of Conversion given Action1
    if ctavr1_prob > 0:
        conditional_cvr = ctcvr_prob / ctavr1_prob
    else:
        conditional_cvr = 0
    logger.info(f"  P(Conversion | Action1): {conditional_cvr:.4f} ({conditional_cvr*100:.2f}%)")
    
    logger.info(f"  Overall P(Conversion | Impression): {ctcvr_prob:.4f} ({ctcvr_prob*100:.2f}%)")
    
if __name__ == "__main__":
    main() 