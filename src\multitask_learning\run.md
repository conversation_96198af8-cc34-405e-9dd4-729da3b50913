
1. **使用 Adult 数据集**：
```bash
python src/multitask_learning/compare_models.py --dataset adult --epochs 10 --batch_size 256
```

2. **使用基本的合成 AliExpress 数据集**：
```bash
# 先创建数据
python src/multitask_learning/data_create.py --dataset_name NL --num_samples 20000

# 然后比较模型
python src/multitask_learning/compare_models.py --dataset aliexpress --epochs 10 --batch_size 256
```

3. **使用不同参数或区域的合成数据**：
```bash
# 创建不同区域的数据
python src/multitask_learning/data_create.py --dataset_name FR --num_samples 30000

# 使用特定数据路径
python src/multitask_learning/compare_models.py --dataset aliexpress --data_path data/AliExpress_FR --epochs 10 --batch_size 256

---
# ESM2 (Extended Space Multi-task Model)

## 概览

ESM2模型是ESMM模型的扩展版本，支持多个任务之间的概率链建模。ESMM模型用于解决CVR预估的样本选择偏差问题，而ESM2模型将其扩展至更多任务，实现多个任务间的联合建模。

在ESM2中，任务链如下所示:
- pCTR: 点击率预估 (第一个任务)
- pCTAVR1: 点击并采取行动1的概率 (第二个任务)
- pCTCVR: 点击并采取行动1并完成转化的概率 (第三个任务)

ESM2的关键优势在于它在整个样本空间上进行学习，通过概率链共享信息，每个任务专注于学习对应的条件概率分布，从而缓解稀疏性和样本选择偏差问题。

## 模型架构

ESM2模型扩展了ESMM的架构:

1. **共享底层特征表示**: 所有任务共享同一套输入特征嵌入
2. **任务塔**: 每个任务有独立的神经网络"塔"
3. **概率链**: 多个任务通过概率相乘构成链式关系
   - pCTR = sigmoid(tower_ctr)
   - pCTAVR1 = pCTR * sigmoid(tower_ctavr1) [条件概率]
   - pCTCVR = pCTAVR1 * sigmoid(tower_ctcvr) [条件概率]

## 使用方法

### 训练新模型

```bash
python train_esm2.py --mode train --epochs 20 --batch_size 1024
```

### 评估已训练的模型

```bash
python train_esm2.py --mode eval
```

### 分析特征重要性

```bash
python train_esm2.py --mode analyze
```

## 数据格式

ESM2模型使用UCI Adult数据集进行演示:
- 点击 (CTR): income_50k 是否 >50K
- 行动1 (CTAVR1): marital_status 是否为 'Married-civ-spouse'
- 转化 (CTCVR): capital_gain 是否 > 0 (且已采取行动1)

## 如何扩展

要将ESM2应用于自定义数据集:
1. 修改 `load_and_process_adult_data_for_esm2` 函数以加载您的数据
2. 定义任务标签，确保它们遵循概率链顺序
3. 调整 `task_names` 以匹配您的任务命名

## 注意事项

- 模型默认保存在 `models/esm2/` 目录
- 训练历史和特征重要性图表默认保存在工作目录
- 任务顺序很重要，请确保它们按照概率链的正确顺序定义

## 实现细节

本次实现包含以下主要组件:

1. **ESM2模型架构** (`esm2_model.py`):
   - 使用TensorFlow和Keras实现了可序列化的ESM2模型
   - 通过`@register_keras_serializable`装饰器确保了模型可以保存和加载
   - 实现了带有共享底层特征和任务特定塔的多任务学习架构

2. **训练脚本** (`train_esm2.py`):
   - 数据加载和预处理功能
   - 模型训练、评估和保存功能
   - 特征重要性分析
   - 支持命令行参数控制训练/评估/分析模式

3. **预测脚本** (`predict_esm2.py`):
   - 加载预训练模型
   - 为新样本准备特征
   - 预测多个任务的概率
   - 解释预测结果，包括条件概率计算

### 运行结果

- 在Adult数据集上的测试结果:
  - CTR任务: AUC=0.88, Accuracy=0.83
  - CTAVR1任务: AUC=0.91, Accuracy=0.79
  - CTCVR任务: AUC=0.99, Accuracy=0.96

- 特征重要性分析显示，对于该数据集，婚姻状况(marital_status)和关系(relationship)是最重要的特征

### 后续改进方向

1. 增加更多的特征工程方法
2. 尝试不同的深度学习架构，如注意力机制
3. 增加模型解释性功能，如SHAP值分析
4. 处理数据类别不平衡问题
5. 扩展到更多任务场景 

---

## 多任务学习模型比较

本仓库实现了三种主要的用于推荐任务的多任务学习模型：

1.  **ESMM (Entire Space Multi-task Model, 全空间多任务模型)**
    *   处理两个任务：CTR (Click-Through Rate, 点击率) 和 CTCVR (Click-Through Conversion Rate, 点击转化率)
    *   使用共享的嵌入层和任务特定的 DNN 网络塔
    *   实现全空间策略，通过概率相乘：pCTCVR = pCTR × pCVR
    *   解决序列用户行为建模中常见的样本选择偏差和数据稀疏性问题

2.  **MMoE (Multi-gate Mixture-of-Experts, 多门控混合专家模型)**
    *   通过带有任务特定门控网络的共享专家网络支持多个任务
    *   默认使用 4 个专家，包含专家特定的网络和任务特定的网络塔
    *   创建一个门控机制，为每个任务以不同方式组合专家输出
    *   与硬参数共享相比，提供更好的任务特定表示学习

3.  **ESM2 (Extended Space Multi-task Model, 扩展空间多任务模型)**
    *   ESMM 的扩展，支持 3 个及以上的序列任务
    *   示例：CTR → CTAVR1 (或 CTBR) → CTCVR
    *   通过条件概率实现概率链
    *   使用共享的嵌入层和任务特定的网络塔
    *   维持约束：pCTCVR = pCTR × pCTAVR1 × pCVR|CTAVR1

### 实现细节

-   所有模型均使用：
    *   类别特征的共享嵌入
    *   数值特征的向量化处理
    *   具有可配置架构的任务特定 DNN 网络塔
    *   使用 Sigmoid 激活函数的二分类输出
    *   可配置的正则化和批量归一化

### 框架比较特性

`compare_models.py` 脚本提供了一个系统性的比较：
-   加载和预处理数据（支持 adult 数据集和 AliExpress 数据集）
-   使用相同的超参数和早停法训练所有三个模型
-   使用以下指标在测试数据上评估模型：
    *   AUC (Area Under ROC Curve, ROC 曲线下面积)
    *   Accuracy (准确率)
    *   Log Loss (对数损失)
    *   Training time (训练时间)

### 训练过程

对于每个模型：
1.  使用合适的架构构建网络
2.  使用 Adam 优化器和二元交叉熵损失进行编译
3.  使用早停法和学习率调度
4.  处理评估指标的特殊情况（例如，当测试集中只存在一个类别时）
5.  报告任务特定的性能指标

### 各模型的主要优势

-   **ESMM**：通过在全空间进行训练来处理样本选择偏差
-   **MMoE**：通过专家机制平衡共享表示和任务特定学习
-   **ESM2**：通过多步序列建模扩展到更长的行为链

这个多任务学习框架为推荐系统中的序列用户行为建模提供了一个全面的解决方案，并具有处理不同用例和数据集的灵活性。

---

## 多任务学习模型比较结果分析

cd src/multitask_learning && python compare_models.py --dataset adult --epochs 5 --batch_size 256

我们成功运行了比较脚本，比较了三种多任务学习模型在Adult数据集上的性能。以下是结果分析：

### 1. 数据集和任务说明

- 数据集：Adult收入预测数据集
- 主要任务：
  - CTR（点击率）任务：预测收入是否大于50K
  - CTCVR（点击转化率）任务：预测收入大于50K且已婚
  - ESM2额外的中间任务（CTAVR1）：收入预测和婚姻状态的联合预测

### 2. 模型性能比较

#### 训练效率比较
- ESMM：2.92秒
- MMOE：4.07秒
- ESM2：3.85秒

ESMM模型训练速度最快，这是因为它的结构相对简单，只有两个任务塔。MMOE由于使用了门控专家机制，计算复杂度更高，因此是最慢的。

#### 任务性能比较

**CTR任务 (收入预测)：**
- MMOE：AUC=0.9025，准确率=0.8074，对数损失=0.4078
- ESMM：AUC=0.9060 (最佳)，准确率=0.8076，对数损失=0.3914
- ESM2：AUC=0.9007，准确率=0.8178 (最佳准确率)，对数损失=0.3949

**CTCVR任务 (收入和婚姻状态)：**
- MMOE：AUC=0.9440 (最佳)，准确率=0.8105，对数损失=0.3346
- ESMM：AUC=0.9429，准确率=0.8225 (最佳准确率)，对数损失=0.3271
- ESM2：AUC=0.9419，准确率=0.8205，对数损失=0.3126 (最佳对数损失)

**ESM2特有的中间任务 (CTAVR1)：**
- AUC=0.9249，准确率=0.8162，对数损失=0.3404

### 3. 结果解读

1. **CTR任务**：
   - ESMM模型在CTR任务上的AUC最高，这表明ESMM在单一任务预测（收入预测）上表现很好。
   - ESM2的准确率略高，这可能是因为ESM2能够从多阶段预测中获益。

2. **CTCVR任务**：
   - MMOE在CTCVR任务上的AUC最高，这证明了MMOE在处理任务间关系复杂的情况下更有优势。
   - ESM2在对数损失方面表现最好，这意味着它的概率估计最准确。
   - ESMM的准确率最高，显示其简单架构在某些场景下仍很有效。

3. **ESM2的优势**：
   - ESM2能够显式建模多阶段转化漏斗，从中间任务（收入和婚姻状态预测）的良好性能可以看出。
   - 虽然ESM2在端到端评价指标上并非最优，但它提供了更丰富的用户行为序列理解。

### 4. 模型特点分析

1. **ESMM**：
   - 优势：结构简单、训练速度快、CTR任务表现优异
   - 适用场景：任务间高度相关、计算资源受限时

2. **MMOE**：
   - 优势：CTCVR任务表现最佳、适应不同任务间关系
   - 适用场景：任务相关性复杂、需要灵活知识共享时

3. **ESM2**：
   - 优势：支持多阶段转化漏斗建模、提供中间任务预测、对数损失较低
   - 适用场景：用户决策流程有多个阶段、需要细粒度用户行为理解时

### 5. 结论

不同模型适合不同场景：
- 如果主要关注单一目标（如CTR）且计算资源有限，ESMM是不错的选择
- 如果任务之间关系复杂，MMOE可能表现更好
- 如果需要建模多阶段决策过程并获得中间任务的预测，ESM2更为适合

这三种模型各有优势，选择时应根据具体业务场景、任务特点和计算资源考虑。整体来看，在Adult数据集上，MMOE和ESMM在不同任务上分别取得了最佳性能，而ESM2则提供了更为丰富的用户行为理解。
