#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化25: 基于历史最优EPMMOENet的增量改进
历史最佳: AUC 0.9146, PR-AUC 0.5619, Recall@840 0.9747
当前最佳: AUC 0.7896, PR-AUC 0.0500, Recall@840 0.2788
目标: 在EPMMOENet基础上应用已验证有效的优化技术，专注提升PR-AUC和Recall
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入历史最优模型组件
from data.nio_loader import NioDataLoader
from models.networks.EPMMOENet import EPMMOENet_Model
from data.preprocessor import DataPreprocessor
from configs.config_loader import ConfigLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

class HistoricalBestEnhancer:
    """基于历史最优模型的增强器"""
    
    def __init__(self, config_path="src/configs/models/sample_20250311_v7-20250311.json"):
        self.config_path = config_path
        self.config_loader = ConfigLoader()
        self.data_preprocessor = DataPreprocessor()
        
    def load_historical_config(self):
        """加载历史最优配置"""
        logger.info("加载历史最优模型配置...")
        
        # 加载模型配置
        model_config = self.config_loader.load_model_config(self.config_path)
        
        # 加载数据集配置
        dataset_config = self.config_loader.load_dataset_config(
            "src/configs/datasets/dataset_nio_new_car_v15.json"
        )
        
        logger.info(f"模型配置加载完成: {model_config['network_name']}")
        logger.info(f"InputGeneral特征数: {len(model_config['InputGeneral']['features'])}")
        logger.info(f"InputScene特征数: {len(model_config['InputScene']['features'])}")
        logger.info(f"InputSeqSet序列数: {len(model_config['InputSeqSet']['Set'])}")
        
        return model_config, dataset_config
    
    def prepare_historical_data(self, model_config, dataset_config):
        """准备历史最优模型的数据"""
        logger.info("准备历史最优模型数据...")
        
        # 加载原始数据
        data_loader = NioDataLoader()
        df = data_loader.load_data()
        
        # 使用历史最优的数据预处理
        processed_data = self.data_preprocessor.preprocess_data(
            df, model_config, dataset_config
        )
        
        logger.info(f"数据预处理完成: {processed_data['X'].shape}")
        
        return processed_data
    
    def create_enhanced_epmmoe_models(self, model_config, input_shape):
        """创建增强版EPMMOENet模型集成"""
        models = []
        
        # 模型1: 原始EPMMOENet
        logger.info("创建原始EPMMOENet模型...")
        model1 = EPMMOENet_Model(
            feature_column=model_config,
            output_dimension=1,
            output_activation="sigmoid",
            default_embedding_dimension=8,
            default_gru_dimension=32,
            expert_num=8,
            use_cross_layer=True,
            use_mixed_precision=True,
            use_time_attention=True,
            time_decay_factor=0.05
        )
        models.append(("Original_EPMMOENet", model1))
        
        # 模型2: 增强版EPMMOENet - 更大的嵌入维度
        logger.info("创建增强版EPMMOENet模型1...")
        model2 = EPMMOENet_Model(
            feature_column=model_config,
            output_dimension=1,
            output_activation="sigmoid",
            default_embedding_dimension=12,  # 增加嵌入维度
            default_gru_dimension=48,        # 增加GRU维度
            expert_num=8,
            use_cross_layer=True,
            use_mixed_precision=True,
            use_time_attention=True,
            time_decay_factor=0.03           # 调整时间衰减因子
        )
        models.append(("Enhanced_EPMMOENet_v1", model2))
        
        # 模型3: 增强版EPMMOENet - 更多专家
        logger.info("创建增强版EPMMOENet模型2...")
        model3 = EPMMOENet_Model(
            feature_column=model_config,
            output_dimension=1,
            output_activation="sigmoid",
            default_embedding_dimension=10,
            default_gru_dimension=40,
            expert_num=12,                   # 增加专家数量
            use_cross_layer=True,
            use_mixed_precision=True,
            use_time_attention=True,
            time_decay_factor=0.07           # 调整时间衰减因子
        )
        models.append(("Enhanced_EPMMOENet_v2", model3))
        
        return models
    
    def train_with_smote_enhancement(self, models, X_train, y_train, X_test, y_test):
        """使用SMOTE增强训练多个EPMMOENet模型"""
        logger.info("=== 开始SMOTE增强训练 ===")
        
        # 应用SMOTE重采样 - 使用已验证有效的参数
        logger.info("应用SMOTE重采样...")
        smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
        X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
        logger.info(f"SMOTE重采样: {len(X_train)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
        
        # 训练参数 - 使用已验证有效的配置
        callbacks = [
            tf.keras.callbacks.EarlyStopping(patience=8, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(patience=4, factor=0.5, min_lr=1e-6)
        ]
        
        trained_models = []
        predictions = []
        
        for i, (model_name, model) in enumerate(models, 1):
            logger.info(f"=== 训练{model_name} ===")
            
            # 编译模型
            model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['auc']
            )
            
            # 训练模型
            history = model.fit(
                X_train_smote, y_train_smote,
                validation_data=(X_test, y_test),
                epochs=15, batch_size=4096,  # 使用历史最优的大批量
                callbacks=callbacks,
                verbose=1 if i == 1 else 0
            )
            
            # 预测
            pred = model.predict(X_test, verbose=0)
            predictions.append(pred)
            trained_models.append((model_name, model))
            
            # 单模型评估
            auc_score = roc_auc_score(y_test, pred)
            pr_auc = calculate_pr_auc(y_test, pred)
            precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
            
            logger.info(f"{model_name} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
            logger.info(f"{model_name} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
        
        return trained_models, predictions
    
    def intelligent_ensemble(self, predictions, y_test):
        """智能集成 - 基于PR-AUC加权"""
        logger.info("=== 智能集成优化 ===")
        
        # 计算每个模型的PR-AUC权重
        pr_aucs = []
        for pred in predictions:
            pr_auc = calculate_pr_auc(y_test, pred)
            pr_aucs.append(pr_auc)
        
        # 使用PR-AUC作为权重进行加权平均
        pr_aucs = np.array(pr_aucs)
        weights = pr_aucs / pr_aucs.sum()
        
        # 加权集成
        weighted_ensemble = np.average(predictions, axis=0, weights=weights)
        
        # 简单平均集成作为对比
        simple_ensemble = np.mean(predictions, axis=0)
        
        return weighted_ensemble, simple_ensemble, weights, pr_aucs

def train_and_evaluate():
    """训练和评估基于历史最优的增强模型"""
    
    # 1. 初始化增强器
    enhancer = HistoricalBestEnhancer()
    
    # 2. 加载历史最优配置
    model_config, dataset_config = enhancer.load_historical_config()
    
    # 3. 准备历史最优数据
    processed_data = enhancer.prepare_historical_data(model_config, dataset_config)
    
    X = processed_data['X']
    y = processed_data['y']
    
    # 4. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. 创建增强版EPMMOENet模型集成
    models = enhancer.create_enhanced_epmmoe_models(model_config, X_train.shape)
    
    # 6. 使用SMOTE增强训练
    trained_models, predictions = enhancer.train_with_smote_enhancement(
        models, X_train, y_train, X_test, y_test
    )
    
    # 7. 智能集成
    weighted_ensemble, simple_ensemble, weights, pr_aucs = enhancer.intelligent_ensemble(
        predictions, y_test
    )
    
    # 8. 详细评估
    weighted_auc = roc_auc_score(y_test, weighted_ensemble)
    weighted_pr_auc = calculate_pr_auc(y_test, weighted_ensemble)
    weighted_precision_at_840, weighted_recall_at_840 = calculate_precision_recall_at_k(y_test, weighted_ensemble, k=840)
    
    simple_auc = roc_auc_score(y_test, simple_ensemble)
    simple_pr_auc = calculate_pr_auc(y_test, simple_ensemble)
    simple_precision_at_840, simple_recall_at_840 = calculate_precision_recall_at_k(y_test, simple_ensemble, k=840)
    
    logger.info(f"=== 基于历史最优的增强结果 ===")
    logger.info(f"加权集成 - AUC: {weighted_auc:.4f}, PR-AUC: {weighted_pr_auc:.4f}")
    logger.info(f"加权集成 - P@840: {weighted_precision_at_840:.4f}, R@840: {weighted_recall_at_840:.4f}")
    logger.info(f"简单集成 - AUC: {simple_auc:.4f}, PR-AUC: {simple_pr_auc:.4f}")
    logger.info(f"简单集成 - P@840: {simple_precision_at_840:.4f}, R@840: {simple_recall_at_840:.4f}")
    
    # 与历史最佳对比
    historical_best_auc = 0.9146
    historical_best_pr_auc = 0.5619
    historical_best_recall = 0.9747
    
    auc_vs_historical = weighted_auc - historical_best_auc
    pr_auc_vs_historical = weighted_pr_auc - historical_best_pr_auc
    recall_vs_historical = weighted_recall_at_840 - historical_best_recall
    
    logger.info(f"相比历史最佳: AUC {auc_vs_historical:+.4f}, PR-AUC {pr_auc_vs_historical:+.4f}, Recall@840 {recall_vs_historical:+.4f}")
    
    # 打印权重信息
    logger.info("=== 模型权重信息 ===")
    for i, (model_name, _) in enumerate(trained_models):
        logger.info(f"{model_name}: 权重={weights[i]:.3f}, PR-AUC={pr_aucs[i]:.4f}")
    
    return {
        'optimization': 'historical_best_enhanced',
        'weighted_ensemble': {
            'auc': float(weighted_auc),
            'pr_auc': float(weighted_pr_auc),
            'precision_at_840': float(weighted_precision_at_840),
            'recall_at_840': float(weighted_recall_at_840)
        },
        'simple_ensemble': {
            'auc': float(simple_auc),
            'pr_auc': float(simple_pr_auc),
            'precision_at_840': float(simple_precision_at_840),
            'recall_at_840': float(simple_recall_at_840)
        },
        'model_count': len(trained_models),
        'model_weights': weights.tolist(),
        'individual_pr_aucs': pr_aucs.tolist(),
        'historical_best': {
            'auc': historical_best_auc,
            'pr_auc': historical_best_pr_auc,
            'recall_at_840': historical_best_recall
        },
        'vs_historical': {
            'auc': float(auc_vs_historical),
            'pr_auc': float(pr_auc_vs_historical),
            'recall_at_840': float(recall_vs_historical)
        },
        'effective': bool(weighted_pr_auc > 0.1 and weighted_recall_at_840 > 0.5)
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_historical_best_enhanced"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化25完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"加权集成AUC: {result['weighted_ensemble']['auc']:.4f}")
    print(f"加权集成PR-AUC: {result['weighted_ensemble']['pr_auc']:.4f}")
    print(f"加权集成P@840: {result['weighted_ensemble']['precision_at_840']:.4f}")
    print(f"加权集成R@840: {result['weighted_ensemble']['recall_at_840']:.4f}")
    print(f"模型数: {result['model_count']}")
    print(f"相比历史最佳AUC: {result['vs_historical']['auc']:+.4f}")
    print(f"相比历史最佳PR-AUC: {result['vs_historical']['pr_auc']:+.4f}")
    print(f"相比历史最佳Recall@840: {result['vs_historical']['recall_at_840']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
