# 早期EPMMOENet模型成功经验详细记录

## 🏗️ **架构设计经验**

### 1. **多模态输入处理**
```python
# 三种输入类型的专门处理
InputGeneral_features    # 通用数值特征 (340+个)
InputSeqSet_features     # 序列特征 (用户行为序列)
InputScene_features      # 场景特征 (意向阶段、用户身份)
```

**关键经验**:
- **分离处理**: 不同类型特征用不同的处理路径
- **专门优化**: 每种特征类型都有专门的嵌入和处理层
- **维度跟踪**: 精确跟踪每种特征的维度用于后续拼接

### 2. **特征嵌入策略**
```python
# 四种特征类型的嵌入方式
StringLookup: StringLookup + Embedding     # 类别特征
Bucket: Discretization + Embedding         # 分桶数值特征  
Dense: Dense(512) + BN + Dropout + Dense   # 高维数值特征
Embedding: 直接传递                        # 预训练嵌入
```

**关键经验**:
- **类别特征**: 自动生成词汇表，低频词汇聚合为"LowFreq"
- **分桶特征**: 使用bin_boundaries进行量化，增强非线性表达
- **高维数值**: 先降维再嵌入，防止维度灾难
- **正则化**: 所有嵌入层都使用L2正则化和Dropout

### 3. **序列特征建模**
```python
# GRU + 时间衰减注意力机制
GRU(gru_dimension, return_sequences=True)
TimeSeriesAttention(attention_dim, time_decay_factor)
```

**关键经验**:
- **GRU选择**: 比LSTM更轻量，适合转化率预测的序列长度
- **时间衰减**: 近期行为权重更高，符合用户行为规律
- **注意力机制**: 自适应选择重要的行为序列片段
- **掩码支持**: 处理变长序列的填充问题

### 4. **特征交互增强**
```python
# Cross Layer实现显式特征交互
CrossLayer(64, l2_reg=0.01)
embedding_cross = self.cross_layer(embedding_all)
embedding_final = Concatenate([embedding_all, embedding_cross])
```

**关键经验**:
- **显式交互**: 不依赖深度网络的隐式交互
- **残差连接**: 保留原始特征的同时增加交互特征
- **维度控制**: 交互层维度适中，避免过拟合

### 5. **场景自适应权重**
```python
# 根据场景特征动态调整特征权重
Scene_layers = Sequential([BN, Dropout, Dense(256), BN, Dropout, Dense(feature_dim, 'sigmoid')])
embedding_all = embedding_all * scene_weight
```

**关键经验**:
- **动态权重**: 不同场景下特征重要性不同
- **Sigmoid激活**: 确保权重在[0,1]范围内
- **场景特征**: 意向阶段、用户身份等业务特征

## 📊 **数据处理经验**

### 1. **特征工程策略**
```python
# 340+个精选特征的构成
- 数值特征: 多时间窗口(1d,7d,30d,60d,90d,180d)行为计数
- 类别特征: 用户画像(性别、年龄、城市、职业、婚姻状况)
- 序列特征: 用户行为序列(action_code_seq, action_day_seq)
- 场景特征: 意向阶段、产品类型、是否walk-in等
- DSLA特征: Days Since Last Action时间衰减
```

**关键经验**:
- **时间窗口**: 多个时间窗口捕捉不同时间尺度的行为模式
- **业务特征**: 深度融入业务理解的特征设计
- **序列建模**: 真实的用户行为序列，不是模拟数据
- **时间衰减**: DSLA特征捕捉行为的时间衰减效应

### 2. **数据预处理流程**
```python
# 特征预处理的关键步骤
1. 特征填充: 根据feature_padding_dict配置
2. 类别特征: 自动生成词汇表，低频聚合
3. 分桶特征: 使用bin_boundaries量化
4. 序列特征: 变长序列填充和截断
5. 标签处理: 多时间窗口标签生成
```

**关键经验**:
- **自动词汇表**: 仅使用训练数据生成，避免数据泄露
- **低频处理**: 频率<1/10000的类别聚合为"LowFreq"
- **序列处理**: 支持变长序列的填充和截断
- **多任务标签**: 同时预测多个时间窗口的转化

### 3. **标签工程**
```python
# 多时间窗口标签生成
m_array = [0, 0, 0, 0, 0, 0]  # 6个月的转化标签
d_array = [0] * 180           # 180天的转化标签
# 累积标签用于训练
m_purchase_days_nio_new_car_consum = cumsum(m_array)
```

**关键经验**:
- **多时间窗口**: 同时预测1-6个月的转化概率
- **累积标签**: 用于训练的累积转化标签
- **时间对齐**: 标签时间窗口与特征时间窗口对齐

## 🎯 **训练策略经验**

### 1. **模型配置**
```python
# EPMMOENet关键参数
default_embedding_dimension=8      # 嵌入维度适中
default_gru_dimension=32          # GRU维度平衡
expert_num=8                      # 专家数量
use_cross_layer=True              # 启用特征交互
use_time_attention=True           # 启用时间注意力
time_decay_factor=0.05            # 时间衰减因子
```

**关键经验**:
- **维度平衡**: 嵌入维度不宜过大，避免过拟合
- **专家数量**: 8个专家是经验最优值
- **时间衰减**: 0.05是转化率预测的最优衰减因子
- **正则化**: 全面使用L2正则化和Dropout

### 2. **训练技巧**
```python
# 训练优化技巧
- 混合精度训练: mixed_float16策略
- 批量大小: 4096 (大批量稳定训练)
- 学习率: Adam默认学习率
- 正则化: L2=0.01, Dropout=0.2-0.3
- 早停: 监控验证损失
```

**关键经验**:
- **混合精度**: 显著加速训练，节省显存
- **大批量**: 4096批量大小提供稳定的梯度估计
- **适度正则**: 防止过拟合但不过度限制模型容量
- **早停策略**: 避免过拟合，保持最佳权重

### 3. **评估策略**
```python
# 多维度评估指标
- ROC-AUC: 整体分类性能
- PR-AUC: 不平衡数据下的性能
- Precision@840: 业务相关的精确率
- Recall@840: 业务相关的召回率
- 按月评估: Month_1到Month_6分别评估
```

**关键经验**:
- **业务指标**: Precision@840和Recall@840直接对应业务需求
- **时间分解**: 按月评估揭示模型在不同时间窗口的性能
- **不平衡处理**: PR-AUC比ROC-AUC更适合极不平衡数据

## 💡 **成功关键因素总结**

### 1. **架构设计 (60%贡献)**
- **多模态处理**: 数值、类别、序列、场景四种特征类型
- **专门优化**: 每种特征类型都有专门的处理路径
- **特征交互**: Cross Layer显式建模特征交互
- **时间建模**: GRU + 时间衰减注意力机制
- **场景自适应**: 根据业务场景动态调整特征权重

### 2. **特征工程 (30%贡献)**
- **340+精选特征**: 经过业务专家精心设计
- **多时间窗口**: 1d,7d,30d,60d,90d,180d全覆盖
- **分桶处理**: bin_boundaries量化增强非线性表达
- **序列特征**: 真实用户行为序列建模
- **DSLA特征**: 时间衰减效应建模

### 3. **业务理解 (10%贡献)**
- **意向阶段**: intention_stage精确刻画用户状态
- **用户画像**: 人口统计学特征深度利用
- **产品差异**: 不同产品类型的差异化建模
- **场景特征**: walk-in、首次意向等业务场景

## 🎯 **可复用的设计模式**

### 1. **多模态特征处理模式**
```python
# 模式: 分离 -> 专门处理 -> 融合
for feature_type in [numerical, categorical, sequential, scene]:
    processed_features[feature_type] = process_by_type(features[feature_type])
final_features = adaptive_fusion(processed_features, scene_weights)
```

### 2. **时间建模模式**
```python
# 模式: 序列编码 -> 时间注意力 -> 衰减权重
sequence_encoded = GRU(sequences)
attention_weights = TimeAttention(sequence_encoded) * time_decay_weights
final_sequence = weighted_sum(sequence_encoded, attention_weights)
```

### 3. **特征交互模式**
```python
# 模式: 原始特征 + 交互特征 -> 残差连接
cross_features = CrossLayer(original_features)
enhanced_features = Concatenate([original_features, cross_features])
```

### 4. **场景自适应模式**
```python
# 模式: 场景特征 -> 权重网络 -> 动态加权
scene_weights = SceneNetwork(scene_features)  # sigmoid激活
adaptive_features = original_features * scene_weights
```

这些经验为我们后续的优化提供了明确的方向和具体的实现模式。
