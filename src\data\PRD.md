## 配置文件和数据源分析

成功修改了配置文件路径并运行了数据分析脚本，确认配置文件是从`src/configs`目录直接读取的，而不是嵌套目录。

## 代码功能分析

1.  **`dict_parse.py` (原 `data_dict.py`)**：
    *   数据字典生成工具，创建详细的特征数据字典。
    *   加载原始数据 (`.parquet`) 和原始字典定义 (`.json`)。
    *   计算详细的特征统计信息，包括对不同类型的无效/空值（NaN, None, "None"字符串, 空字符串）的区分和汇总统计（总无效率）。
    *   推断特征业务领域和特征组（优先使用原始字典定义）。
    *   输出数据字典为 JSON, Markdown, 和 HTML 格式。
    *   HTML 报告包含详细的列含义解释。

2.  **`dict_analyze.py` (原 `simple_dict_analyzer.py`)**：
    *   数据字典分析工具，读取 `dict_parse.py` 生成的 JSON 数据字典。
    *   生成数据字典的统计报告，分析特征类型分布、缺失值、零值率等问题。
    *   识别潜在的有问题特征（如高缺失率、低方差等）。
    *   输出分析结果到 CSV 文件。

3.  **`data_analyze.py`**：
    *   主要数据分析脚本，负责对原始数据进行更深入的探索性分析 (EDA)。
    *   包含多个分析函数：基本统计、特征类型分析、序列特征分析、类别特征分析、数值特征分析、标签分析、特征相关性分析、特征重要性预估。
    *   生成综合数据分析报告 (Markdown)。

4.  **`data_reorganize.py`**：
    *   数据重组织工具，将数据划分为训练集/验证集/测试集 (70%/15%/15%)。
    *   包含数据加载、类型修复和数据划分功能。

5.  **`preprocessor.py`**：
    *   数据预处理模块，用于特征和标签准备。
    *   负责处理不同类型特征的填充、转换、截断等操作。

6.  **`loader.py`**：
    *   数据加载模块，处理从存储加载数据的功能。
    *   支持加载多个日期分区和额外数据集。

7.  **`data_run.sh`**：
    *   统一运行脚本，按顺序执行 `dict_parse.py`, `dict_analyze.py`, `data_analyze.py`。
    *   将所有标准输出和错误输出重定向到 `data_run.log` 文件。

## 数据字典 (`data_dictionary.html`) 解读

通过运行 `dict_parse.py` 生成的 HTML 数据字典提供了对每个特征的详细统计视图。关键列解读如下：

*   **数据质量指标**:
    *   `缺失率 (Missing Rate)`: 标准 `NaN` 值的比例。
    *   `Actual None值率`: Python `None` 对象的比例。
    *   `String 'None'值率`: 字符串 "None" (忽略大小写) 的比例，指示了数据表示错误。
    *   `空值率`: 空字符串 (`""`) 的比例。
    *   `总无效率`: 上述四种无效/空值类型的总比例，是评估特征可用性的首要指标。
*   **基础统计 (基于可用数据)**:
    *   `唯一值数`: 排除无效/空值后，不同值的数量 (衡量基数)。
    *   `最常见值`: 排除无效/空值后，出现频率最高的值。
    *   `最小值`, `最大值`, `均值`: 仅对可解析为数值的特征计算，否则显示 "-"。

**关键发现与后续步骤**: 数据字典揭示了许多特征中存在大量的字符串 "None" 值（`String 'None'值率` 很高），这是首要的数据清洗任务。需要将这些字符串转换为标准的 `np.nan`。此外，`总无效率` 指出了许多特征几乎完全不可用（接近100%），应考虑移除。特征分组信息来源于原始 JSON 文件，如发现不符（例如 `answer_sales_call_duration_s_seq` 显示为 `比率特征` 而非 `序列特征`），应检查并修正源 `src/configs/dataset_nio_new_car_v15.json` 文件。

## 数据分析结果关键信息

从运行 `dict_analyze.py` 和 `data_analyze.py` 的结果 (`data_run.log` 及相关报告) 中提取的关键信息：

1.  **数据集概况**：
    *   训练集日期分区: ['20240430']，样本量: 56,464
    *   测试集日期分区: ['20240531']，样本量: 9,800 (需要确认是否应为单独的测试集)
    *   评估集: 212,611 样本（占比过大，可能包含多月份数据或抽样问题）
    *   特征总量: 511个 (从 `dict_parse.py` 日志确认)

2.  **特征类型分布**：
    *   数据字典显示了 String, Double 等类型，预处理器 (`preprocessor.py`) 中可能映射为 StringLookup(类别型) 和 Bucket(数值型)。

3.  **标签分析**：
    *   总体正样本率极低: 约 1.17% (基于评估集?)
    *   训练集 (20240430) 正样本率: 4.20%
    *   测试集 (20240531) 正样本率: 3.91%
    *   评估集正样本率: 0.24%（如评估集包含更早月份，此分布差异可能合理，但需确认评估集范围）
    *   月度正样本率随时间递减：第1个月1.29%，第6个月仅0.38%

4.  **数据问题**：
    *   **严重无效值问题**: 数据字典的 `总无效率` 显示，许多特征（尤其是 `fellow_follow_*` 系列）包含大量无效/空值（包括 "None" 字符串），部分特征接近或达到100%无效，应优先清理或移除。
    *   **数据集划分/采样问题**: 评估集占比 (76%) 显著大于训练/测试集，且标签分布差异巨大，需要审查数据划分逻辑或评估集定义。
    *   **数据严重不平衡**: 整体及各子集正样本比例均非常低。

5.  **特征相关性**：
    *   发现多个高相关性特征对（相关系数>0.7）。
    *   主要是时间窗口特征间的高相关性（如 *_cnt_7d 与 *_cnt_14d 等）。

6.  **优化建议 (基于当前理解)**：
    *   **数据清洗**: 优先处理 `String 'None'` 值，将其转换为 `np.nan`。移除 `总无效率` 极高（如 >95%）的特征。
    *   **数据集审查**: 明确训练/验证/测试/评估集的划分标准和时间范围，解决评估集占比过大和标签分布不一致的问题。
    *   **特征工程**: 减少高相关性特征（如保留最相关的或进行 PCA 降维）。考虑序列特征的更有效表示方法。针对低方差特征进行处理。
    *   **模型策略**: 采用处理不平衡数据的技术（如过采样、欠采样、代价敏感学习）。特别关注模型在远期月份（低正样本率）的性能。

总体来看，该项目旨在通过用户行为和属性特征预测 NIO 新车购买倾向。当前阶段通过完善的数据字典生成流程，明确了数据质量方面的主要挑战（无效值表示、高无效率），以及数据集划分和标签不平衡等问题。下一步的核心是数据清洗、数据集重新定义/划分以及针对性的特征工程。
