#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化27: EPMMOENet + SMOTE简化版
基于成功的EPMMOENet，在数据层面应用SMOTE，然后使用原始训练流程
历史最优基线: Month_1 AUC 0.8906, PR-AUC 0.4808
目标: 在EPMMOENet基础上应用SMOTE，提升PR-AUC和Recall
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def apply_smote_to_dataset():
    """在数据集层面应用SMOTE增强"""
    logger.info("=== 在数据集层面应用SMOTE增强 ===")
    
    # 1. 加载原始数据集
    train_path = "data/dataset_nio_new_car_v15/datetime=20240430"
    test_path = "data/dataset_nio_new_car_v15/datetime=20240531"
    
    logger.info("加载训练数据...")
    df_train = pd.read_parquet(train_path)
    logger.info(f"原始训练集: {len(df_train)}条记录")
    
    logger.info("加载测试数据...")
    df_test = pd.read_parquet(test_path)
    logger.info(f"测试集: {len(df_test)}条记录")
    
    # 2. 准备SMOTE所需的数值特征
    # 选择数值特征用于SMOTE
    numeric_cols = [col for col in df_train.columns 
                   if df_train[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
    
    # 选择前200个数值特征
    selected_features = numeric_cols[:200]
    
    # 提取特征和标签
    X_train = df_train[selected_features].fillna(0).values
    
    # 处理标签
    if 'purchase_days_nio_new_car_total' in df_train.columns:
        y_train = (df_train['purchase_days_nio_new_car_total'].fillna(999) < 180).astype(int).values
    else:
        logger.error("未找到标签列")
        return None, None
    
    logger.info(f"特征维度: {X_train.shape[1]}")
    logger.info(f"原始正样本比例: {y_train.mean():.4f}")
    
    # 3. 应用SMOTE
    logger.info("应用SMOTE重采样...")
    smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    
    logger.info(f"SMOTE后: {len(X_train_smote)}条记录")
    logger.info(f"SMOTE后正样本比例: {y_train_smote.mean():.4f}")
    
    # 4. 重构增强后的训练数据集
    logger.info("重构增强后的训练数据集...")
    
    # 创建增强后的DataFrame
    df_train_enhanced = pd.DataFrame(X_train_smote, columns=selected_features)
    
    # 添加必要的列
    df_train_enhanced['user_id'] = range(len(df_train_enhanced))  # 生成新的user_id
    df_train_enhanced['datetime'] = '20240430'  # 保持原始日期
    df_train_enhanced['purchase_days_nio_new_car_total'] = np.where(
        y_train_smote == 1, 
        np.random.randint(0, 180, size=len(y_train_smote)),  # 正样本：0-179天
        np.random.randint(180, 999, size=len(y_train_smote))  # 负样本：180-999天
    )
    
    # 添加其他必要的列（用默认值填充）
    for col in df_train.columns:
        if col not in df_train_enhanced.columns:
            if df_train[col].dtype == 'object':
                df_train_enhanced[col] = df_train[col].iloc[0]  # 用第一个值填充
            else:
                df_train_enhanced[col] = 0  # 数值列用0填充
    
    # 5. 保存增强后的数据集
    enhanced_train_path = "data/dataset_nio_new_car_v15_enhanced/datetime=20240430"
    os.makedirs(os.path.dirname(enhanced_train_path), exist_ok=True)
    
    logger.info(f"保存增强后的训练集到: {enhanced_train_path}")
    df_train_enhanced.to_parquet(enhanced_train_path, index=False)
    
    # 6. 复制测试集到新位置
    enhanced_test_path = "data/dataset_nio_new_car_v15_enhanced/datetime=20240531"
    logger.info(f"复制测试集到: {enhanced_test_path}")
    df_test.to_parquet(enhanced_test_path, index=False)
    
    return enhanced_train_path, enhanced_test_path

def create_enhanced_dataset_config():
    """创建增强数据集的配置文件"""
    logger.info("创建增强数据集配置文件...")
    
    # 复制原始配置
    original_config_path = "src/configs/datasets/dataset_nio_new_car_v15.json"
    enhanced_config_path = "src/configs/datasets/dataset_nio_new_car_v15_enhanced.json"
    
    with open(original_config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 修改数据集路径
    config['dataset_path'] = "data/dataset_nio_new_car_v15_enhanced"
    
    # 保存增强配置
    with open(enhanced_config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"增强数据集配置保存到: {enhanced_config_path}")
    return enhanced_config_path

def train_epmmoe_with_enhanced_data():
    """使用增强数据训练EPMMOENet"""
    logger.info("=== 使用增强数据训练EPMMOENet ===")
    
    # 1. 应用SMOTE增强数据集
    enhanced_train_path, enhanced_test_path = apply_smote_to_dataset()
    if enhanced_train_path is None:
        logger.error("SMOTE增强失败")
        return None
    
    # 2. 创建增强数据集配置
    enhanced_config_path = create_enhanced_dataset_config()
    
    # 3. 使用原始训练脚本训练EPMMOENet
    logger.info("使用原始训练脚本训练EPMMOENet...")
    
    # 修改训练命令以使用增强数据集
    train_command = f"""
    cd src && python train.py \\
        --run_name epmmoe_smote_enhanced \\
        --epochs 15 \\
        --data_dir ../data \\
        --dataset_config configs/datasets/dataset_nio_new_car_v15_enhanced.json
    """
    
    logger.info(f"执行训练命令: {train_command}")
    
    # 执行训练
    import subprocess
    try:
        result = subprocess.run(
            train_command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=os.getcwd(),
            timeout=3600  # 1小时超时
        )
        
        logger.info("训练完成")
        logger.info(f"训练输出: {result.stdout}")
        
        if result.stderr:
            logger.warning(f"训练警告: {result.stderr}")
        
        return result.stdout
        
    except subprocess.TimeoutExpired:
        logger.error("训练超时")
        return None
    except Exception as e:
        logger.error(f"训练失败: {e}")
        return None

def extract_results_from_output(output):
    """从训练输出中提取结果"""
    if not output:
        return None
    
    results = {}
    lines = output.split('\n')
    
    for line in lines:
        if 'Month_1' in line and 'ROC-AUC' in line:
            # 提取Month_1的AUC
            parts = line.split()
            for i, part in enumerate(parts):
                if part == 'ROC-AUC' and i + 2 < len(parts):
                    try:
                        results['month_1_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
                if part == 'PR-AUC' and i + 2 < len(parts):
                    try:
                        results['month_1_pr_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
        
        if 'Overall' in line and 'ROC-AUC' in line:
            # 提取Overall的AUC
            parts = line.split()
            for i, part in enumerate(parts):
                if part == 'ROC-AUC' and i + 2 < len(parts):
                    try:
                        results['overall_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
                if part == 'PR-AUC' and i + 2 < len(parts):
                    try:
                        results['overall_pr_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
    
    return results

def main():
    """主函数"""
    logger.info("=== 开始EPMMOENet + SMOTE优化 ===")
    
    # 训练增强模型
    output = train_epmmoe_with_enhanced_data()
    
    if output:
        # 提取结果
        results = extract_results_from_output(output)
        
        if results:
            logger.info("=== EPMMOENet + SMOTE结果 ===")
            
            # 与基线对比
            baseline_month1_auc = 0.8906
            baseline_month1_pr_auc = 0.4808
            baseline_overall_auc = 0.8104
            baseline_overall_pr_auc = 0.0099
            
            month1_auc = results.get('month_1_auc', 0)
            month1_pr_auc = results.get('month_1_pr_auc', 0)
            overall_auc = results.get('overall_auc', 0)
            overall_pr_auc = results.get('overall_pr_auc', 0)
            
            logger.info(f"Month_1 AUC: {month1_auc:.4f} (基线: {baseline_month1_auc:.4f}, 差异: {month1_auc - baseline_month1_auc:+.4f})")
            logger.info(f"Month_1 PR-AUC: {month1_pr_auc:.4f} (基线: {baseline_month1_pr_auc:.4f}, 差异: {month1_pr_auc - baseline_month1_pr_auc:+.4f})")
            logger.info(f"Overall AUC: {overall_auc:.4f} (基线: {baseline_overall_auc:.4f}, 差异: {overall_auc - baseline_overall_auc:+.4f})")
            logger.info(f"Overall PR-AUC: {overall_pr_auc:.4f} (基线: {baseline_overall_pr_auc:.4f}, 差异: {overall_pr_auc - baseline_overall_pr_auc:+.4f})")
            
            # 保存结果
            final_results = {
                'optimization': 'epmmoe_smote_simple',
                'month_1': {
                    'auc': month1_auc,
                    'pr_auc': month1_pr_auc
                },
                'overall': {
                    'auc': overall_auc,
                    'pr_auc': overall_pr_auc
                },
                'baseline': {
                    'month_1_auc': baseline_month1_auc,
                    'month_1_pr_auc': baseline_month1_pr_auc,
                    'overall_auc': baseline_overall_auc,
                    'overall_pr_auc': baseline_overall_pr_auc
                },
                'improvements': {
                    'month_1_auc': month1_auc - baseline_month1_auc,
                    'month_1_pr_auc': month1_pr_auc - baseline_month1_pr_auc,
                    'overall_auc': overall_auc - baseline_overall_auc,
                    'overall_pr_auc': overall_pr_auc - baseline_overall_pr_auc
                },
                'effective': bool(month1_pr_auc > baseline_month1_pr_auc or overall_pr_auc > baseline_overall_pr_auc)
            }
            
            # 创建实验目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            exp_dir = f"logs/{timestamp}_epmmoe_smote_simple"
            os.makedirs(exp_dir, exist_ok=True)
            
            # 保存结果
            with open(f'{exp_dir}/results.json', 'w') as f:
                json.dump(final_results, f, indent=2)
            
            # 保存训练输出
            with open(f'{exp_dir}/training_output.txt', 'w') as f:
                f.write(output)
            
            print(f"\n优化27完成: {'✅ 有效' if final_results['effective'] else '❌ 无效'}")
            print(f"Month_1 AUC: {month1_auc:.4f} (vs基线: {month1_auc - baseline_month1_auc:+.4f})")
            print(f"Month_1 PR-AUC: {month1_pr_auc:.4f} (vs基线: {month1_pr_auc - baseline_month1_pr_auc:+.4f})")
            print(f"Overall AUC: {overall_auc:.4f} (vs基线: {overall_auc - baseline_overall_auc:+.4f})")
            print(f"Overall PR-AUC: {overall_pr_auc:.4f} (vs基线: {overall_pr_auc - baseline_overall_pr_auc:+.4f})")
            print(f"结果保存到: {exp_dir}/results.json")
            
            return final_results
        else:
            logger.error("无法从训练输出中提取结果")
            return None
    else:
        logger.error("训练失败")
        return None

if __name__ == "__main__":
    main()
