#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化24: 基于真实基线的优化
真实基线: AUC 0.7948 (可复现)
目标: 在真实基线基础上提升PR-AUC和Recall
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def create_optimized_model_v1(input_dim):
    """优化模型版本1 - 基于真实基线的改进"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_v2(input_dim):
    """优化模型版本2 - 更宽的网络"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(512, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(256, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_v3(input_dim):
    """优化模型版本3 - 平衡网络"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_v4(input_dim):
    """优化模型版本4 - 专门为PR-AUC优化"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(384, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.35),
        
        tf.keras.layers.Dense(192, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(96, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.25),
        
        tf.keras.layers.Dense(48, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_v5(input_dim):
    """优化模型版本5 - 深度网络"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(256, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def train_and_evaluate():
    """训练和评估基于真实基线的优化模型"""
    # 1. 加载数据 - 与真实基线完全相同
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 特征准备 - 与真实基线完全相同
    X, y, feature_names = data_loader.load_and_prepare(feature_count=150)
    
    # 3. 数据分割 - 与真实基线完全相同
    X_train, X_test, y_train, y_test = data_loader.split_data(
        X, y, test_size=0.2, random_state=42
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 4. SMOTE重采样 - 与真实基线完全相同
    logger.info("应用SMOTE重采样...")
    smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    logger.info(f"SMOTE重采样: {len(X_train)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
    
    # 5. 训练参数 - 与真实基线相同
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5)
    ]
    
    # 6. 训练多个优化模型
    logger.info("=== 训练优化模型集成 ===")
    
    models = []
    predictions = []
    model_creators = [
        create_optimized_model_v1, 
        create_optimized_model_v2, 
        create_optimized_model_v3,
        create_optimized_model_v4,
        create_optimized_model_v5
    ]
    
    for i, create_model in enumerate(model_creators, 1):
        logger.info(f"=== 训练优化模型{i} ===")
        model = create_model(X_train_smote.shape[1])
        
        if i == 1:
            model.summary()
        
        model.fit(
            X_train_smote, y_train_smote,
            validation_data=(X_test, y_test),
            epochs=12, batch_size=512,
            callbacks=callbacks,
            verbose=0
        )
        
        pred = model.predict(X_test, verbose=0)
        predictions.append(pred)
        models.append(model)
        
        # 单模型评估
        auc_score = roc_auc_score(y_test, pred)
        pr_auc = calculate_pr_auc(y_test, pred)
        precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
        
        logger.info(f"优化模型{i} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
        logger.info(f"优化模型{i} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
    
    # 7. 集成预测 - 简单平均
    ensemble_pred = np.mean(predictions, axis=0)
    
    # 8. 详细评估
    ensemble_auc = roc_auc_score(y_test, ensemble_pred)
    ensemble_pr_auc = calculate_pr_auc(y_test, ensemble_pred)
    ensemble_precision_at_840, ensemble_recall_at_840 = calculate_precision_recall_at_k(y_test, ensemble_pred, k=840)
    
    logger.info(f"=== 基于真实基线的优化结果 ===")
    logger.info(f"集成AUC: {ensemble_auc:.4f}")
    logger.info(f"集成PR-AUC: {ensemble_pr_auc:.4f}")
    logger.info(f"集成P@840: {ensemble_precision_at_840:.4f}")
    logger.info(f"集成R@840: {ensemble_recall_at_840:.4f}")
    
    # 与真实基线对比
    real_baseline_auc = 0.7948
    
    auc_vs_baseline = ensemble_auc - real_baseline_auc
    
    logger.info(f"相比真实基线: AUC {auc_vs_baseline:+.4f}")
    
    return {
        'optimization': 'real_baseline_optimization',
        'ensemble_auc': float(ensemble_auc),
        'ensemble_pr_auc': float(ensemble_pr_auc),
        'precision_at_840': float(ensemble_precision_at_840),
        'recall_at_840': float(ensemble_recall_at_840),
        'feature_count': len(feature_names),
        'model_count': len(models),
        'real_baseline_auc': real_baseline_auc,
        'auc_vs_baseline': float(auc_vs_baseline),
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in predictions],
        'individual_pr_aucs': [float(calculate_pr_auc(y_test, pred)) for pred in predictions],
        'individual_recalls': [float(calculate_precision_recall_at_k(y_test, pred, k=840)[1]) for pred in predictions],
        'effective': bool(auc_vs_baseline > 0.01 or ensemble_pr_auc > 0.05)
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_real_baseline_opt"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化24完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"集成AUC: {result['ensemble_auc']:.4f}")
    print(f"集成PR-AUC: {result['ensemble_pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: {result['feature_count']}")
    print(f"模型数: {result['model_count']}")
    print(f"相比真实基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"单模型AUC: {[f'{auc:.4f}' for auc in result['individual_aucs']]}")
    print(f"单模型PR-AUC: {[f'{pr_auc:.4f}' for pr_auc in result['individual_pr_aucs']]}")
    print(f"单模型Recall@840: {[f'{recall:.4f}' for recall in result['individual_recalls']]}")
    print(f"结果保存到: {exp_dir}/results.json")
