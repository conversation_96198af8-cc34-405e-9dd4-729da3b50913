# Conversion Rate Prediction Framework

A modular machine learning framework for predicting conversion rates in a structured and maintainable way.

## Project Structure

```
.
├── README.md                  # 项目说明文档
├── data/                      # 数据存储目录
│   ├── dataset_nio_new_car_v15/ # 原始数据集
│   │   ├── 20240531_随机采样1%.parquet  # 用于快速验证的样本数据
│   │   ├── datetime=20240430/      # 4月数据分区
│   │   └── datetime=20240531/      # 5月数据分区
│   │
│   ├── processed/             # 预处理后的数据集
│   │   ├── train.parquet      # 训练集
│   │   ├── validation.parquet # 验证集
│   │   ├── test.parquet       # 测试集
│   │   └── dataset_info.json  # 数据集信息
│   │
│   ├── configs/               # 数据集相关配置
│   │   └── datasets/          # 数据集特征配置
│   │       └── dataset_nio_new_car_v15.json  # 特征定义配置文件
│   │
│   └── dict/                  # 生成的数据字典存放位置
│       ├── json/              # JSON格式数据字典
│       └── markdown/          # Markdown格式数据字典
│
├── scripts/                   # 脚本目录
│   ├── evaluation/            # 评估脚本
│   │   └── compare_models.py  # 模型比较脚本
│   └── optimization/          # 优化脚本
│       ├── run_optimization.sh # 优化运行脚本
│       └── run_smote_enn.sh   # SMOTE-ENN优化脚本
│
├── logs/                      # 日志目录
│   └── error_20250409.log     # 错误日志
│
├── src/                       # 源代码目录
│   ├── configs/               # 配置文件目录
│   │   └── models/            # 模型配置文件
│   │       └── sample_20250311_v7-20250311.json
│   │
│   ├── data/                  # 数据处理相关模块
│   │   ├── __init__.py
│   │   ├── loader.py          # 数据加载器
│   │   ├── preprocessor.py    # 数据预处理器
│   │   ├── data_dict.py       # 数据字典生成工具
│   │   └── data_reorganize.py # 数据集重组织工具
│   │
│   ├── features/              # 特征工程相关模块
│   │   ├── __init__.py
│   │   ├── builder.py         # 特征构建器
│   │   └── imbalance/         # 不平衡学习模块 
│   │       ├── __init__.py    # 模块初始化
│   │       ├── samplers.py    # SMOTE和SMOTE-ENN实现
│   │       ├── balanced_dataset.py # 平衡数据集工具
│   │       └── README.md      # 模块文档
│   │
│   ├── models/                # 模型相关模块
│   │   ├── __init__.py
│   │   ├── networks/          # 网络模型实现
│   │   │   ├── __init__.py
│   │   │   └── EPMMOENet.py   # 主要模型实现
│   │   └── layers/            # 自定义网络层
│   │       ├── __init__.py
│   │       └── layers.py      # 层实现
│   │
│   ├── training/              # 训练相关模块
│   │   ├── __init__.py
│   │   ├── trainer.py         # 模型训练器
│   │   └── losses.py          # 损失函数实现
│   │
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   ├── config_utils.py    # 配置管理器
│   │   └── migration_helper.py # 目录结构迁移助手
│   │
│   ├── evaluation/            # 评估相关模块和结果
│   │   ├── __init__.py
│   │   ├── evaluator.py       # 模型评估器
│   │   ├── comparisons/       # 模型比较结果
│   │   ├── 20250317_v1/       # 评估结果目录
│   │   └── 20250317_v2/       # 评估结果目录
│   │
│   └── train.py               # 主训练脚本
│
└── report/                    # 报告目录
```

## Usage

### Training a Model

To train a model, use the `train.py` script:

```bash
python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=20250320_v1 --dataset_code=dataset_nio_new_car_v15

python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=20250320_v2 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=10

运行模型训练
python src/train.py --model_code=sample_20250311_v7-20250311 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=10

MTL训练
python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=20250330_multitask_v9 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=5 --use_multitask --batch_size=4096

使用SMOTE-ENN处理不平衡数据
python src/train.py --model_code=sample_20250311_v7-20250311 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=5 --resampling_strategy=optimized_smote_enn --smote_k=3 --enn_k=3 --smote_ratio=0.5
```

#### Arguments:

- `--model_code`: Model configuration file name (without .json extension)
- `--run_name`: Name for this training run
- `--dataset_code`: Dataset code/location
- `--evaluate_file`: Evaluation dataset filename
- `--epochs`: Maximum training epochs (default: 150)
- `--batch_size`: Training batch size (default: 8192)
- `--patience`: Early stopping patience (default: 10)
- `--use_cross_layer`: Whether to use feature cross layer (default: True)
- `--use_time_attention`: Whether to use time attention mechanism (default: True)
- `--log_file`: Custom log file path (optional)
- `--data_dir`: Root directory for data (default: current directory)
- `--output_dir`: Output directory for evaluation results (default: src/evaluation)

**Advanced options:**
- `--resampling_strategy`: Strategy for handling imbalanced data, options: [None, 'smote', 'smote_enn', 'optimized_smote_enn']
- `--smote_k`: Number of nearest neighbors for SMOTE (default: 5)
- `--enn_k`: Number of nearest neighbors for ENN (default: 3)
- `--smote_ratio`: Target ratio of positive samples relative to negative samples (default: 0.5)

### Configuration Files

The framework uses two types of configuration files:

1. **Model configuration** (e.g., `src/configs/models/sample_20250311_v7-20250311.json`): Defines model architecture, features, and training parameters.
2. **Dataset configuration** (e.g., `data/configs/dataset_nio_new_car_v15.json`): Defines dataset preprocessing parameters.

### Directory Organization

- **Raw Data**: Original datasets in `data/dataset_nio_new_car_v15/` directory
- **Processed Data**: Preprocessed train/validation/test sets in `data/processed/` directory
- **Dataset configurations**: Dataset feature configuration files in `data/configs/` directory
- **Model configurations**: Model configuration files in `src/configs/models/` directory
- **Evaluation results**: All evaluation results will be stored in the `src/evaluation/` directory, organized by run name
- **Data dictionaries**: Generated data dictionaries will be stored in `data/dict/` directory
- **Scripts**: Utility scripts for optimization and evaluation in `scripts/` directory
- **Logs**: Error and debug logs are stored in the `logs/` directory

### Preprocessing Data

To reorganize raw data into train/validation/test sets, use the `data_reorganize.py` script:

```bash
python src/data/data_reorganize.py --data_dir="data/dataset_nio_new_car_v15" --output_dir="data/processed" --train_date="20240430" --test_date="20240531" --format="parquet"
```

#### Arguments:

- `--data_dir`: Directory containing raw data partitions
- `--output_dir`: Output directory for preprocessed datasets
- `--train_date`: Training data partition date
- `--test_date`: Testing data partition date
- `--random_state`: Random seed for splitting (default: 42)
- `--format`: Output file format (parquet or csv)

### Generating Data Dictionary

To generate a data dictionary, use the `data_dict.py` script:

```bash
python src/data/data_dict.py --data_path="data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet" --original_dict_path="data/configs/dataset_nio_new_car_v15.json" --output_path="data/dict" --formats json markdown --visualize
```

#### Arguments:

- `--data_path`: Path to the dataset file (parquet format)
- `--original_dict_path`: Path to the original dictionary JSON file
- `--output_path`: Output directory for generated data dictionaries
- `--formats`: Output formats (json, markdown, html)
- `--visualize`: Whether to generate visualizations
- `--model_config_path`: Path to the model configuration file (optional)

### Using Imbalanced Learning Module

For handling imbalanced datasets, the framework provides a specialized module `src.features.imbalance`:

```python
# Using SMOTE for oversampling
from src.features.imbalance import apply_smote
X_resampled, y_resampled = apply_smote(X_train, y_train, k_neighbors=5, random_state=42)

# Using SMOTE-ENN for combined over/undersampling
from src.features.imbalance import apply_smote_enn
X_resampled, y_resampled = apply_smote_enn(X_train, y_train, k_neighbors=5, enn_k=3, random_state=42)

# Using Optimized SMOTE-ENN with controlled positive ratio
from src.features.imbalance import apply_optimized_smote_enn
X_resampled, y_resampled = apply_optimized_smote_enn(
    X_train, y_train, 
    smote_ratio=0.5,
    k_neighbors=3, 
    enn_k=3, 
    random_state=42
)

# Creating balanced datasets
from src.features.imbalance import create_balanced_dataset
balanced_ds = create_balanced_dataset(original_dataset, pos_ratio=0.3, batch_size=4096)
```

### Running Optimization Scripts

To run optimization experiments:

```bash
# Run general optimization
bash scripts/optimization/run_optimization.sh

# Run SMOTE-ENN optimization
bash scripts/optimization/run_smote_enn.sh
```

### Comparing Model Results

To compare the performance of two model runs:

```bash
python scripts/evaluation/compare_models.py \
  --baseline_dir=src/evaluation/baseline_run/evaluation_YYYYMMDD_HHMMSS \
  --improved_dir=src/evaluation/improved_run/evaluation_YYYYMMDD_HHMMSS \
  --output_dir=src/evaluation/comparisons/comparison_results
```

## Model Features

- Multimodal architecture with time series attention
- Feature crossing for enhanced feature interactions
- Time decay attention for sequential features
- Mixed precision training for better performance
- Early stopping and automatic evaluation
- Imbalanced learning techniques (SMOTE, SMOTE-ENN)

## Contributing

To contribute to this framework:

1. Add new model implementations in `src/models/networks/`
2. Add new layer implementations in `src/models/layers/`
3. Extend evaluation metrics in `src/evaluation/`
4. Add new imbalanced learning methods in `src/features/imbalance/`

---

```
机器学习模型全流程
├── 1. 数据加载与准备
│   ├── DataLoader
│   │   ├── 加载训练集数据 (train_dates)
│   │   ├── 加载测试集数据 (test_dates)
│   │   ├── 加载评估集数据 (evaluate_file)
│   │   └── 支持外挂数据集 (extra_datasets)
│   └── 数据配置
│       ├── 特征列配置 (feature_padding_dict)
│       └── 日期分区配置
│
├── 2. 数据预处理
│   ├── DataPreprocessor
│   │   ├── 基本特征处理 (preprocess_features)
│   │   │   ├── 类型转换
│   │   │   ├── 缺失值填充
│   │   │   └── 异常值处理
│   │   ├── 模型特征处理 (preprocess_model_features)
│   │   │   ├── 非序列特征
│   │   │   │   ├── 类别特征 (StringLookup)
│   │   │   │   │   ├── 词表生成
│   │   │   │   │   └── 低频值合并
│   │   │   │   ├── 连续特征 (Bucket)
│   │   │   │   ├── 降维特征 (Dense)
│   │   │   │   └── 直接特征 (Embedding)
│   │   │   └── 序列特征 (VarLen)
│   │   │       ├── 序列填充
│   │   │       └── 序列截断
│   │   └── 标签处理 (process_purchase_labels)
│   │       ├── 转换购买标签
│   │       └── 生成累计标签
│   │
│   └── 数据集分割
│       ├── 训练集
│       └── 测试集
│
├── 3. 特征工程
│   ├── FeatureBuilder
│   │   ├── 特征转换
│   │   │   ├── 非序列特征转换
│   │   │   └── 序列特征转换
│   │   └── 数据集生成 (generate_dataset)
│   │       ├── 训练数据集
│   │       ├── 测试数据集
│   │       └── 推理特征字典
│   │
│   └── 不平衡学习 (imbalance)
│       ├── 重采样技术
│       │   ├── SMOTE
│       │   ├── SMOTE-ENN
│       │   └── 优化的SMOTE-ENN
│       └── 平衡数据集创建
│           ├── 静态平衡数据集
│           ├── 动态平衡数据集
│           └── 正样本过采样
│
├── 4. 模型架构
│   ├── EPMMOENet_Model
│   │   ├── 输入层
│   │   │   ├── 非序列特征嵌入
│   │   │   └── 序列特征嵌入
│   │   ├── 特征处理层
│   │   │   ├── 非序列特征分支
│   │   │   ├── 序列特征分支
│   │   │   │   ├── GRU层处理
│   │   │   │   └── 时间衰减注意力机制
│   │   │   └── 场景特征加权
│   │   ├── 特征交叉层 (可选)
│   │   └── 预测塔层
│   │       ├── BatchNormalization
│   │       ├── Dropout
│   │       └── Dense层级
│   │
│   └── 自定义层
│       ├── CrossLayer (特征交叉增强)
│       └── TimeSeriesAttention (时间衰减注意力)
│
├── 5. 模型训练
│   └── ModelTrainer
│       ├── 构建模型
│       ├── 配置损失函数
│       │   ├── 累计损失 (cumsum_loss)
│       │   └── 带掩码累计损失 (cumsum_mask_loss)
│       ├── 训练配置
│       │   ├── 批量大小 (batch_size)
│       │   ├── 训练轮数 (epochs)
│       │   ├── 早停策略 (early_stopping_patience)
│       │   └── 混合精度训练 (mixed_precision)
│       └── 模型保存
│
└── 6. 模型评估
    └── ModelEvaluator
        ├── 预测生成
        ├── 指标计算
        │   ├── ROC-AUC
        │   ├── PR-AUC
        │   ├── Precision@K
        │   └── Recall@K
        ├── 评估维度
        │   ├── 整体评估 (calculate_overall_metrics)
        │   └── 按月评估 (calculate_monthly_metrics)
        └── 结果存储与可视化
```


1. 模型架构分析：

   - EPMMOENet是一个多模态模型，处理三类输入：
     * 通用特征(InputGeneral)：用户行为计数、属性等
     * 序列特征(InputSeqSet)：用户行为序列
     * 场景特征(InputScene)：用户身份、意向阶段等
   - 主要组件：
     * 特征嵌入层(Embedding_layers)：处理不同类型特征
     * GRU层+时间衰减注意力(TimeSeriesAttention)：处理序列特征
     * 特征交叉层(CrossLayer)：增强特征交互
     * 场景加权网络(Scene_layers)：根据场景特征动态调整特征权重
     * 预测塔(Tower_layers)：最终预测层
2. 当前模型特点：

   - 支持混合精度训练
   - 可配置是否使用特征交叉和时间注意力
   - 使用BatchNormalization和Dropout防止过拟合
   - 输出层使用sigmoid激活函数

生成数据字典 python src/data/data_dict.py --data_path data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet --original_dict_path data/original_dict.json --output_path data/full_data_dictionary --visualize
python src/data/data_dict.py --data_path data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet --original_dict_path src/configs/datasets/dataset_nio_new_car_v15.json --output_path data/full_business_dictionary --visualize


运行ESMM模型训练
python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=esmm_debug_run_$(date +%Y%m%d_%H%M%S) --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=1 --patience=1 --batch_size=1024 --use_esmm

运行ESM2模型训练
 cd /Users/<USER>/Documents/Com/nio-eatv && python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=esm2_debug_run_$(date +%Y%m%d_%H%M%S) --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=1 --patience=1 --batch_size=1024 --use_esm2