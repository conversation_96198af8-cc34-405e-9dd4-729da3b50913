"""
Feature builder module for conversion rate prediction models.
"""
import numpy as np
import tensorflow as tf
import logging


class FeatureBuilder:
    """Feature builder class to convert dataframes into model inputs."""
    
    def __init__(self):
        """Initialize the feature builder."""
        self.logger = logging.getLogger(__name__)
    
    def generate_dataset(self, 
                        df_input, 
                        raw_feature,
                        label=None, 
                        batch_size=8192,
                        mask_label=None,
                        predict_method="6m"):
        """
        Generate TensorFlow dataset from dataframe.
        
        Args:
            df_input (pd.DataFrame): Input dataframe.
            raw_feature (dict): Raw feature definitions.
            label (str, optional): Label column name.
            batch_size (int): Batch size for training.
            mask_label (str, optional): Mask label column name.
            predict_method (str): Prediction method ("1m" or "6m").
            
        Returns:
            tf.data.Dataset or dict: Training dataset or feature dict for inference.
        """
        self.logger.info(f"Generating {'inference features' if label is None else 'training dataset'}")
        
        # Create feature dictionary
        feature_dict = {}
        for feature_name, feature_info in raw_feature.items():
            feature_type = feature_info.get("type", "table")
            feature_dtype = feature_info.get("dtype", "StringLookup")
            
            # Handle different types of features
            if feature_type == "VarLen" or feature_dtype in ["Dense", "Embedding"]:
                feature_dict[feature_name] = np.array(df_input[feature_name].tolist())
            else:
                feature_dict[feature_name] = np.array(df_input[feature_name])
        
        # For inference, return just the feature dictionary
        if label is None:
            return feature_dict
        
        # For training, create full dataset with labels
        else:
            # Convert labels to numpy array
            label_array = np.array(df_input[label].tolist()).astype(np.float32)
            
            # Create appropriate dataset based on mask presence and prediction method
            if mask_label is None or predict_method == "1m":
                # No mask label case
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, label_array))
            else:
                # With mask label case
                mask_array = np.array(df_input[mask_label].tolist()).astype(np.float32)
                ds = tf.data.Dataset.from_tensor_slices(
                    (feature_dict, np.concatenate([label_array, mask_array], axis=-1)))
            
            self.logger.info("Dataset creation complete")
            
            # Apply batching and prefetching for optimal performance
            ds = ds.batch(batch_size)
            ds = ds.prefetch(batch_size)
            
            return ds
    
    def split_train_test(self, df, test_dates):
        """
        Split dataset into training and test sets based on dates.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            test_dates (list): List of dates to use for testing.
            
        Returns:
            tuple: (Training dataframe, Testing dataframe)
        """
        self.logger.info(f"Splitting dataset using test dates: {test_dates}")
        
        # Shuffle training data
        df_shuffled = df.sample(frac=1) 
        
        # Split based on dates
        df_train = df_shuffled[~df_shuffled["datetime"].isin(test_dates)]
        df_test = df_shuffled[df_shuffled["datetime"].isin(test_dates)]
        
        # Sort training data by date for temporal consistency
        df_train = df_train.sort_values("datetime", ascending=True)
        
        self.logger.info(f"Split complete. Training set: {len(df_train)}, Test set: {len(df_test)}")
        
        return df_train, df_test 