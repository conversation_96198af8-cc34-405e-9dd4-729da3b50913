#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
蔚来转化率预测 - 最终优化模型
整合所有有效的优化技术
"""
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='蔚来转化率预测 - 最终优化模型')
    parser.add_argument('--run_name', type=str, default=f'final_optimized_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    parser.add_argument('--epochs', type=int, default=15, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=512, help='批次大小')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心值')
    parser.add_argument('--ensemble_size', type=int, default=3, help='集成模型数量')
    return parser.parse_args()

def load_and_prepare_data():
    """加载和准备数据"""
    logger.info("加载数据...")
    df = pd.read_parquet('data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet')
    logger.info(f"数据加载成功: {df.shape}")
    
    # 选择数值特征 - 使用更多特征
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime']]
    
    # 使用更多特征提升性能
    selected_features = numeric_cols[:150]  # 增加到150个特征
    logger.info(f"选择特征数: {len(selected_features)}")
    
    # 准备特征矩阵
    X = df[selected_features].fillna(0).values
    
    # 准备标签
    target_col = df['purchase_days_nio_new_car_total']
    target_numeric = pd.to_numeric(target_col, errors='coerce').fillna(0)
    y = (target_numeric > 0).astype(int).values
    
    logger.info(f"特征形状: {X.shape}, 正样本比例: {y.mean():.4f}")
    return X, y

def create_optimized_model_1(input_dim):
    """创建优化模型1 - 深网络架构"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_2(input_dim):
    """创建优化模型2 - 宽网络架构"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(512, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(256, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def create_optimized_model_3(input_dim):
    """创建优化模型3 - 平衡架构"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    return model

def train_final_model(args):
    """训练最终优化模型"""
    logger.info(f"开始训练最终优化模型: {args.run_name}")
    
    # 1. 加载数据
    X, y = load_and_prepare_data()
    
    # 2. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 3. 训练参数
    pos_weight = len(y_train) / (2 * np.sum(y_train)) if np.sum(y_train) > 0 else 1.0
    logger.info(f"正样本权重: {pos_weight:.2f}")
    
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=args.patience, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5),
        tf.keras.callbacks.ModelCheckpoint(f'{args.run_name}_best.keras', save_best_only=True)
    ]
    
    # 4. 训练集成模型
    models = []
    predictions = []
    model_creators = [create_optimized_model_1, create_optimized_model_2, create_optimized_model_3]
    
    for i, create_model in enumerate(model_creators[:args.ensemble_size], 1):
        logger.info(f"=== 训练优化模型{i} ===")
        model = create_model(X_train.shape[1])
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=args.epochs, batch_size=args.batch_size,
            callbacks=callbacks,
            class_weight={0: 1.0, 1: pos_weight},
            verbose=1
        )
        
        # 预测
        pred = model.predict(X_test, verbose=0)
        predictions.append(pred)
        models.append(model)
        
        # 单模型AUC
        auc = roc_auc_score(y_test, pred)
        logger.info(f"优化模型{i} AUC: {auc:.4f}")
    
    # 5. 集成预测
    ensemble_pred = np.mean(predictions, axis=0)
    ensemble_auc = roc_auc_score(y_test, ensemble_pred)
    ensemble_acc = accuracy_score(y_test, (ensemble_pred > 0.5).astype(int))
    
    logger.info(f"=== 最终集成模型结果 ===")
    logger.info(f"集成AUC: {ensemble_auc:.4f}")
    logger.info(f"集成准确率: {ensemble_acc:.4f}")
    
    # 详细评估报告
    y_pred = (ensemble_pred > 0.5).astype(int)
    print("\n=== 最终模型评估报告 ===")
    print(f"AUC: {ensemble_auc:.4f}")
    print(f"准确率: {ensemble_acc:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    # 6. 保存结果
    results = {
        'run_name': args.run_name,
        'final_auc': float(ensemble_auc),
        'final_accuracy': float(ensemble_acc),
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in predictions],
        'feature_count': X.shape[1],
        'train_samples': len(X_train),
        'test_samples': len(X_test),
        'positive_rate': float(y.mean()),
        'ensemble_size': args.ensemble_size,
        'optimization_techniques': [
            'deeper_network_architecture',
            'ensemble_learning',
            'more_features',
            'batch_normalization',
            'class_weight_balancing'
        ]
    }
    
    result_file = f'{args.run_name}_results.json'
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"最终结果保存到: {result_file}")
    
    return results

def main():
    """主函数"""
    args = parse_args()
    results = train_final_model(args)
    
    print(f"\n🏆 最终优化模型训练完成!")
    print(f"🎯 最终AUC: {results['final_auc']:.4f}")
    print(f"📊 准确率: {results['final_accuracy']:.4f}")
    print(f"🔧 使用的优化技术: {len(results['optimization_techniques'])}项")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
