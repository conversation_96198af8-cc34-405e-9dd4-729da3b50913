"""
Balanced dataset creation utilities for handling imbalanced data.

This module provides functions to create balanced datasets and oversample
positive samples for better model training on imbalanced datasets.
"""
import numpy as np
import tensorflow as tf
import logging

logger = logging.getLogger(__name__)


def create_balanced_dataset(dataset, pos_ratio=0.5, batch_size=4096, buffer_size=10000, seed=42):
    """
    Create a balanced dataset with controlled positive sample ratio per batch.
    
    Args:
        dataset: Original TensorFlow dataset
        pos_ratio: Proportion of positive samples in each batch (0.0 to 1.0)
        batch_size: Size of each batch
        buffer_size: Shuffle buffer size
        seed: Random seed for reproducibility
        
    Returns:
        tf.data.Dataset: Balanced dataset
    """
    # Cache dataset to memory to avoid repeated computation
    cached_dataset = dataset.cache()
    
    # Calculate number of positive and negative samples per batch
    positive_samples_per_batch = int(batch_size * pos_ratio)
    negative_samples_per_batch = batch_size - positive_samples_per_batch
    
    # Define batch generator function
    def balanced_batch_generator():
        # Collect all samples and separate into positive and negative
        all_samples = list(cached_dataset.as_numpy_iterator())
        
        # Separate samples based on labels
        positive_samples = []
        negative_samples = []
        
        for sample in all_samples:
            features, labels = sample
            # Check if labels contain any positive values
            if isinstance(labels, np.ndarray) and labels.size > 0:
                if np.any(labels > 0):
                    positive_samples.append(sample)
                else:
                    negative_samples.append(sample)
            else:
                # If labels aren't an array or empty, assume negative
                negative_samples.append(sample)
        
        # Create numpy random number generator
        rng = np.random.RandomState(seed)
        
        # Generate batches indefinitely
        while True:
            # Shuffle positive and negative samples
            rng.shuffle(positive_samples)
            rng.shuffle(negative_samples)
            
            # Select samples for this batch
            batch_positives = positive_samples[:positive_samples_per_batch]
            batch_negatives = negative_samples[:negative_samples_per_batch]
            
            # Handle case where we don't have enough samples
            if len(batch_positives) < positive_samples_per_batch:
                # Calculate how many cycles needed to reach required count
                cycles = (positive_samples_per_batch // len(positive_samples)) + 1
                batch_positives = (positive_samples * cycles)[:positive_samples_per_batch]
            
            if len(batch_negatives) < negative_samples_per_batch:
                cycles = (negative_samples_per_batch // len(negative_samples)) + 1
                batch_negatives = (negative_samples * cycles)[:negative_samples_per_batch]
            
            # Combine samples
            batch_samples = batch_positives + batch_negatives
            
            # Shuffle combined batch
            rng.shuffle(batch_samples)
            
            # Separate features and labels
            batch_features = {}
            batch_labels = []
            
            # Process samples in the batch
            for i, (feat, label) in enumerate(batch_samples):
                # Initialize feature dictionary if first sample
                if i == 0 and isinstance(feat, dict):
                    for key in feat.keys():
                        batch_features[key] = []
                
                # Add features and labels
                if isinstance(feat, dict):
                    for key, value in feat.items():
                        batch_features[key].append(value)
                else:
                    # If features aren't a dict, create single key
                    if i == 0:
                        batch_features = []
                    batch_features.append(feat)
                
                batch_labels.append(label)
            
            # Convert lists to numpy arrays
            if isinstance(batch_features, dict):
                for key in batch_features.keys():
                    # Check if all elements have same shape
                    shapes = [item.shape for item in batch_features[key]]
                    if all(shape == shapes[0] for shape in shapes):
                        batch_features[key] = np.stack(batch_features[key])
            else:
                batch_features = np.stack(batch_features)
                
            batch_labels = np.stack(batch_labels)
            
            yield batch_features, batch_labels
    
    # Create dataset with proper types and shapes
    output_types = tf.nest.map_structure(lambda spec: spec.dtype, dataset.element_spec)
    output_shapes = tf.nest.map_structure(lambda spec: spec.shape, dataset.element_spec)
    
    # Add batch dimension to output shapes
    def add_batch_dim(shape):
        return tf.TensorShape([None]).concatenate(shape[1:])
        
    output_shapes = tf.nest.map_structure(add_batch_dim, output_shapes)
    
    return tf.data.Dataset.from_generator(
        balanced_batch_generator,
        output_types=output_types,
        output_shapes=output_shapes
    )


def create_dynamic_balanced_dataset(dataset, pos_ratios, batch_size=4096, buffer_size=10000, seed=42):
    """
    Create multiple balanced datasets with different positive ratios
    for dynamic adjustment during training.
    
    Args:
        dataset: Original TensorFlow dataset
        pos_ratios: List of positive sample ratios for different training stages
        batch_size: Size of each batch
        buffer_size: Shuffle buffer size
        seed: Random seed for reproducibility
        
    Returns:
        list: List of balanced datasets that can be switched during training
    """
    balanced_datasets = []
    
    for i, pos_ratio in enumerate(pos_ratios):
        balanced_dataset = create_balanced_dataset(
            dataset, 
            pos_ratio=pos_ratio, 
            batch_size=batch_size, 
            buffer_size=buffer_size, 
            seed=seed+i
        )
        balanced_datasets.append(balanced_dataset)
    
    return balanced_datasets


def oversample_positive_samples(features, labels, pos_weight=5.0):
    """
    Oversample positive samples by copying them to balance the dataset.
    
    Args:
        features: Feature array
        labels: Label array
        pos_weight: Positive sample weight (how many times to duplicate)
        
    Returns:
        tuple: (oversampled_features, oversampled_labels)
    """
    # Determine which samples are positive based on labels
    if len(labels.shape) > 1 and labels.shape[1] >= 6:
        # Multi-dimensional labels case
        is_positive = np.any(labels[:, :6] > 0, axis=1)
    else:
        # One-dimensional labels case
        is_positive = labels > 0
    
    # Extract positive and negative samples
    pos_features = features[is_positive]
    pos_labels = labels[is_positive]
    
    neg_features = features[~is_positive]
    neg_labels = labels[~is_positive]
    
    # Calculate duplication factors
    repeat_times = int(pos_weight) - 1
    remainder = pos_weight - int(pos_weight)
    
    # Duplicate integer number of times
    repeated_pos_features = np.repeat(pos_features, repeat_times, axis=0)
    repeated_pos_labels = np.repeat(pos_labels, repeat_times, axis=0)
    
    # Handle fractional part of pos_weight
    extra_count = int(remainder * len(pos_features))
    if extra_count > 0:
        indices = np.random.choice(len(pos_features), extra_count, replace=False)
        extra_pos_features = pos_features[indices]
        extra_pos_labels = pos_labels[indices]
        
        # Merge extra samples
        repeated_pos_features = np.concatenate([repeated_pos_features, extra_pos_features], axis=0)
        repeated_pos_labels = np.concatenate([repeated_pos_labels, extra_pos_labels], axis=0)
    
    # Combine oversampled positives with original negatives
    oversampled_features = np.concatenate([pos_features, repeated_pos_features, neg_features], axis=0)
    oversampled_labels = np.concatenate([pos_labels, repeated_pos_labels, neg_labels], axis=0)
    
    # Shuffle samples
    indices = np.random.permutation(len(oversampled_features))
    oversampled_features = oversampled_features[indices]
    oversampled_labels = oversampled_labels[indices]
    
    return oversampled_features, oversampled_labels 