#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NIO用户embedding模型结果可视化脚本

此脚本用于生成丰富的可视化图表，分析embedding模型的性能和用户相似性。
"""

import os
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import logging
from datetime import datetime
import glob

# Global constants from other scripts if needed (e.g. for loading candidate data for labels)
USER_ID_COL = "user_id"
DEFAULT_LABEL_COL = "target_purchase_next_30d" # For coloring embeddings
DEFAULT_CANDIDATE_DATE = "20240531"
DEFAULT_DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")

# Settings
plt.style.use('seaborn-v0_8-whitegrid') # Using an available seaborn style
sns.set_palette("pastel")

# Setup logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler() # Log to console
    ]
)

def ensure_output_dir(output_dir_path: Path):
    output_dir_path.mkdir(parents=True, exist_ok=True)
    logger.info(f"Output directory ensured: {output_dir_path}")

# --- Data Loading Functions --- 
def load_embeddings_from_file(embeddings_path: Path):
    """Loads user_ids and embeddings from a .pkl file (dict format)."""
    if not embeddings_path.exists():
        logger.warning(f"Embeddings file not found: {embeddings_path}")
        return None, None
    try:
        with open(embeddings_path, 'rb') as f:
            data = pickle.load(f)
        if isinstance(data, dict) and 'user_ids' in data and 'embeddings' in data:
            user_ids = np.array(data['user_ids'])
            embeddings = np.array(data['embeddings'])
            logger.info(f"Loaded {len(user_ids)} embeddings from {embeddings_path}")
            return user_ids, embeddings.astype(np.float32)
        else:
            logger.error(f"Invalid format in embeddings file {embeddings_path}. Expected dict with 'user_ids' and 'embeddings'.")
            return None, None
    except Exception as e:
        logger.error(f"Error loading embeddings from {embeddings_path}: {e}")
        return None, None

def load_json_metrics(metrics_path: Path):
    """Loads metrics from a JSON file."""
    if not metrics_path.exists():
        logger.warning(f"Metrics file not found: {metrics_path}")
        return None
    try:
        with open(metrics_path, 'r') as f:
            metrics = json.load(f)
        logger.info(f"Loaded metrics from {metrics_path}")
        return metrics
    except Exception as e:
        logger.error(f"Error loading metrics from {metrics_path}: {e}")
        return None

# (Optional) Simplified candidate data loader for labels if PCA/t-SNE coloring is desired
def load_candidate_labels_for_viz(
    candidate_data_dir: Path,
    user_id_col: str, 
    label_col: str, 
    max_files: int = None
):
    data_path = candidate_data_dir
    if not data_path.exists():
        logger.warning(f"Candidate data path for labels not found: {data_path}")
        return pd.DataFrame()

    files_to_load = glob.glob(str(data_path / "**/*.parquet"), recursive=True)
    if not files_to_load:
        logger.warning(f"No Parquet files for labels found in {data_path}")
        return pd.DataFrame()
    if max_files: files_to_load = files_to_load[:max_files]
    
    df_list = []
    raw_label_col_for_processing = "m_purchase_days_nio_new_car"
    for f_path in files_to_load:
        try:
            cols_to_read = {user_id_col}
            if raw_label_col_for_processing != label_col and label_col == DEFAULT_LABEL_COL:
                 cols_to_read.add(raw_label_col_for_processing)
            else:
                 cols_to_read.add(label_col)
            df_part = pd.read_parquet(f_path, columns=list(cols_to_read))
            if label_col == DEFAULT_LABEL_COL and raw_label_col_for_processing in df_part.columns:
                def safe_parse_purchase_days(val):
                    if isinstance(val, list) and len(val) > 0 and val[0] == 1: return 1
                    if isinstance(val, str): 
                        try: parsed_val = json.loads(val)
                        except: return 0
                        if isinstance(parsed_val, list) and len(parsed_val) > 0 and parsed_val[0] == 1: return 1
                    return 0
                df_part[label_col] = df_part[raw_label_col_for_processing].apply(safe_parse_purchase_days)
            if user_id_col in df_part.columns and label_col in df_part.columns:
                df_list.append(df_part[[user_id_col, label_col]])
        except Exception as e:
            logger.error(f"Error loading label data from {f_path}: {e}")
    if not df_list: return pd.DataFrame()
    return pd.concat(df_list, ignore_index=True).drop_duplicates(subset=[user_id_col])

# --- Visualization Functions ---
def visualize_classification_metrics(metrics_data, output_dir: Path):
    """Visualizes classification metrics (ROC AUC, PR AUC, Precision@K, Recall@K)."""
    if not metrics_data: 
        logger.warning("No classification metrics data to visualize.")
        return

    logger.info("Generating classification metrics plots...")
    
    labels = []
    roc_aucs = []
    pr_aucs = []
    prec_at_k = []
    rec_at_k = []
    k_value_str = ""

    # Assuming metrics_data is the direct dict from evaluation_metrics.json
    if "ROC_AUC" in metrics_data: labels.append("ROC AUC"); roc_aucs.append(metrics_data["ROC_AUC"])
    if "PR_AUC" in metrics_data: labels.append("PR AUC"); pr_aucs.append(metrics_data["PR_AUC"])
    
    # Find Precision@K and Recall@K (assumes one K value is primary, e.g. Precision@840)
    for key, value in metrics_data.items():
        if "Precision@" in key and value is not None: labels.append(key); prec_at_k.append(value); k_value_str=key.split("@")[1]
        if "Recall@" in key and value is not None: labels.append(key); rec_at_k.append(value)

    all_metrics_for_bar = []
    all_labels_for_bar = []
    if roc_aucs: all_metrics_for_bar.extend(roc_aucs); all_labels_for_bar.append("ROC AUC")
    if pr_aucs: all_metrics_for_bar.extend(pr_aucs); all_labels_for_bar.append("PR AUC")
    if prec_at_k: all_metrics_for_bar.extend(prec_at_k); all_labels_for_bar.append(f"Precision@{k_value_str}")
    if rec_at_k: all_metrics_for_bar.extend(rec_at_k); all_labels_for_bar.append(f"Recall@{k_value_str}")

    if not all_metrics_for_bar:
        logger.warning("No plottable classification metrics found.")
        return

    plt.figure(figsize=(10, 6))
    bars = plt.bar(all_labels_for_bar, all_metrics_for_bar, color=sns.color_palette("viridis", len(all_labels_for_bar)))
    plt.ylabel('Score')
    plt.title('Classification Performance Metrics')
    plt.ylim(0, 1.05)
    for bar in bars:
        yval = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.01, f'{yval:.3f}', ha='center', va='bottom')
    
    plot_path = output_dir / "classification_metrics_summary.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved classification metrics plot to {plot_path}")

def visualize_retrieval_metrics_plot(retrieval_metrics_data, output_dir: Path):
    """Visualizes retrieval metrics (Mean Precision@K, Global Recall@K)."""
    if not retrieval_metrics_data or "precision_at_k" not in retrieval_metrics_data or "recall_at_k" not in retrieval_metrics_data:
        logger.warning("No valid retrieval metrics data to visualize.")
        return

    logger.info("Generating retrieval metrics plots...")
    
    prec_at_k = retrieval_metrics_data["precision_at_k"]
    rec_at_k = retrieval_metrics_data["recall_at_k"]

    k_values_p = sorted([int(k.split('@')[1]) for k in prec_at_k.keys()])
    p_scores = [prec_at_k[f"P@{k}"] for k in k_values_p]
    
    k_values_r = sorted([int(k.split('@')[1]) for k in rec_at_k.keys()])
    r_scores = [rec_at_k[f"R@{k}"] for k in k_values_r]

    if not k_values_p or not k_values_r:
        logger.warning("No K values found for retrieval plots.")
        return

    plt.figure(figsize=(14, 6))
    plt.subplot(1, 2, 1)
    plt.plot(k_values_p, p_scores, marker='o', linestyle='-', color='#1f77b4')
    plt.title('Mean Precision @ K (for Seed Users)')
    plt.xlabel('K (Number of Retrieved Users)')
    plt.ylabel('Mean Precision')
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.subplot(1, 2, 2)
    plt.plot(k_values_r, r_scores, marker='s', linestyle='--', color='#ff7f0e')
    plt.title('Global Recall @ K (Overall Coverage)')
    plt.xlabel('K (Number of Retrieved Users)')
    plt.ylabel('Global Recall')
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()
    plot_path = output_dir / "retrieval_metrics_curves.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved retrieval metrics plot to {plot_path}")

def visualize_embedding_distribution_plot(embeddings: np.ndarray, user_ids: np.ndarray, candidate_labels_df: pd.DataFrame, output_dir: Path, user_id_col: str, label_col: str, n_samples=2000):
    if embeddings is None or user_ids is None:
        logger.warning("Embeddings or user_ids are None. Skipping embedding distribution visualization.")
        return
    if embeddings.shape[0] == 0:
        logger.warning("Empty embeddings array. Skipping distribution visualization.")
        return

    logger.info(f"Generating embedding distribution plots for {min(n_samples, embeddings.shape[0])} samples...")

    actual_n_samples = min(n_samples, embeddings.shape[0])
    indices = np.random.choice(embeddings.shape[0], actual_n_samples, replace=False)
    sampled_embeddings = embeddings[indices]
    sampled_user_ids = user_ids[indices]

    labels_for_plot = np.zeros(actual_n_samples) # Default to 0
    if not candidate_labels_df.empty and label_col in candidate_labels_df.columns:
        labels_map = pd.Series(candidate_labels_df[label_col].values, index=candidate_labels_df[user_id_col]).to_dict()
        for i, uid in enumerate(sampled_user_ids):
            labels_for_plot[i] = labels_map.get(uid, 0) # Default to 0 if user not in labels_df or no label
        label_legend_title = f'{label_col} (1=Positive)'
    else:
        label_legend_title = 'User (no labels)'
        logger.info("No candidate labels provided for coloring embedding plots.")

    # PCA Visualization
    try:
        pca = PCA(n_components=2)
        embeddings_pca = pca.fit_transform(sampled_embeddings)
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(embeddings_pca[:, 0], embeddings_pca[:, 1], c=labels_for_plot, cmap='viridis', alpha=0.6, s=30)
        plt.colorbar(scatter, label=label_legend_title)
        plt.title(f'PCA of User Embeddings ({actual_n_samples} samples)')
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.grid(True, linestyle='--', alpha=0.3)
        pca_plot_path = output_dir / "embeddings_pca_distribution.png"
        plt.savefig(pca_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"Saved PCA plot to {pca_plot_path}")
    except Exception as e:
        logger.error(f"Error during PCA visualization: {e}")

    # t-SNE Visualization (can be slow for many samples)
    if actual_n_samples <= 2000: # Limit t-SNE to fewer samples for speed
        try:
            tsne = TSNE(n_components=2, perplexity=min(30, actual_n_samples -1 if actual_n_samples >1 else 1), n_iter=300, random_state=42, init='pca', learning_rate='auto')
            embeddings_tsne = tsne.fit_transform(sampled_embeddings)
            plt.figure(figsize=(10, 8))
            scatter_tsne = plt.scatter(embeddings_tsne[:, 0], embeddings_tsne[:, 1], c=labels_for_plot, cmap='viridis', alpha=0.6, s=30)
            plt.colorbar(scatter_tsne, label=label_legend_title)
            plt.title(f't-SNE of User Embeddings ({actual_n_samples} samples)')
            plt.xlabel('t-SNE Dimension 1')
            plt.ylabel('t-SNE Dimension 2')
            plt.grid(True, linestyle='--', alpha=0.3)
            tsne_plot_path = output_dir / "embeddings_tsne_distribution.png"
            plt.savefig(tsne_plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            logger.info(f"Saved t-SNE plot to {tsne_plot_path}")
        except Exception as e:
            logger.error(f"Error during t-SNE visualization: {e}")
    else:
        logger.info(f"Skipping t-SNE plot as sample size {actual_n_samples} > 2000.")


def visualize_retrieved_item_distances(retrieval_results_data, output_dir: Path):
    """Visualizes the distribution of distances of retrieved items."""
    if not retrieval_results_data:
        logger.warning("No retrieval results data for distance visualization.")
        return

    all_distances = []
    for seed_id, items in retrieval_results_data.items():
        for item_id, distance in items:
            all_distances.append(distance)
    
    if not all_distances:
        logger.info("No distances found in retrieval results.")
        return

    plt.figure(figsize=(10,6))
    sns.histplot(all_distances, kde=True, bins=50)
    plt.title('Distribution of Distances for Retrieved Similar Users')
    plt.xlabel('L2 Distance to Seed User')
    plt.ylabel('Frequency')
    dist_plot_path = output_dir / "retrieved_distances_distribution.png"
    plt.savefig(dist_plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Saved retrieved distances distribution to {dist_plot_path}")


# --- Main Orchestration ---
def main(args):
    model_output_dir = Path(args.model_output_dir)
    viz_output_dir = model_output_dir / "visualizations"
    ensure_output_dir(viz_output_dir)

    # Setup file-based logging for this script's run
    log_file_handler = logging.FileHandler(viz_output_dir / f"visualization_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    log_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(log_file_handler)
    
    logger.info(f"Starting visualization process. Model output dir: {model_output_dir}")

    # 1. Load Embeddings
    embeddings_file = model_output_dir / "user_embeddings.pkl" # Adjusted from user_embeddings.npz to user_embeddings.pkl based on previous log
    user_ids, embeddings = load_embeddings_from_file(embeddings_file)

    # 2. Load Classification Metrics (from embedding_model_train.py --mode predict)
    classification_metrics_file = model_output_dir / "evaluation_metrics.json"
    class_metrics = load_json_metrics(classification_metrics_file)
    if class_metrics:
        visualize_classification_metrics(class_metrics, viz_output_dir)
    
    # 3. Load Retrieval Metrics (from embedding_model_retrieval.py)
    retrieval_metrics_file = model_output_dir / "retrieval_evaluation_metrics.json" # Assuming it's in the *same* model_output_dir
                                                                               # Or it could be in a sub-folder like 'retrieval_results'
                                                                               # For now, assume it's sibling to evaluation_metrics.json
    # If the retrieval script saves its output into a *different* directory, this path needs to be an arg.
    # Let's assume for now it's relative to the main model_output_dir, perhaps in a subfolder.
    # If the user ran retrieval separately, they'd provide the path to its output dir or specific file.
    # We'll use the current structure where the retrieval script was run with `embedding/retrieval_test_output`
    # So, we need a specific path for this file.
    if args.retrieval_metrics_file:
        ret_metrics_file_path = Path(args.retrieval_metrics_file)
    else: # Try to find it in a subfolder of the main model output if not specified
        ret_metrics_file_path = model_output_dir / "retrieval_evaluation_metrics.json" # Default assumption
        if not ret_metrics_file_path.exists() and (model_output_dir / "../retrieval_test_output").exists(): # common case for testing
             ret_metrics_file_path = model_output_dir / "../retrieval_test_output/retrieval_evaluation_metrics.json"

    ret_metrics = load_json_metrics(ret_metrics_file_path)
    if ret_metrics:
        visualize_retrieval_metrics_plot(ret_metrics, viz_output_dir)
    
    # 4. Visualize Embedding Distribution (PCA/t-SNE)
    if user_ids is not None and embeddings is not None:
        candidate_labels_df = pd.DataFrame() # Default to no labels
        if args.visualize_with_labels:
            logger.info("Attempting to load candidate labels for PCA/t-SNE visualization...")
            # Use default paths or allow override via args if a specific candidate file is needed
            candidate_labels_df = load_candidate_labels_for_viz(
                Path(args.candidate_data_dir_for_viz),
                args.user_id_col_viz if args.user_id_col_viz else USER_ID_COL,
                args.label_col_viz if args.label_col_viz else DEFAULT_LABEL_COL,
                args.max_candidate_files_viz
            )
        visualize_embedding_distribution_plot(embeddings, user_ids, candidate_labels_df, viz_output_dir, 
                                            args.user_id_col_viz if args.user_id_col_viz else USER_ID_COL, 
                                            args.label_col_viz if args.label_col_viz else DEFAULT_LABEL_COL,
                                            n_samples=args.n_samples_for_dist_plot)

    # 5. Visualize Distances of Retrieved Items
    # This needs the output of the retrieval script (retrieval_results.json)
    retrieval_results_json_path = Path(args.retrieval_results_file) if args.retrieval_results_file else model_output_dir / "retrieval_results.json"
    if not retrieval_results_json_path.exists() and (model_output_dir / "../retrieval_test_output").exists():
        retrieval_results_json_path = model_output_dir / "../retrieval_test_output/retrieval_results.json"

    retrieval_distances_data = load_json_metrics(retrieval_results_json_path) # Reusing load_json_metrics
    if retrieval_distances_data:
        visualize_retrieved_item_distances(retrieval_distances_data, viz_output_dir)

    logger.info(f"Visualization script finished. Outputs in {viz_output_dir}")
    if logger.handlers:
         logger.removeHandler(log_file_handler) # Clean up specific file handler
         log_file_handler.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="User Embedding Model Visualization Script")
    parser.add_argument("--model_output_dir", type=str, required=True,
                        help="Path to the directory containing model outputs (embeddings, metrics).")
    parser.add_argument("--retrieval_metrics_file", type=str, default=None,
                        help="Optional: Path to retrieval_evaluation_metrics.json if not in model_output_dir.")
    parser.add_argument("--retrieval_results_file", type=str, default=None,
                        help="Optional: Path to retrieval_results.json (for distance plot) if not in model_output_dir.")
    parser.add_argument("--n_samples_for_dist_plot", type=int, default=2000, 
                        help="Number of samples for PCA/t-SNE plots.")
    parser.add_argument("--visualize_with_labels", action="store_true", 
                        help="Attempt to load candidate labels to color PCA/t-SNE plots.")
    # Args for loading candidate labels if visualize_with_labels is True
    parser.add_argument("--candidate_data_dir_for_viz", type=str, default=None, 
                        help="Full path to candidate Parquet directory for visualization labels (e.g., data/.../datetime=YYYYMMDD).")
    parser.add_argument("--user_id_col_viz", type=str, default=None)
    parser.add_argument("--label_col_viz", type=str, default=None)
    parser.add_argument("--max_candidate_files_viz", type=int, default=None, 
                        help="Optional: Max number of candidate Parquet files to load for labels (for visualization).")

    args = parser.parse_args()
    main(args) 