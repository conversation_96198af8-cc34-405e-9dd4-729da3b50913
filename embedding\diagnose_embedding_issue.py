#!/usr/bin/env python3
"""
诊断embedding维度不匹配问题的脚本
分析训练配置并生成正确的extract_embeddings命令
"""

import json
import os
import sys
from pathlib import Path
import pickle


def find_training_output_dirs():
    """查找可能的训练输出目录"""
    possible_dirs = []
    
    # 当前目录下查找
    for item in Path('.').rglob('*'):
        if item.is_dir() and (
            'training_config.json' in [f.name for f in item.iterdir() if f.is_file()] or
            'trained_model.keras' in [f.name for f in item.iterdir() if f.is_file()] or
            'my_test_run' in str(item)
        ):
            possible_dirs.append(item)
    
    return possible_dirs


def analyze_training_config(config_path):
    """分析训练配置文件"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"✓ 找到训练配置文件: {config_path}")
        print("训练时的配置:")
        
        use_enhanced = config.get('USE_ENHANCED_MODEL', False)
        include_static = config.get('INCLUDE_STATIC_FEATURES', False)
        master_seq_map = config.get('MASTER_SEQUENCE_FEATURES_MAP', {})
        maxlen = config.get('MAXLEN', 64)
        
        print(f"  - 使用增强模型: {use_enhanced}")
        print(f"  - 包含静态特征: {include_static}")
        print(f"  - 序列长度: {maxlen}")
        print(f"  - 序列特征映射: {master_seq_map}")
        
        return {
            'use_enhanced': use_enhanced,
            'include_static': include_static,
            'master_seq_map': master_seq_map,
            'maxlen': maxlen
        }
        
    except Exception as e:
        print(f"✗ 无法读取配置文件 {config_path}: {e}")
        return None


def analyze_static_features(output_dir):
    """分析静态特征配置"""
    static_names_path = output_dir / 'processed_static_feature_names.json'
    static_preprocessors_path = output_dir / 'static_preprocessors.pkl'
    
    if static_names_path.exists():
        try:
            with open(static_names_path, 'r') as f:
                static_names = json.load(f)
            print(f"  - 静态特征数量: {len(static_names)}")
            print(f"  - 静态特征: {static_names[:5]}...")  # 显示前5个
            return len(static_names)
        except Exception as e:
            print(f"✗ 无法读取静态特征配置: {e}")
    
    return 0


def analyze_vocabularies(output_dir):
    """分析词汇表"""
    vocab_path = output_dir / 'all_vocabs.pkl'
    
    if vocab_path.exists():
        try:
            with open(vocab_path, 'rb') as f:
                vocabs = pickle.load(f)
            
            print("  - 词汇表:")
            for seq_key, (token_to_id, id_to_token) in vocabs.items():
                print(f"    * {seq_key}: {len(token_to_id)} 个词汇")
            
            return vocabs
        except Exception as e:
            print(f"✗ 无法读取词汇表: {e}")
    
    return {}


def generate_correct_command(config, output_dir, test_date="20240531"):
    """根据配置生成正确的命令"""
    base_cmd = [
        "python embedding/embedding_model_train.py",
        f"--output-dir {output_dir}",
        f"--mode extract_embeddings",
        f"--test-date {test_date}",
        f"--maxlen {config['maxlen']}"
    ]
    
    if config['use_enhanced']:
        base_cmd.append("--use-enhanced-model")
        
        # 分析额外序列特征
        additional_seqs = []
        seq_map = config['master_seq_map']
        primary_seqs = ['action_seq', 'day_seq']
        
        for seq_key in seq_map.keys():
            if seq_key not in primary_seqs:
                additional_seqs.append(seq_key)
        
        if additional_seqs:
            base_cmd.append(f"--additional-sequences {' '.join(additional_seqs)}")
    
    if config['include_static']:
        base_cmd.append("--include-static-features")
    
    return " \\\n  ".join(base_cmd)


def main():
    print("=== Embedding维度不匹配问题诊断 ===\n")
    
    # 查找训练输出目录
    print("1. 查找训练输出目录...")
    training_dirs = find_training_output_dirs()
    
    if not training_dirs:
        print("✗ 未找到训练输出目录")
        print("请确保存在包含training_config.json的目录")
        sys.exit(1)
    
    print(f"找到 {len(training_dirs)} 个可能的训练目录:")
    for i, dir_path in enumerate(training_dirs):
        print(f"  {i+1}. {dir_path}")
    
    # 选择目录（如果只有一个，自动选择）
    if len(training_dirs) == 1:
        selected_dir = training_dirs[0]
        print(f"\n自动选择: {selected_dir}")
    else:
        try:
            choice = int(input("\n请选择目录编号: ")) - 1
            selected_dir = training_dirs[choice]
        except (ValueError, IndexError):
            print("无效选择")
            sys.exit(1)
    
    print(f"\n2. 分析训练配置...")
    config_path = selected_dir / 'training_config.json'
    
    if not config_path.exists():
        print(f"✗ 配置文件不存在: {config_path}")
        sys.exit(1)
    
    config = analyze_training_config(config_path)
    if not config:
        sys.exit(1)
    
    print(f"\n3. 分析特征配置...")
    static_dim = analyze_static_features(selected_dir)
    vocabs = analyze_vocabularies(selected_dir)
    
    print(f"\n4. 生成正确的extract_embeddings命令...")
    correct_cmd = generate_correct_command(config, selected_dir)
    
    print(f"\n=== 解决方案 ===")
    print("请使用以下命令进行embedding提取:")
    print(f"\n{correct_cmd}")
    
    print(f"\n=== 问题原因分析 ===")
    print("维度不匹配 (387 vs 379) 的可能原因:")
    print(f"1. 静态特征配置不一致 (当前静态特征维度: {static_dim})")
    print(f"2. 序列特征配置不一致")
    print(f"3. 模型类型不匹配 (增强模型: {config['use_enhanced']})")
    
    # 保存命令到文件
    cmd_file = selected_dir / 'correct_extract_command.sh'
    with open(cmd_file, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# 自动生成的正确extract_embeddings命令\n\n")
        f.write(correct_cmd)
    
    print(f"\n命令已保存到: {cmd_file}")
    print("可以直接运行: bash " + str(cmd_file))


if __name__ == "__main__":
    main() 