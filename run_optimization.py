#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NIO模型优化迭代主启动脚本
统一的模型测试评估和优化迭代入口

功能：
1. 基线性能测试
2. 优化实验执行
3. 结果对比分析
4. 自动化优化迭代

使用方法：
python run_optimization.py --mode baseline                    # 运行基线测试
python run_optimization.py --mode optimize --opt smote        # 运行SMOTE优化
python run_optimization.py --mode sequence                    # 运行优化序列
"""
import os
import sys
import json
import argparse
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NIOOptimizationRunner:
    """NIO模型优化运行器"""
    
    def __init__(self):
        self.logs_dir = "logs/experiments"
        self.src_dir = "src"
        self.data_dir = "data"
        
        # 确保目录存在
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # 基准性能指标
        self.historical_best = {
            'auc': 0.9146,
            'pr_auc': 0.5619,
            'recall_at_840': 0.9747
        }
        
        self.epmmoe_baseline = {
            'month_1_auc': 0.8906,
            'month_1_pr_auc': 0.4808,
            'overall_auc': 0.8104,
            'overall_pr_auc': 0.0099
        }
        
        self.baseline_results = None
    
    def run_baseline(self) -> Dict:
        """运行基线测试"""
        logger.info("=== 🚀 运行EPMMOENet基线测试 ===")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"baseline_{timestamp}"
        
        # 构建训练命令
        cmd = [
            "python", f"{self.src_dir}/train.py",
            "--run_name", run_name,
            "--epochs", "15",
            "--data_dir", self.data_dir,
            "--output_dir", f"{self.logs_dir}/{run_name}"
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                # 保存stderr以便解析
                self._last_stderr = result.stderr

                # 解析结果
                baseline_results = self._parse_training_output(result.stdout)
                baseline_results.update({
                    'run_name': run_name,
                    'timestamp': timestamp,
                    'type': 'baseline',
                    'command': ' '.join(cmd),
                    'stdout': result.stdout,
                    'stderr': result.stderr
                })

                # 保存基线结果
                self.baseline_results = baseline_results
                result_file = f"{self.logs_dir}/{run_name}/baseline_results.json"
                self._save_results(baseline_results, result_file)

                # 输出结果
                self._print_baseline_results(baseline_results)

                return baseline_results
            else:
                logger.error(f"基线训练失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("基线训练超时")
            return None
        except Exception as e:
            logger.error(f"基线训练异常: {e}")
            return None
    
    def run_optimization(self, opt_name: str, opt_config: Dict) -> Dict:
        """运行单次优化"""
        logger.info(f"=== 🔧 运行优化: {opt_name} ===")
        
        if not self.baseline_results:
            logger.error("请先运行基线测试")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"opt_{opt_name}_{timestamp}"
        
        # 根据优化类型构建命令
        if opt_name == "smote":
            success = self._run_smote_optimization(run_name, opt_config)
        elif opt_name == "extended_epochs":
            success = self._run_extended_epochs(run_name, opt_config)
        elif opt_name == "larger_batch":
            success = self._run_larger_batch(run_name, opt_config)
        else:
            logger.error(f"未知的优化类型: {opt_name}")
            return None
        
        if success:
            # 解析和评估结果
            result_file = f"{self.logs_dir}/{run_name}/optimization_results.json"
            if os.path.exists(result_file):
                with open(result_file, 'r', encoding='utf-8') as f:
                    opt_results = json.load(f)
                
                # 评估优化效果
                is_effective = self._evaluate_optimization(opt_results)
                opt_results['is_effective'] = is_effective
                
                # 更新结果文件
                self._save_results(opt_results, result_file)
                
                # 输出结果
                self._print_optimization_results(opt_name, opt_results)
                
                return opt_results
        
        return None
    
    def _run_smote_optimization(self, run_name: str, config: Dict) -> bool:
        """运行SMOTE优化"""
        logger.info("执行SMOTE数据增强优化...")
        
        # 1. 创建SMOTE增强数据集
        from data.smote_enhancer import SMOTEEnhancer
        
        enhancer = SMOTEEnhancer(
            sampling_strategy=config.get('sampling_strategy', 0.05),
            k_neighbors=config.get('k_neighbors', 5)
        )
        
        train_path = f"{self.data_dir}/dataset_nio_new_car_v15/datetime=20240430"
        test_path = f"{self.data_dir}/dataset_nio_new_car_v15/datetime=20240531"
        enhanced_dir = f"{self.data_dir}/dataset_nio_new_car_v15_smote"
        
        enhanced_train, enhanced_test = enhancer.enhance_dataset(
            train_path, test_path, enhanced_dir
        )
        
        if not enhanced_train:
            logger.error("SMOTE数据增强失败")
            return False
        
        # 2. 使用增强数据训练模型
        cmd = [
            "python", f"{self.src_dir}/train.py",
            "--run_name", run_name,
            "--epochs", str(config.get('epochs', 15)),
            "--data_dir", self.data_dir,
            "--dataset_code", "dataset_nio_new_car_v15_smote",
            "--output_dir", f"{self.logs_dir}/{run_name}"
        ]
        
        return self._execute_training_command(cmd, run_name, config)
    
    def _run_extended_epochs(self, run_name: str, config: Dict) -> bool:
        """运行延长训练轮数优化"""
        logger.info("执行延长训练轮数优化...")
        
        cmd = [
            "python", f"{self.src_dir}/train.py",
            "--run_name", run_name,
            "--epochs", str(config.get('epochs', 25)),
            "--data_dir", self.data_dir,
            "--output_dir", f"{self.logs_dir}/{run_name}"
        ]
        
        return self._execute_training_command(cmd, run_name, config)
    
    def _run_larger_batch(self, run_name: str, config: Dict) -> bool:
        """运行大批量优化"""
        logger.info("执行大批量训练优化...")
        
        cmd = [
            "python", f"{self.src_dir}/train.py",
            "--run_name", run_name,
            "--epochs", str(config.get('epochs', 15)),
            "--batch_size", str(config.get('batch_size', 16384)),
            "--data_dir", self.data_dir,
            "--output_dir", f"{self.logs_dir}/{run_name}"
        ]
        
        return self._execute_training_command(cmd, run_name, config)
    
    def _execute_training_command(self, cmd: List[str], run_name: str, config: Dict) -> bool:
        """执行训练命令"""
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                # 解析结果
                opt_results = self._parse_training_output(result.stdout)
                opt_results.update({
                    'run_name': run_name,
                    'timestamp': datetime.now().isoformat(),
                    'type': 'optimization',
                    'config': config,
                    'command': ' '.join(cmd),
                    'stdout': result.stdout,
                    'stderr': result.stderr
                })
                
                # 保存结果
                result_file = f"{self.logs_dir}/{run_name}/optimization_results.json"
                os.makedirs(os.path.dirname(result_file), exist_ok=True)
                self._save_results(opt_results, result_file)
                
                return True
            else:
                logger.error(f"训练失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("训练超时")
            return False
        except Exception as e:
            logger.error(f"训练异常: {e}")
            return False
    
    def _parse_training_output(self, output: str) -> Dict:
        """解析训练输出"""
        results = {}

        # 同时解析stdout和stderr
        all_output = output
        if hasattr(self, '_last_stderr'):
            all_output += "\n" + self._last_stderr

        lines = all_output.split('\n')

        for line in lines:
            # 解析Month_1指标 - 格式: "Month_1: ROC-AUC = 0.8805, PR-AUC = 0.4179"
            if 'Month_1:' in line and 'ROC-AUC' in line:
                try:
                    # 提取AUC
                    if 'ROC-AUC =' in line:
                        auc_part = line.split('ROC-AUC =')[1].split(',')[0].strip()
                        results['month_1_auc'] = float(auc_part)

                    # 提取PR-AUC
                    if 'PR-AUC =' in line:
                        pr_auc_part = line.split('PR-AUC =')[1].strip()
                        results['month_1_pr_auc'] = float(pr_auc_part)
                except:
                    pass

            # 解析Overall指标 - 格式: "Overall: ROC-AUC = 0.8106, PR-AUC = 0.0094"
            if 'Overall:' in line and 'ROC-AUC' in line:
                try:
                    # 提取AUC
                    if 'ROC-AUC =' in line:
                        auc_part = line.split('ROC-AUC =')[1].split(',')[0].strip()
                        results['overall_auc'] = float(auc_part)

                    # 提取PR-AUC
                    if 'PR-AUC =' in line:
                        pr_auc_part = line.split('PR-AUC =')[1].strip()
                        results['overall_pr_auc'] = float(pr_auc_part)
                except:
                    pass

        return results
    
    def _evaluate_optimization(self, opt_results: Dict) -> bool:
        """评估优化是否有效"""
        if not self.baseline_results:
            return False
        
        # 主要评估指标：Month_1 PR-AUC
        baseline_pr_auc = self.baseline_results.get('month_1_pr_auc', 0)
        opt_pr_auc = opt_results.get('month_1_pr_auc', 0)
        
        # 次要评估指标：Month_1 AUC
        baseline_auc = self.baseline_results.get('month_1_auc', 0)
        opt_auc = opt_results.get('month_1_auc', 0)
        
        # 评估标准：PR-AUC提升 > 1% 或 (PR-AUC提升 > 0.5% 且 AUC不下降超过1%)
        pr_auc_improvement = opt_pr_auc - baseline_pr_auc
        auc_change = opt_auc - baseline_auc
        
        if pr_auc_improvement > 0.01:  # PR-AUC提升超过1%
            return True
        elif pr_auc_improvement > 0.005 and auc_change > -0.01:  # PR-AUC提升0.5%且AUC不大幅下降
            return True
        else:
            return False
    
    def _save_results(self, results: Dict, filepath: str):
        """保存结果"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
    
    def _print_baseline_results(self, results: Dict):
        """打印基线结果"""
        logger.info("=== 📊 基线测试结果 ===")
        logger.info(f"Month_1 AUC: {results.get('month_1_auc', 0):.4f}")
        logger.info(f"Month_1 PR-AUC: {results.get('month_1_pr_auc', 0):.4f}")
        logger.info(f"Overall AUC: {results.get('overall_auc', 0):.4f}")
        logger.info(f"Overall PR-AUC: {results.get('overall_pr_auc', 0):.4f}")
        
        # 与历史最佳对比
        month1_auc_gap = self.historical_best['auc'] - results.get('month_1_auc', 0)
        month1_pr_auc_gap = self.historical_best['pr_auc'] - results.get('month_1_pr_auc', 0)
        
        logger.info("=== 📈 与历史最佳对比 ===")
        logger.info(f"AUC差距: {month1_auc_gap:.4f}")
        logger.info(f"PR-AUC差距: {month1_pr_auc_gap:.4f}")
    
    def _print_optimization_results(self, opt_name: str, results: Dict):
        """打印优化结果"""
        logger.info(f"=== 📊 优化结果: {opt_name} ===")
        logger.info(f"Month_1 AUC: {results.get('month_1_auc', 0):.4f}")
        logger.info(f"Month_1 PR-AUC: {results.get('month_1_pr_auc', 0):.4f}")
        
        # 与基线对比
        if self.baseline_results:
            auc_diff = results.get('month_1_auc', 0) - self.baseline_results.get('month_1_auc', 0)
            pr_auc_diff = results.get('month_1_pr_auc', 0) - self.baseline_results.get('month_1_pr_auc', 0)
            
            logger.info("=== 📈 与基线对比 ===")
            logger.info(f"AUC变化: {auc_diff:+.4f}")
            logger.info(f"PR-AUC变化: {pr_auc_diff:+.4f}")
            logger.info(f"优化效果: {'✅ 有效' if results.get('is_effective', False) else '❌ 无效'}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NIO模型优化迭代主启动脚本')
    parser.add_argument('--mode', choices=['baseline', 'optimize', 'sequence'], 
                       required=True, help='运行模式')
    parser.add_argument('--opt', choices=['smote', 'extended_epochs', 'larger_batch'],
                       help='优化类型（optimize模式时必需）')
    parser.add_argument('--config', type=str, help='优化配置JSON字符串')
    
    args = parser.parse_args()
    
    runner = NIOOptimizationRunner()
    
    if args.mode == 'baseline':
        # 运行基线测试
        result = runner.run_baseline()
        if result:
            print(f"\n🎯 基线测试完成！")
            print(f"Month_1 AUC: {result.get('month_1_auc', 0):.4f}")
            print(f"Month_1 PR-AUC: {result.get('month_1_pr_auc', 0):.4f}")
        else:
            print("❌ 基线测试失败")
            return 1
    
    elif args.mode == 'optimize':
        if not args.opt:
            print("❌ optimize模式需要指定--opt参数")
            return 1
        
        # 解析配置
        config = {}
        if args.config:
            try:
                config = json.loads(args.config)
            except json.JSONDecodeError:
                print("❌ 配置JSON格式错误")
                return 1
        
        # 设置默认配置
        if args.opt == 'smote':
            config.setdefault('sampling_strategy', 0.05)
            config.setdefault('k_neighbors', 5)
            config.setdefault('epochs', 15)
        elif args.opt == 'extended_epochs':
            config.setdefault('epochs', 25)
        elif args.opt == 'larger_batch':
            config.setdefault('batch_size', 16384)
            config.setdefault('epochs', 15)
        
        # 运行优化
        result = runner.run_optimization(args.opt, config)
        if result:
            print(f"\n🎯 优化 {args.opt} 完成！")
            print(f"Month_1 AUC: {result.get('month_1_auc', 0):.4f}")
            print(f"Month_1 PR-AUC: {result.get('month_1_pr_auc', 0):.4f}")
            print(f"优化效果: {'✅ 有效' if result.get('is_effective', False) else '❌ 无效'}")
        else:
            print(f"❌ 优化 {args.opt} 失败")
            return 1
    
    elif args.mode == 'sequence':
        # 运行优化序列
        print("🚀 开始优化序列...")
        
        # 1. 运行基线
        baseline = runner.run_baseline()
        if not baseline:
            print("❌ 基线测试失败，终止优化序列")
            return 1
        
        # 2. 运行优化序列
        optimizations = [
            ('smote', {'sampling_strategy': 0.05, 'epochs': 15}),
            ('extended_epochs', {'epochs': 25}),
            ('larger_batch', {'batch_size': 16384, 'epochs': 15})
        ]
        
        effective_count = 0
        for opt_name, opt_config in optimizations:
            result = runner.run_optimization(opt_name, opt_config)
            if result and result.get('is_effective', False):
                effective_count += 1
        
        print(f"\n🎯 优化序列完成！")
        print(f"有效优化数量: {effective_count}/{len(optimizations)}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
