#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化26: EPMMOENet + SMOTE增强
基于成功的EPMMOENet基线，应用已验证有效的SMOTE技术
历史最优基线: Month_1 AUC 0.8906, PR-AUC 0.4808
目标: 在EPMMOENet基础上应用SMOTE，进一步提升PR-AUC和Recall
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入历史最优模型组件
from data.loader import DataLoader
from models.networks.EPMMOENet import EPMMOENet_Model
from data.preprocessor import DataPreprocessor
from features.builder import FeatureBuilder
from utils.config_utils import ConfigManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def load_and_prepare_data():
    """加载和准备数据 - 使用与历史最优相同的流程"""
    logger.info("=== 加载和准备数据 ===")
    
    # 1. 加载配置
    config_manager = ConfigManager()
    model_config = config_manager.load_config("src/configs/models/sample_20250311_v7-20250311.json")
    
    # 2. 数据加载参数
    train_dates = model_config.get("train_dates", ["20240430"])
    test_dates = model_config.get("test_dates", ["20240531"])
    dict_raw_features = model_config.get("RawFeature", {})
    extra_datasets = model_config.get("Extra", [])
    
    # 3. 准备列名
    list_raw_features = list(dict_raw_features.keys())
    list_raw_labels = ["purchase_days_nio_new_car_total"]
    read_columns = ["user_id", "datetime"] + list_raw_features + list_raw_labels
    dataset_read_columns = read_columns + ["m_purchase_days_nio_new_car"]
    
    logger.info(f"特征数: {len(list_raw_features)}")
    
    # 4. 数据加载
    dataset_path = "data/dataset_nio_new_car_v15"
    dataset_config_path = "src/configs/datasets/dataset_nio_new_car_v15.json"
    data_loader = DataLoader(dataset_path, dataset_config_path)
    
    # 加载训练和测试数据
    dataset_dates = sorted(list(set(train_dates + test_dates)))
    df_raw = data_loader.load_dataset(
        dates=dataset_dates,
        columns=dataset_read_columns,
        extra_datasets=extra_datasets
    )
    
    # 5. 数据预处理
    preprocessor = DataPreprocessor(data_loader.feature_padding_dict, train_dates=train_dates)
    
    # 预处理特征
    df_raw = preprocessor.preprocess_features(df_raw)
    df_raw = preprocessor.preprocess_model_features(df_raw, dict_raw_features)
    df_raw = preprocessor.process_purchase_labels(df_raw)
    
    # 6. 特征工程
    feature_builder = FeatureBuilder()
    df_train_m, df_test_m = feature_builder.split_train_test(df_raw, test_dates)
    
    logger.info(f"训练集: {len(df_train_m)}, 测试集: {len(df_test_m)}")
    
    return df_train_m, df_test_m, dict_raw_features, model_config

def extract_features_and_labels(df_train, df_test, dict_raw_features):
    """提取特征和标签用于SMOTE处理"""
    logger.info("=== 提取特征和标签 ===")

    # 提取数值特征
    feature_cols = []
    for feature_name, feature_config in dict_raw_features.items():
        if feature_config.get("feature_type") in ["Dense", "Bucket"]:
            if feature_name in df_train.columns:
                feature_cols.append(feature_name)

    # 如果没有找到Dense/Bucket特征，使用所有数值列
    if len(feature_cols) == 0:
        numeric_cols = [col for col in df_train.columns
                       if df_train[col].dtype in ['int64', 'float64']
                       and col not in ['user_id', 'datetime', 'm_purchase_days_nio_new_car', 'm_purchase_days_nio_new_car_consum']]
        feature_cols = numeric_cols[:200]

    # 选择前200个数值特征
    feature_cols = feature_cols[:200]
    
    X_train = df_train[feature_cols].fillna(0).values
    X_test = df_test[feature_cols].fillna(0).values
    
    # 提取标签 - 处理可能的列表格式
    y_train_raw = df_train["m_purchase_days_nio_new_car_consum"]
    y_test_raw = df_test["m_purchase_days_nio_new_car_consum"]

    # 处理可能的列表格式标签
    def process_labels(y_raw):
        if y_raw.dtype == 'object':
            # 如果是object类型，可能包含列表
            processed = []
            for val in y_raw:
                if isinstance(val, (list, np.ndarray)):
                    # 如果是列表或数组，取第一个元素
                    processed.append(float(val[0]) if len(val) > 0 else 0.0)
                else:
                    # 如果是标量，直接转换
                    processed.append(float(val) if val is not None else 0.0)
            return np.array(processed)
        else:
            # 如果是数值类型，直接转换
            return y_raw.astype(float)

    y_train = process_labels(y_train_raw)
    y_test = process_labels(y_test_raw)

    logger.info(f"特征维度: {X_train.shape[1]}")
    logger.info(f"标签形状: 训练集{y_train.shape}, 测试集{y_test.shape}")
    logger.info(f"标签类型: {type(y_train)}, 数据类型: {y_train.dtype}")

    train_pos_ratio = np.mean(y_train)
    test_pos_ratio = np.mean(y_test)
    logger.info(f"训练集正样本比例: {train_pos_ratio:.4f}")
    logger.info(f"测试集正样本比例: {test_pos_ratio:.4f}")
    
    return X_train, X_test, y_train, y_test, feature_cols

def create_enhanced_epmmoe_models(model_config):
    """创建增强版EPMMOENet模型"""
    models = []
    
    # 模型1: 原始EPMMOENet
    model1 = EPMMOENet_Model(
        feature_column=model_config,
        output_dimension=1,
        output_activation="sigmoid",
        default_embedding_dimension=8,
        default_gru_dimension=32,
        expert_num=8,
        use_cross_layer=True,
        use_mixed_precision=True,
        use_time_attention=True,
        time_decay_factor=0.05
    )
    models.append(("Original_EPMMOENet", model1))
    
    # 模型2: 增强版EPMMOENet - 更大的嵌入维度
    model2 = EPMMOENet_Model(
        feature_column=model_config,
        output_dimension=1,
        output_activation="sigmoid",
        default_embedding_dimension=12,
        default_gru_dimension=48,
        expert_num=8,
        use_cross_layer=True,
        use_mixed_precision=True,
        use_time_attention=True,
        time_decay_factor=0.03
    )
    models.append(("Enhanced_EPMMOENet_v1", model2))
    
    # 模型3: 增强版EPMMOENet - 更多专家
    model3 = EPMMOENet_Model(
        feature_column=model_config,
        output_dimension=1,
        output_activation="sigmoid",
        default_embedding_dimension=10,
        default_gru_dimension=40,
        expert_num=12,
        use_cross_layer=True,
        use_mixed_precision=True,
        use_time_attention=True,
        time_decay_factor=0.07
    )
    models.append(("Enhanced_EPMMOENet_v2", model3))
    
    return models

def train_with_smote_variants(models, df_train, df_test, dict_raw_features):
    """使用不同SMOTE策略训练模型"""
    logger.info("=== 使用SMOTE变体训练模型 ===")
    
    # 提取特征和标签
    X_train, X_test, y_train, y_test, feature_cols = extract_features_and_labels(
        df_train, df_test, dict_raw_features
    )
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # SMOTE策略
    smote_strategies = [
        ("SMOTE_0.03", SMOTE(sampling_strategy=0.03, random_state=42, k_neighbors=5)),
        ("SMOTE_0.05", SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)),
        ("SMOTE_0.08", SMOTE(sampling_strategy=0.08, random_state=42, k_neighbors=3))
    ]
    
    trained_models = []
    predictions = []
    
    for (model_name, model), (smote_name, smote) in zip(models, smote_strategies):
        logger.info(f"=== 训练 {model_name} with {smote_name} ===")
        
        # 应用SMOTE
        X_train_smote, y_train_smote = smote.fit_resample(X_train_scaled, y_train)
        logger.info(f"{smote_name}: {len(X_train_scaled)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
        
        # 编译模型
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['auc']
        )
        
        # 训练参数
        callbacks = [
            tf.keras.callbacks.EarlyStopping(patience=8, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(patience=4, factor=0.5, min_lr=1e-6)
        ]
        
        # 训练模型
        model.fit(
            X_train_smote, y_train_smote,
            validation_data=(X_test_scaled, y_test),
            epochs=15, batch_size=4096,
            callbacks=callbacks,
            verbose=0
        )
        
        # 预测
        pred = model.predict(X_test_scaled, verbose=0)
        predictions.append(pred)
        trained_models.append((f"{model_name}_{smote_name}", model))
        
        # 单模型评估
        auc_score = roc_auc_score(y_test, pred)
        pr_auc = calculate_pr_auc(y_test, pred)
        precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
        
        logger.info(f"{model_name}_{smote_name} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
        logger.info(f"{model_name}_{smote_name} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
    
    return trained_models, predictions, y_test

def intelligent_ensemble(predictions, y_test):
    """智能集成 - 基于PR-AUC加权"""
    logger.info("=== 智能集成优化 ===")
    
    # 计算每个模型的PR-AUC权重
    pr_aucs = []
    for pred in predictions:
        pr_auc = calculate_pr_auc(y_test, pred)
        pr_aucs.append(pr_auc)
    
    # 使用PR-AUC作为权重进行加权平均
    pr_aucs = np.array(pr_aucs)
    weights = pr_aucs / pr_aucs.sum()
    
    # 加权集成
    weighted_ensemble = np.average(predictions, axis=0, weights=weights)
    
    # 简单平均集成作为对比
    simple_ensemble = np.mean(predictions, axis=0)
    
    return weighted_ensemble, simple_ensemble, weights, pr_aucs

def train_and_evaluate():
    """训练和评估EPMMOENet + SMOTE增强模型"""
    
    # 1. 加载和准备数据
    df_train, df_test, dict_raw_features, model_config = load_and_prepare_data()
    
    # 2. 创建增强版EPMMOENet模型
    models = create_enhanced_epmmoe_models(model_config)
    
    # 3. 使用SMOTE变体训练
    trained_models, predictions, y_test = train_with_smote_variants(
        models, df_train, df_test, dict_raw_features
    )
    
    # 4. 智能集成
    weighted_ensemble, simple_ensemble, weights, pr_aucs = intelligent_ensemble(
        predictions, y_test
    )
    
    # 5. 详细评估
    weighted_auc = roc_auc_score(y_test, weighted_ensemble)
    weighted_pr_auc = calculate_pr_auc(y_test, weighted_ensemble)
    weighted_precision_at_840, weighted_recall_at_840 = calculate_precision_recall_at_k(y_test, weighted_ensemble, k=840)
    
    simple_auc = roc_auc_score(y_test, simple_ensemble)
    simple_pr_auc = calculate_pr_auc(y_test, simple_ensemble)
    simple_precision_at_840, simple_recall_at_840 = calculate_precision_recall_at_k(y_test, simple_ensemble, k=840)
    
    logger.info(f"=== EPMMOENet + SMOTE增强结果 ===")
    logger.info(f"加权集成 - AUC: {weighted_auc:.4f}, PR-AUC: {weighted_pr_auc:.4f}")
    logger.info(f"加权集成 - P@840: {weighted_precision_at_840:.4f}, R@840: {weighted_recall_at_840:.4f}")
    logger.info(f"简单集成 - AUC: {simple_auc:.4f}, PR-AUC: {simple_pr_auc:.4f}")
    logger.info(f"简单集成 - P@840: {simple_precision_at_840:.4f}, R@840: {simple_recall_at_840:.4f}")
    
    # 与历史最佳和基线对比
    historical_best_auc = 0.9146
    historical_best_pr_auc = 0.5619
    historical_best_recall = 0.9747
    
    baseline_auc = 0.8906  # EPMMOENet基线Month_1
    baseline_pr_auc = 0.4808  # EPMMOENet基线Month_1
    
    auc_vs_historical = weighted_auc - historical_best_auc
    pr_auc_vs_historical = weighted_pr_auc - historical_best_pr_auc
    recall_vs_historical = weighted_recall_at_840 - historical_best_recall
    
    auc_vs_baseline = weighted_auc - baseline_auc
    pr_auc_vs_baseline = weighted_pr_auc - baseline_pr_auc
    
    logger.info(f"相比历史最佳: AUC {auc_vs_historical:+.4f}, PR-AUC {pr_auc_vs_historical:+.4f}, Recall@840 {recall_vs_historical:+.4f}")
    logger.info(f"相比EPMMOENet基线: AUC {auc_vs_baseline:+.4f}, PR-AUC {pr_auc_vs_baseline:+.4f}")
    
    # 打印权重信息
    logger.info("=== 模型权重信息 ===")
    for i, (model_name, _) in enumerate(trained_models):
        logger.info(f"{model_name}: 权重={weights[i]:.3f}, PR-AUC={pr_aucs[i]:.4f}")
    
    return {
        'optimization': 'epmmoe_with_smote',
        'weighted_ensemble': {
            'auc': float(weighted_auc),
            'pr_auc': float(weighted_pr_auc),
            'precision_at_840': float(weighted_precision_at_840),
            'recall_at_840': float(weighted_recall_at_840)
        },
        'simple_ensemble': {
            'auc': float(simple_auc),
            'pr_auc': float(simple_pr_auc),
            'precision_at_840': float(simple_precision_at_840),
            'recall_at_840': float(simple_recall_at_840)
        },
        'model_count': len(trained_models),
        'model_weights': weights.tolist(),
        'individual_pr_aucs': pr_aucs.tolist(),
        'historical_best': {
            'auc': historical_best_auc,
            'pr_auc': historical_best_pr_auc,
            'recall_at_840': historical_best_recall
        },
        'baseline_epmmoe': {
            'auc': baseline_auc,
            'pr_auc': baseline_pr_auc
        },
        'vs_historical': {
            'auc': float(auc_vs_historical),
            'pr_auc': float(pr_auc_vs_historical),
            'recall_at_840': float(recall_vs_historical)
        },
        'vs_baseline': {
            'auc': float(auc_vs_baseline),
            'pr_auc': float(pr_auc_vs_baseline)
        },
        'effective': bool(weighted_pr_auc > 0.3 and weighted_recall_at_840 > 0.6)
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_epmmoe_with_smote"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化26完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"加权集成AUC: {result['weighted_ensemble']['auc']:.4f}")
    print(f"加权集成PR-AUC: {result['weighted_ensemble']['pr_auc']:.4f}")
    print(f"加权集成P@840: {result['weighted_ensemble']['precision_at_840']:.4f}")
    print(f"加权集成R@840: {result['weighted_ensemble']['recall_at_840']:.4f}")
    print(f"模型数: {result['model_count']}")
    print(f"相比历史最佳AUC: {result['vs_historical']['auc']:+.4f}")
    print(f"相比历史最佳PR-AUC: {result['vs_historical']['pr_auc']:+.4f}")
    print(f"相比历史最佳Recall@840: {result['vs_historical']['recall_at_840']:+.4f}")
    print(f"相比EPMMOENet基线AUC: {result['vs_baseline']['auc']:+.4f}")
    print(f"相比EPMMOENet基线PR-AUC: {result['vs_baseline']['pr_auc']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
