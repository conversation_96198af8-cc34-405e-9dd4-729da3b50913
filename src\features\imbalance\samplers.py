"""
Resampling techniques for imbalanced datasets.

This module provides implementations of various resampling techniques
for handling class imbalance:
- SMOTE: Synthetic Minority Over-sampling Technique
- SMOTE-ENN: SMOTE with Edited Nearest Neighbors cleaning
- Optimized SMOTE-ENN: SMOTE-ENN with controlled ratio of synthetic samples
"""
import numpy as np
import pandas as pd
import logging
from collections import Counter
from imblearn.over_sampling import SMOTE
from imblearn.combine import SMOTEENN
from imblearn.under_sampling import EditedNearestNeighbours

logger = logging.getLogger(__name__)


def apply_smote(X, y, k_neighbors=5, random_state=42):
    """
    Apply SMOTE algorithm for oversampling the minority class.
    
    This function uses Synthetic Minority Over-sampling Technique (SMOTE) to
    create synthetic samples of the minority class, balancing the dataset.
    
    Args:
        X (numpy.ndarray): Feature matrix
        y (numpy.ndarray): Target labels
        k_neighbors (int): Number of nearest neighbors for SMOTE
        random_state (int): Random seed for reproducibility
        
    Returns:
        tuple: (resampled_X, resampled_y) - The resampled feature matrix and target labels
    """
    # Ensure inputs are numpy arrays
    X = np.array(X)
    y = np.array(y)
    
    # Check for non-numeric columns in X
    invalid_indices = []
    X_clean = X.copy()
    
    for i in range(X.shape[1]):
        try:
            # Try to convert column to float
            X_clean[:, i] = X_clean[:, i].astype(float)
        except (ValueError, TypeError):
            invalid_indices.append(i)
    
    if invalid_indices:
        logger.warning(f"Found {len(invalid_indices)} non-numeric feature columns. These will be removed before applying SMOTE.")
        # Create a mask to keep only numeric feature columns
        valid_cols = np.ones(X.shape[1], dtype=bool)
        valid_cols[invalid_indices] = False
        
        # Filter out non-numeric features
        X_clean = X_clean[:, valid_cols]
        logger.info(f"Original feature shape: {X.shape}, filtered feature shape: {X_clean.shape}")
    
    # Check for NaN values in y
    y_clean = y.copy()
    nan_mask = np.isnan(y_clean)
    if np.any(nan_mask):
        logger.warning(f"Found {np.sum(nan_mask)} NaN values in labels. These will be removed.")
        # Remove samples with NaN labels
        valid_indices = ~nan_mask.flatten()
        X_clean = X_clean[valid_indices]
        y_clean = y_clean[valid_indices]
        logger.info(f"After removing NaN labels - Features: {X_clean.shape}, Labels: {y_clean.shape}")
    
    # Ensure y is flattened
    if y_clean.ndim > 1:
        y_clean = y_clean.flatten()
    
    # Convert to binary classification problem if needed
    unique_labels = np.unique(y_clean)
    if len(unique_labels) > 2:
        logger.info(f"Converting multi-class problem with {len(unique_labels)} classes to binary")
        # Convert to binary: non-zero -> 1, zero -> 0
        y_clean = (y_clean > 0).astype(int)
    
    # Check if we have at least two classes for SMOTE
    unique_labels = np.unique(y_clean)
    if len(unique_labels) < 2:
        logger.warning(f"Only one class found in the labels: {unique_labels[0]}. SMOTE requires at least two classes.")
        return X, y
    
    # Check if we have enough samples of each class
    class_counts = np.bincount(y_clean.astype(int))
    minority_count = np.min(class_counts[class_counts > 0])
    
    if minority_count < k_neighbors + 1:
        logger.warning(f"Not enough samples ({minority_count}) in the minority class for SMOTE with k_neighbors={k_neighbors}.")
        logger.warning("Reducing k_neighbors to match available samples.")
        k_neighbors = max(1, minority_count - 1)
    
    # Check distribution
    pos_count = np.sum(y_clean > 0)
    neg_count = len(y_clean) - pos_count
    logger.info(f"Original class distribution - Positive: {pos_count}, Negative: {neg_count}")
    
    if pos_count == 0 or neg_count == 0:
        logger.warning("One of the classes has zero samples. Cannot apply SMOTE.")
        return X, y
    
    try:
        # Apply SMOTE
        smote = SMOTE(k_neighbors=k_neighbors, random_state=random_state)
        X_resampled, y_resampled = smote.fit_resample(X_clean, y_clean)
        
        # Log new distribution
        pos_count_new = np.sum(y_resampled > 0)
        neg_count_new = len(y_resampled) - pos_count_new
        logger.info(f"Resampled class distribution - Positive: {pos_count_new}, Negative: {neg_count_new}")
        logger.info(f"Resampled shapes - Features: {X_resampled.shape}, Labels: {y_resampled.shape}")
        
        # Reshape labels if needed to match original shape
        if y.ndim > 1:
            y_resampled = y_resampled.reshape(-1, 1)
        
        # Shuffle the data
        indices = np.arange(X_resampled.shape[0])
        np.random.seed(random_state)
        np.random.shuffle(indices)
        
        X_resampled = X_resampled[indices]
        y_resampled = y_resampled[indices]
        
        return X_resampled, y_resampled
        
    except Exception as e:
        logger.error(f"Error in SMOTE: {str(e)}")
        return X, y


def apply_smote_enn(X, y, k_neighbors=5, enn_k=3, random_state=42):
    """
    Apply SMOTE-ENN algorithm for handling imbalanced data.
    
    This function first applies SMOTE (Synthetic Minority Over-sampling Technique)
    to oversample the minority class, and then uses ENN (Edited Nearest Neighbors)
    to clean the resulting data.
    
    Args:
        X (numpy.ndarray): Feature matrix
        y (numpy.ndarray): Target labels
        k_neighbors (int): Number of nearest neighbors for SMOTE
        enn_k (int): Number of nearest neighbors for ENN
        random_state (int): Random seed for reproducibility
        
    Returns:
        tuple: (resampled_X, resampled_y) - The resampled feature matrix and target labels
    """
    # Ensure inputs are numpy arrays
    X = np.array(X)
    y = np.array(y)
    
    # Check for non-numeric columns in X
    invalid_indices = []
    X_clean = X.copy()
    
    for i in range(X.shape[1]):
        try:
            # Try to convert column to float
            X_clean[:, i] = X_clean[:, i].astype(float)
        except (ValueError, TypeError):
            invalid_indices.append(i)
    
    if invalid_indices:
        logger.warning(f"Found {len(invalid_indices)} non-numeric feature columns. These will be removed before applying SMOTE-ENN.")
        # Create a mask to keep only numeric feature columns
        valid_cols = np.ones(X.shape[1], dtype=bool)
        valid_cols[invalid_indices] = False
        
        # Filter out non-numeric features
        X_clean = X_clean[:, valid_cols]
        logger.info(f"Original feature shape: {X.shape}, filtered feature shape: {X_clean.shape}")
    
    # Check for NaN values in y
    y_clean = y.copy()
    nan_mask = np.isnan(y_clean)
    if np.any(nan_mask):
        logger.warning(f"Found {np.sum(nan_mask)} NaN values in labels. These will be removed.")
        # Remove samples with NaN labels
        valid_indices = ~nan_mask.flatten()
        X_clean = X_clean[valid_indices]
        y_clean = y_clean[valid_indices]
        logger.info(f"After removing NaN labels - Features: {X_clean.shape}, Labels: {y_clean.shape}")
    
    # Ensure y is flattened
    if y_clean.ndim > 1:
        y_clean = y_clean.flatten()
    
    # Convert to binary classification problem if needed
    unique_labels = np.unique(y_clean)
    if len(unique_labels) > 2:
        logger.info(f"Converting multi-class problem with {len(unique_labels)} classes to binary")
        # Convert to binary: non-zero -> 1, zero -> 0
        y_clean = (y_clean > 0).astype(int)
    
    # Check if we have at least two classes for SMOTE
    unique_labels = np.unique(y_clean)
    if len(unique_labels) < 2:
        logger.warning(f"Only one class found in the labels: {unique_labels[0]}. SMOTE-ENN requires at least two classes.")
        return X, y
    
    # Check if we have enough samples of each class
    class_counts = np.bincount(y_clean.astype(int))
    minority_count = np.min(class_counts[class_counts > 0])
    
    if minority_count < k_neighbors + 1:
        logger.warning(f"Not enough samples ({minority_count}) in the minority class for SMOTE with k_neighbors={k_neighbors}.")
        logger.warning("Reducing k_neighbors to match available samples.")
        k_neighbors = max(1, minority_count - 1)
    
    # Check distribution
    pos_count = np.sum(y_clean > 0)
    neg_count = len(y_clean) - pos_count
    logger.info(f"Original class distribution - Positive: {pos_count}, Negative: {neg_count}")
    
    if pos_count == 0 or neg_count == 0:
        logger.warning("One of the classes has zero samples. Cannot apply SMOTE-ENN.")
        return X, y
    
    try:
        # Apply SMOTE-ENN
        smote_enn = SMOTEENN(
            smote=dict(k_neighbors=k_neighbors),
            enn=dict(n_neighbors=enn_k),
            random_state=random_state
        )
        
        X_resampled, y_resampled = smote_enn.fit_resample(X_clean, y_clean)
        
        # Log new distribution
        pos_count_new = np.sum(y_resampled > 0)
        neg_count_new = len(y_resampled) - pos_count_new
        logger.info(f"Resampled class distribution - Positive: {pos_count_new}, Negative: {neg_count_new}")
        logger.info(f"Resampled shapes - Features: {X_resampled.shape}, Labels: {y_resampled.shape}")
        
        # Reshape labels if needed to match original shape
        if y.ndim > 1:
            y_resampled = y_resampled.reshape(-1, 1)
        
        # Shuffle the data
        indices = np.arange(X_resampled.shape[0])
        np.random.seed(random_state)
        np.random.shuffle(indices)
        
        X_resampled = X_resampled[indices]
        y_resampled = y_resampled[indices]
        
        return X_resampled, y_resampled
        
    except Exception as e:
        logger.error(f"Error in SMOTE-ENN: {str(e)}")
        return X, y


def apply_optimized_smote_enn(X, y, smote_ratio=0.5, k_neighbors=3, enn_k=3, random_state=42):
    """
    Apply optimized SMOTE-ENN algorithm with controlled synthetic sample ratio.
    
    This version extends SMOTE-ENN by allowing control over the ratio of positive
    samples to generate relative to the number of negative samples.
    
    Args:
        X (numpy.ndarray): Feature matrix
        y (numpy.ndarray): Target labels
        smote_ratio (float): Target ratio of positive to negative samples (0.0 to 1.0)
        k_neighbors (int): Number of nearest neighbors for SMOTE
        enn_k (int): Number of nearest neighbors for ENN
        random_state (int): Random seed for reproducibility
        
    Returns:
        tuple: (resampled_X, resampled_y) - The resampled feature matrix and target labels
    """
    # Ensure inputs are numpy arrays
    X = np.array(X)
    y = np.array(y)
    
    # Check for non-numeric columns in X
    invalid_indices = []
    X_clean = X.copy()
    
    for i in range(X.shape[1]):
        try:
            # Try to convert column to float
            X_clean[:, i] = X_clean[:, i].astype(float)
        except (ValueError, TypeError):
            invalid_indices.append(i)
    
    if invalid_indices:
        logger.warning(f"Found {len(invalid_indices)} non-numeric feature columns. These will be removed before applying optimized SMOTE-ENN.")
        # Create a mask to keep only numeric feature columns
        valid_cols = np.ones(X.shape[1], dtype=bool)
        valid_cols[invalid_indices] = False
        
        # Filter out non-numeric features
        X_clean = X_clean[:, valid_cols]
        logger.info(f"Original feature shape: {X.shape}, filtered feature shape: {X_clean.shape}")
    
    # Check for NaN values in y
    y_clean = y.copy()
    nan_mask = np.isnan(y_clean)
    if np.any(nan_mask):
        logger.warning(f"Found {np.sum(nan_mask)} NaN values in labels. These will be removed.")
        # Remove samples with NaN labels
        valid_indices = ~nan_mask.flatten()
        X_clean = X_clean[valid_indices]
        y_clean = y_clean[valid_indices]
        logger.info(f"After removing NaN labels - Features: {X_clean.shape}, Labels: {y_clean.shape}")
    
    # Ensure y is flattened if needed for binary classification
    if y_clean.ndim > 1:
        # For multi-dimensional labels, convert to binary problem
        if y_clean.shape[1] >= 6:
            binary_y = np.any(y_clean[:, :6] > 0, axis=1).astype(int)
        else:
            binary_y = np.any(y_clean > 0, axis=1).astype(int)
    else:
        # One-dimensional case
        binary_y = (y_clean > 0).astype(int)
    
    # Check class distribution
    class_counts = np.bincount(binary_y.astype(int))
    logger.info(f"Original class distribution: {dict(Counter(binary_y))}")
    
    # Ensure enough samples for both classes
    if len(class_counts) < 2 or 0 in class_counts:
        logger.warning("Not enough samples of each class. Cannot apply SMOTE-ENN.")
        return X_clean, y_clean
    
    # Adjust neighbors if necessary
    if class_counts[1] <= k_neighbors:
        k_neighbors = max(1, class_counts[1] - 1)
        logger.warning(f"Adjusted SMOTE k_neighbors to {k_neighbors} due to small sample size")
    
    if class_counts[0] <= enn_k:
        enn_k = max(1, class_counts[0] - 1)
        logger.warning(f"Adjusted ENN n_neighbors to {enn_k} due to small sample size")
    
    # Calculate SMOTE sampling strategy based on ratio
    if smote_ratio < 1.0:
        # Calculate number of negative samples
        n_neg = class_counts[0]
        # Calculate target number of positive samples
        n_target_pos = int(n_neg * smote_ratio)
        # Current positive sample count
        n_current_pos = class_counts[1]
        
        # Set sampling strategy only if we need to generate more positives
        if n_target_pos > n_current_pos:
            sampling_strategy = {1: n_target_pos}
            logger.info(f"Setting SMOTE target positive samples to {n_target_pos}")
        else:
            # If we already have enough positives, use default strategy
            sampling_strategy = 'auto'
            logger.info("Using auto sampling strategy (already enough positive samples)")
    else:
        sampling_strategy = 'auto'
    
    try:
        # Apply SMOTE-ENN with optimized ratio
        smote_enn = SMOTEENN(
            sampling_strategy=sampling_strategy,
            random_state=random_state,
            smote=SMOTE(k_neighbors=k_neighbors),
            enn=EditedNearestNeighbours(n_neighbors=enn_k)
        )
        
        X_resampled, y_binary_resampled = smote_enn.fit_resample(X_clean, binary_y)
        logger.info(f"Resampled class distribution: {dict(Counter(y_binary_resampled))}")
        
        # Handle multi-label case
        if y.ndim > 1:
            # Generate labels for resampled positive samples from original positives
            pos_indices = np.where(binary_y == 1)[0]
            if len(pos_indices) > 0:
                pos_labels = y_clean[pos_indices]
                
                # Create new label array
                y_resampled = np.zeros((len(y_binary_resampled), y_clean.shape[1]), dtype=y_clean.dtype)
                
                # Assign labels to new positive samples (randomly select from original positives)
                new_pos_indices = np.where(y_binary_resampled == 1)[0]
                if len(new_pos_indices) > 0 and len(pos_labels) > 0:
                    # Allow replacement if we have fewer original positives than resampled
                    replace = len(pos_labels) < len(new_pos_indices)
                    random_pos_labels = pos_labels[np.random.choice(len(pos_labels), len(new_pos_indices), replace=replace)]
                    y_resampled[new_pos_indices] = random_pos_labels
            else:
                # No positive samples, keep all zeros
                y_resampled = np.zeros((len(y_binary_resampled), y_clean.shape[1]), dtype=y_clean.dtype)
        else:
            # For one-dimensional labels, keep binary labels
            y_resampled = y_binary_resampled
        
        # Shuffle data
        indices = np.random.permutation(len(X_resampled))
        X_resampled = X_resampled[indices]
        y_resampled = y_resampled[indices]
        
        return X_resampled, y_resampled
        
    except Exception as e:
        logger.error(f"Error in optimized SMOTE-ENN: {str(e)}")
        # Try fallback to regular SMOTE
        try:
            logger.warning("Falling back to SMOTE only")
            smote = SMOTE(
                sampling_strategy=sampling_strategy,
                k_neighbors=k_neighbors,
                random_state=random_state
            )
            X_resampled, y_binary_resampled = smote.fit_resample(X_clean, binary_y)
            
            # Handle multi-label case (same logic as above)
            if y.ndim > 1:
                pos_indices = np.where(binary_y == 1)[0]
                if len(pos_indices) > 0:
                    pos_labels = y_clean[pos_indices]
                    y_resampled = np.zeros((len(y_binary_resampled), y_clean.shape[1]), dtype=y_clean.dtype)
                    new_pos_indices = np.where(y_binary_resampled == 1)[0]
                    if len(new_pos_indices) > 0 and len(pos_labels) > 0:
                        replace = len(pos_labels) < len(new_pos_indices)
                        random_pos_labels = pos_labels[np.random.choice(len(pos_labels), len(new_pos_indices), replace=replace)]
                        y_resampled[new_pos_indices] = random_pos_labels
                else:
                    y_resampled = np.zeros((len(y_binary_resampled), y_clean.shape[1]), dtype=y_clean.dtype)
            else:
                y_resampled = y_binary_resampled
                
            # Shuffle data
            indices = np.random.permutation(len(X_resampled))
            X_resampled = X_resampled[indices]
            y_resampled = y_resampled[indices]
            
            return X_resampled, y_resampled
            
        except Exception as e2:
            logger.error(f"SMOTE fallback also failed: {str(e2)}")
            return X_clean, y_clean 