#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
为多任务学习创建合成测试数据集

该脚本生成一个模拟的电子商务数据集，用于测试和开发多任务学习模型。
它创建包含以下内容的合成数据:
- 类别特征
- 数值特征
- 点击和转化标签
"""

import os
import pandas as pd
import numpy as np
import argparse
import logging
from sklearn.preprocessing import StandardScaler

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_synthetic_data(dataset_name='NL', num_samples=50000, data_path='./data'):
    """
    创建合成的多任务学习数据集
    
    Args:
        dataset_name: 数据集名称后缀 (默认: 'NL')
        num_samples: 要生成的样本数 (默认: 50000)
        data_path: 保存数据的目录路径 (默认: './data')
    
    Returns:
        训练和测试数据的路径
    """
    logger.info(f"生成合成AliExpress_{dataset_name}数据集...")
    
    # 创建目标目录
    target_dir = os.path.join(data_path, f'AliExpress_{dataset_name}')
    os.makedirs(target_dir, exist_ok=True)
    
    # 定义特征数量
    num_categorical_features = 15  # 类别特征数量
    num_numerical_features = 50    # 数值特征数量
    
    # 生成合成数据
    np.random.seed(42)
    
    # 生成类别特征
    categorical_data = np.random.randint(0, 10, size=(num_samples, num_categorical_features))
    
    # 生成数值特征
    numerical_data = np.random.randn(num_samples, num_numerical_features)
    
    # 生成特征名称
    categorical_cols = ['search_id'] + [f'categorical_{i}' for i in range(1, num_categorical_features)]
    numerical_cols = [f'numerical_{i}' for i in range(1, num_numerical_features + 1)]
    
    # 生成点击和转化标签
    # 使用逻辑函数基于特征生成概率
    click_prob = 1 / (1 + np.exp(-0.5 * numerical_data[:, 0] - 0.3 * numerical_data[:, 1] + 0.2 * categorical_data[:, 0]))
    conversion_prob = click_prob * (1 / (1 + np.exp(-0.4 * numerical_data[:, 2] - 0.2 * numerical_data[:, 3] + 0.1 * categorical_data[:, 1])))
    
    clicks = (np.random.random(num_samples) < click_prob).astype(int)
    conversions = np.zeros_like(clicks)
    # 只有点击的样本才能有转化
    clicked_indices = np.where(clicks == 1)[0]
    conversions[clicked_indices] = (np.random.random(len(clicked_indices)) < conversion_prob[clicked_indices]).astype(int)
    
    # 创建DataFrame
    data = {}
    for i, col in enumerate(categorical_cols):
        data[col] = categorical_data[:, i]
    
    for i, col in enumerate(numerical_cols):
        data[col] = numerical_data[:, i]
    
    data['click'] = clicks
    data['conversion'] = conversions
    
    df = pd.DataFrame(data)
    
    # 将数据分为训练集和测试集
    train_ratio = 0.8
    train_size = int(num_samples * train_ratio)
    
    df_train = df[:train_size]
    df_test = df[train_size:]
    
    # 保存数据
    train_path = os.path.join(target_dir, 'train.csv')
    test_path = os.path.join(target_dir, 'test.csv')
    
    df_train.to_csv(train_path, index=False)
    df_test.to_csv(test_path, index=False)
    
    # 生成元数据
    click_rate = df['click'].mean()
    conversion_rate = df['conversion'].mean()
    
    metadata = {
        'num_train': len(df_train),
        'num_test': len(df_test),
        'categorical_cols': categorical_cols,
        'numerical_cols': numerical_cols,
        'click_rate': click_rate,
        'conversion_rate': conversion_rate
    }
    
    metadata_path = os.path.join(target_dir, 'metadata.csv')
    with open(metadata_path, 'w') as f:
        for key, value in metadata.items():
            if isinstance(value, list):
                f.write(f"{key}:{','.join(value)}\n")
            else:
                f.write(f"{key}:{value}\n")
    
    logger.info(f"合成数据生成完成!")
    logger.info(f"训练集大小: {len(df_train)}, 测试集大小: {len(df_test)}")
    logger.info(f"点击率: {click_rate:.4f}, 转化率: {conversion_rate:.4f}")
    logger.info(f"已保存数据到: {target_dir}")
    
    return train_path, test_path, metadata

def main():
    parser = argparse.ArgumentParser(description='创建合成多任务学习数据集')
    parser.add_argument('--dataset_name', default='NL', choices=['NL', 'ES', 'FR', 'US'],
                       help='数据集名称后缀 (默认: NL)')
    parser.add_argument('--num_samples', type=int, default=50000,
                       help='要生成的样本数 (默认: 50000)')
    parser.add_argument('--data_path', default='./data',
                       help='保存数据的目录路径 (默认: ./data)')
    args = parser.parse_args()
    
    try:
        train_path, test_path, metadata = create_synthetic_data(
            dataset_name=args.dataset_name,
            num_samples=args.num_samples,
            data_path=args.data_path
        )
        
        logger.info(f"合成数据集已成功创建!")
        logger.info(f"训练集: {train_path}")
        logger.info(f"测试集: {test_path}")
        
    except Exception as e:
        logger.error(f"创建合成数据集时出错: {e}")
        raise

if __name__ == "__main__":
    main() 