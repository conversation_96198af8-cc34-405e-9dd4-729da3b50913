"""
Feature builder module for conversion rate prediction models.
"""
import numpy as np
import tensorflow as tf
import logging
from src.features.imbalance import create_balanced_dataset, oversample_positive_samples


class FeatureBuilder:
    """Feature builder class to convert dataframes into model inputs."""
    
    def __init__(self):
        """Initialize the feature builder."""
        self.logger = logging.getLogger(__name__)
    
    def generate_dataset(self, 
                        df_input, 
                        raw_feature,
                        label=None, 
                        batch_size=8192,
                        mask_label=None,
                        predict_method="6m",
                        use_balanced_sampling=False,
                        pos_ratio=0.5,
                        use_oversampling=False,
                        pos_weight=5.0,
                        use_esmm=False,
                        use_esm2=False,
                        esm2_task_names=('test_drive_output', 'appointment_output', 'purchase_output')):
        """
        Generate TensorFlow dataset from dataframe.
        
        Args:
            df_input (pd.DataFrame): Input dataframe.
            raw_feature (dict): Raw feature definitions.
            label (str, optional): Label column name.
            batch_size (int): Batch size for training.
            mask_label (str, optional): Mask label column name.
            predict_method (str): Prediction method ("1m" or "6m").
            use_balanced_sampling (bool): Whether to use balanced batch sampling.
            pos_ratio (float): Ratio of positive samples in each batch when using balanced sampling.
            use_oversampling (bool): Whether to use oversampling for positive samples.
            pos_weight (float): Weight for positive sample oversampling.
            use_esmm (bool): Whether to use ESMM model structure.
            use_esm2 (bool): Whether to use ESM2 model structure.
            esm2_task_names (tuple/list): Names of ESM2 tasks in order.
            
        Returns:
            tf.data.Dataset or dict: Training dataset or feature dict for inference.
        """
        self.logger.info(f"Generating {'inference features' if label is None else 'training dataset'}")
        
        # Create feature dictionary
        feature_dict = {}
        for feature_name, feature_info in raw_feature.items():
            feature_type = feature_info.get("type", "table")
            feature_dtype = feature_info.get("dtype", "StringLookup")
            
            # Handle different types of features
            if feature_type == "VarLen" or feature_dtype in ["Dense", "Embedding"]:
                feature_dict[feature_name] = np.array(df_input[feature_name].tolist())
            else:
                feature_dict[feature_name] = np.array(df_input[feature_name])
        
        # For inference, return just the feature dictionary
        if label is None:
            return feature_dict
        
        # For training, create full dataset with labels
        else:
            # Convert labels to numpy array
            label_array = np.array(df_input[label].tolist()).astype(np.float32)
            
            # Create appropriate dataset based on mask presence, prediction method, and model type
            if use_esm2:
                # For ESM2, we need to create multiple ordered tasks labeled as defined in esm2_task_names
                # Purchase label is usually the final task
                purchase_label = label_array
                
                # If the purchase_label is a multi-dimensional array (e.g., 6 months prediction),
                # convert it to a binary value indicating if purchase happens in any month
                if len(purchase_label.shape) > 1 and purchase_label.shape[1] > 1:
                    # For ESM2 we just need binary indicators, so we check if any month has a purchase
                    # Convert to binary: 1 if any month has purchase, 0 otherwise
                    purchase_label = np.any(purchase_label > 0, axis=1).astype(np.float32)
                
                # Create dictionary with all task labels
                labels_dict = {}
                
                # 初始化一系列从第一个任务到最后一个任务的标签
                for i, task_name in enumerate(esm2_task_names):
                    if task_name == 'purchase_output' or i == len(esm2_task_names) - 1:
                        # 购买(最终任务)标签
                        labels_dict[task_name] = purchase_label
                    elif task_name == 'test_drive_output':
                        # 试驾标签：假设所有购买的用户都进行了试驾，加上一些随机试驾
                        test_drive_label = np.logical_or(
                            purchase_label > 0,
                            np.random.random(purchase_label.shape) < 0.4  # 比购买概率高的试驾概率
                        ).astype(np.float32)
                        labels_dict[task_name] = test_drive_label
                    elif task_name == 'appointment_output':
                        # 预约标签：假设所有试驾的用户都进行了预约，加上一些随机预约
                        if 'test_drive_output' in labels_dict:
                            # 如果已有试驾标签，使用它作为基础
                            test_drive_label = labels_dict['test_drive_output']
                            appointment_label = np.logical_or(
                                test_drive_label > 0,
                                np.random.random(test_drive_label.shape) < 0.3  # 额外预约概率
                            ).astype(np.float32)
                        else:
                            # 如果没有试驾标签，直接从购买标签生成
                            appointment_label = np.logical_or(
                                purchase_label > 0,
                                np.random.random(purchase_label.shape) < 0.5  # 较高的预约概率
                            ).astype(np.float32)
                        labels_dict[task_name] = appointment_label
                    else:
                        # 对其他任务名称，根据序列位置创建条件概率
                        if i == 0:  # 第一个任务
                            # 第一个任务的样本更多
                            labels_dict[task_name] = (np.random.random(purchase_label.shape) < 0.6).astype(np.float32)
                        else:  # 后续任务
                            # 后续任务受前一个任务影响
                            prev_task = esm2_task_names[i-1]
                            prev_label = labels_dict[prev_task]
                            # 基于前一个任务的条件概率
                            rand_prob = np.random.random(purchase_label.shape) < 0.7
                            labels_dict[task_name] = np.logical_and(
                                prev_label > 0,
                                rand_prob
                            ).astype(np.float32)
                
                # 创建数据集
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, labels_dict))
                
                # 应用平衡采样（如果请求的话）
                if use_balanced_sampling:
                    self.logger.info(f"Applying balanced batch sampling with positive ratio {pos_ratio}")
                    # 对于ESM2，我们根据最终任务的标签（通常是购买）进行平衡
                    
                    # 提取正负样本
                    final_task = esm2_task_names[-1]
                    final_label = labels_dict[final_task]
                    pos_indices = np.where(final_label > 0)[0]
                    neg_indices = np.where(final_label <= 0)[0]
                    
                    # 计算批次组成
                    pos_per_batch = int(batch_size * pos_ratio)
                    neg_per_batch = batch_size - pos_per_batch
                    
                    # 为正负样本创建数据集
                    pos_features = {k: v[pos_indices] for k, v in feature_dict.items()}
                    pos_labels = {k: v[pos_indices] for k, v in labels_dict.items()}
                    
                    neg_features = {k: v[neg_indices] for k, v in feature_dict.items()}
                    neg_labels = {k: v[neg_indices] for k, v in labels_dict.items()}
                    
                    pos_ds = tf.data.Dataset.from_tensor_slices((pos_features, pos_labels))
                    neg_ds = tf.data.Dataset.from_tensor_slices((neg_features, neg_labels))
                    
                    # 采样和交织
                    pos_ds = pos_ds.shuffle(len(pos_indices)).repeat()
                    neg_ds = neg_ds.shuffle(len(neg_indices)).repeat()
                    
                    pos_batches = pos_ds.batch(pos_per_batch)
                    neg_batches = neg_ds.batch(neg_per_batch)
                    
                    # 合并数据集
                    ds = tf.data.Dataset.zip((pos_batches, neg_batches)).map(
                        lambda pos, neg: (
                            {k: tf.concat([pos[0][k], neg[0][k]], axis=0) for k in pos[0]},
                            {k: tf.concat([pos[1][k], neg[1][k]], axis=0) for k in pos[1]}
                        )
                    )
                else:
                    ds = ds.shuffle(10000).batch(batch_size)
            elif use_esmm:
                # For ESMM, we need to create two labels: test_drive_output and purchase_output
                # Purchase label is the original label
                purchase_label = label_array
                
                # If the purchase_label is a multi-dimensional array (e.g., 6 months prediction),
                # convert it to a binary value indicating if purchase happens in any month
                if len(purchase_label.shape) > 1 and purchase_label.shape[1] > 1:
                    # For ESMM we just need binary indicators, so we check if any month has a purchase
                    # Convert to binary: 1 if any month has purchase, 0 otherwise
                    purchase_label = np.any(purchase_label > 0, axis=1).astype(np.float32)
                
                # Test drive label is a proxy created from purchase label
                # Assuming test drive is a prerequisite for purchase + some additional test drives
                # This is a simplified simulation; in a real case, you would use actual test drive data
                test_drive_label = np.logical_or(
                    purchase_label > 0,
                    np.random.random(purchase_label.shape) < 0.3  # Additional test drives
                ).astype(np.float32)
                
                # Create dictionary of labels for ESMM
                labels_dict = {
                    'test_drive_output': test_drive_label,
                    'purchase_output': purchase_label
                }
                
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, labels_dict))
                
                # Apply balanced sampling if requested (but for ESMM we use the purchase labels for balancing)
                if use_balanced_sampling:
                    self.logger.info(f"Applying balanced batch sampling with positive ratio {pos_ratio}")
                    # For ESMM, create custom balanced dataset based on purchase labels
                    # This requires a slightly different approach since our labels are now dictionaries
                    
                    # Extract positive and negative samples
                    pos_indices = np.where(purchase_label > 0)[0]
                    neg_indices = np.where(purchase_label <= 0)[0]
                    
                    # Calculate batch composition
                    pos_per_batch = int(batch_size * pos_ratio)
                    neg_per_batch = batch_size - pos_per_batch
                    
                    # Create datasets for positive and negative samples
                    pos_features = {k: v[pos_indices] for k, v in feature_dict.items()}
                    pos_labels = {
                        'test_drive_output': test_drive_label[pos_indices],
                        'purchase_output': purchase_label[pos_indices]
                    }
                    
                    neg_features = {k: v[neg_indices] for k, v in feature_dict.items()}
                    neg_labels = {
                        'test_drive_output': test_drive_label[neg_indices],
                        'purchase_output': purchase_label[neg_indices]
                    }
                    
                    pos_ds = tf.data.Dataset.from_tensor_slices((pos_features, pos_labels))
                    neg_ds = tf.data.Dataset.from_tensor_slices((neg_features, neg_labels))
                    
                    # Sample and interleave
                    pos_ds = pos_ds.shuffle(len(pos_indices)).repeat()
                    neg_ds = neg_ds.shuffle(len(neg_indices)).repeat()
                    
                    pos_batches = pos_ds.batch(pos_per_batch)
                    neg_batches = neg_ds.batch(neg_per_batch)
                    
                    # Zip datasets together
                    ds = tf.data.Dataset.zip((pos_batches, neg_batches)).map(
                        lambda pos, neg: (
                            {k: tf.concat([pos[0][k], neg[0][k]], axis=0) for k in pos[0]},
                            {k: tf.concat([pos[1][k], neg[1][k]], axis=0) for k in pos[1]}
                        )
                    )
                else:
                    ds = ds.shuffle(10000).batch(batch_size)
            elif mask_label is None or predict_method == "1m":
                # No mask label case
                if use_oversampling:
                    self.logger.info(f"Applying positive sample oversampling with weight {pos_weight}")
                    feature_dict, label_array = oversample_positive_samples(feature_dict, label_array, pos_weight)
                    
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, label_array))
                
                # Apply balanced sampling if requested
                if use_balanced_sampling:
                    self.logger.info(f"Applying balanced batch sampling with positive ratio {pos_ratio}")
                    ds = create_balanced_dataset(ds, pos_ratio=pos_ratio, batch_size=batch_size)
                else:
                    ds = ds.shuffle(10000).batch(batch_size)
            else:
                # With mask label case
                mask_array = np.array(df_input[mask_label].tolist()).astype(np.float32)
                combined_labels = np.concatenate([label_array, mask_array], axis=-1)
                
                if use_oversampling:
                    self.logger.info(f"Applying positive sample oversampling with weight {pos_weight}")
                    feature_dict, combined_labels = oversample_positive_samples(feature_dict, combined_labels, pos_weight)
                
                ds = tf.data.Dataset.from_tensor_slices((feature_dict, combined_labels))
                
                # Apply balanced sampling if requested
                if use_balanced_sampling:
                    self.logger.info(f"Applying balanced batch sampling with positive ratio {pos_ratio}")
                    ds = create_balanced_dataset(ds, pos_ratio=pos_ratio, batch_size=batch_size)
                else:
                    ds = ds.shuffle(10000).batch(batch_size)
            
            self.logger.info("Dataset creation complete")
            
            # Apply prefetching for optimal performance (only needed if not using balanced sampling)
            if not use_balanced_sampling or use_esmm or use_esm2:
                ds = ds.prefetch(batch_size)
            
            return ds
    
    def split_train_test(self, df, test_dates):
        """
        Split dataset into training and test sets based on dates.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            test_dates (list): List of dates to use for testing.
            
        Returns:
            tuple: (Training dataframe, Testing dataframe)
        """
        self.logger.info(f"Splitting dataset using test dates: {test_dates}")
        
        # Shuffle training data
        df_shuffled = df.sample(frac=1) 
        
        # Split based on dates
        df_train = df_shuffled[~df_shuffled["datetime"].isin(test_dates)]
        df_test = df_shuffled[df_shuffled["datetime"].isin(test_dates)]
        
        # Sort training data by date for temporal consistency
        df_train = df_train.sort_values("datetime", ascending=True)
        
        self.logger.info(f"Split complete. Training set: {len(df_train)}, Test set: {len(df_test)}")
        
        return df_train, df_test 
    
    def engineer_conversion_features(self, df):
        """
        创建专门针对转化率预测的特殊特征
        
        Args:
            df (pd.DataFrame): 输入数据框
            
        Returns:
            pd.DataFrame: 增强了特征的数据框
        """
        self.logger.info("Creating specialized conversion prediction features")
        
        # 创建新的数据框以避免修改原始数据
        enhanced_df = df.copy()
        
        # 1. 行为密度特征 - 捕捉用户活跃程度
        if all(col in df.columns for col in ['user_core_action_cnt_30d', 'user_core_action_cnt_7d']):
            enhanced_df['action_density_week'] = df['user_core_action_cnt_7d'] / 7
            enhanced_df['action_density_month'] = df['user_core_action_cnt_30d'] / 30
        
        # 2. 行为加速度 - 表示用户活动频率变化
        if all(col in df.columns for col in ['user_core_action_cnt_30d', 'user_core_action_cnt_60d']):
            enhanced_df['action_acceleration'] = df['user_core_action_cnt_30d'] / (
                df['user_core_action_cnt_60d'] - df['user_core_action_cnt_30d'] + 1
            )
        
        # 3. 汽车相关行为比例 - 衡量用户对车辆的特定兴趣
        if all(col in df.columns for col in ['user_car_core_action_cnt_30d', 'user_core_action_cnt_30d']):
            enhanced_df['car_specific_ratio'] = df['user_car_core_action_cnt_30d'] / (
                df['user_core_action_cnt_30d'] + 1
            )
        
        # 4. 近期转化相关行为 - 捕捉与购买直接相关的行为
        conversion_related_columns = [
            'user_core_view_finance_calc_nioapp_7d_cnt',
            'user_core_visit_veh_cgf_nio_7d_cnt',
            'user_core_book_td_nio_30d_cnt',
            'user_core_exp_td_nio_30d_cnt'
        ]
        if all(col in df.columns for col in conversion_related_columns):
            # 计算购买意向分数
            enhanced_df['purchase_intent_score'] = (
                df['user_core_view_finance_calc_nioapp_7d_cnt'] * 3 + 
                df['user_core_visit_veh_cgf_nio_7d_cnt'] * 2 + 
                df['user_core_book_td_nio_30d_cnt'] * 4 + 
                df['user_core_exp_td_nio_30d_cnt'] * 5
            )
        
        # 5. 转化路径特征
        if 'user_core_action_code_seq' in df.columns and isinstance(df['user_core_action_code_seq'].iloc[0], list):
            # 检查是否包含关键转化路径行为
            def check_conversion_path(action_seq):
                # 关键行为路径: 配置车辆 -> 预约试驾 -> 体验试驾 -> 支付意向金
                key_actions = [
                    'visit_veh_cgf_nio', 
                    'book_td_nio', 
                    'exp_td_nio', 
                    'pay_ncar_intention_nio'
                ]
                if not isinstance(action_seq, list):
                    return 0
                    
                score = 0
                found_actions = set()
                
                for action in action_seq:
                    if action in key_actions:
                        found_actions.add(action)
                        
                # 根据关键行为路径的完整性评分
                score = len(found_actions) / len(key_actions)
                
                # 如果路径完整，给予额外奖励
                if len(found_actions) == len(key_actions):
                    score *= 1.5
                    
                return score
            
            enhanced_df['conversion_path_score'] = df['user_core_action_code_seq'].apply(check_conversion_path)
        
        self.logger.info(f"Created {len(enhanced_df.columns) - len(df.columns)} new specialized features")
        return enhanced_df 