#!/usr/bin/env python3
"""
智能修复embedding维度不匹配问题
- 自动检测可能的训练输出目录
- 生成正确的extract命令
- 提供配置一致性检查
"""

import json
import os
import sys
import argparse
from pathlib import Path
import pickle
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def find_potential_training_dirs():
    """查找可能的训练输出目录"""
    potential_dirs = []
    search_paths = [
        Path('.'),  # 当前目录
        Path('..'),  # 上级目录
        Path.home() / 'embedding_outputs',  # 用户目录
        Path('/tmp'),  # 临时目录
    ]
    
    for base_path in search_paths:
        if not base_path.exists():
            continue
            
        try:
            for item in base_path.rglob('*'):
                if not item.is_dir():
                    continue
                    
                # 检查是否包含训练输出文件
                files_in_dir = [f.name for f in item.iterdir() if f.is_file()]
                
                score = 0
                if 'training_config.json' in files_in_dir:
                    score += 10
                if 'trained_model.keras' in files_in_dir or 'final_model.keras' in files_in_dir:
                    score += 8
                if 'all_vocabs.pkl' in files_in_dir:
                    score += 6
                if 'static_preprocessors.pkl' in files_in_dir:
                    score += 4
                if 'processed_static_feature_names.json' in files_in_dir:
                    score += 4
                if any(f.endswith('.h5') for f in files_in_dir):
                    score += 2
                
                if score >= 6:  # 至少有一些训练文件
                    potential_dirs.append((item, score, files_in_dir))
        except (PermissionError, OSError):
            continue
    
    # 按得分排序
    potential_dirs.sort(key=lambda x: x[1], reverse=True)
    return potential_dirs

def analyze_training_dir(dir_path, files_list):
    """分析训练目录"""
    analysis = {
        'path': dir_path,
        'has_config': 'training_config.json' in files_list,
        'has_model': any(f in files_list for f in ['trained_model.keras', 'final_model.keras']),
        'has_vocabs': 'all_vocabs.pkl' in files_list,
        'has_static_preprocessors': 'static_preprocessors.pkl' in files_list,
        'has_static_names': 'processed_static_feature_names.json' in files_list,
        'config': None,
        'static_dim': 0,
        'vocab_info': {}
    }
    
    # 读取配置
    if analysis['has_config']:
        try:
            with open(dir_path / 'training_config.json', 'r') as f:
                analysis['config'] = json.load(f)
        except Exception as e:
            logging.warning(f"Failed to read config from {dir_path}: {e}")
    
    # 读取静态特征信息
    if analysis['has_static_names']:
        try:
            with open(dir_path / 'processed_static_feature_names.json', 'r') as f:
                static_names = json.load(f)
                analysis['static_dim'] = len(static_names)
        except Exception as e:
            logging.warning(f"Failed to read static names from {dir_path}: {e}")
    
    # 读取词汇表信息
    if analysis['has_vocabs']:
        try:
            with open(dir_path / 'all_vocabs.pkl', 'rb') as f:
                vocabs = pickle.load(f)
                analysis['vocab_info'] = {k: len(v[0]) for k, v in vocabs.items()}
        except Exception as e:
            logging.warning(f"Failed to read vocabs from {dir_path}: {e}")
    
    return analysis

def generate_extract_command(analysis, test_date="20240531", output_name=None):
    """根据分析结果生成extract命令"""
    if not analysis['config']:
        return None
    
    config = analysis['config']
    output_dir = output_name or analysis['path'].name
    
    cmd_parts = [
        "python embedding/embedding_model_train.py",
        f"--output-dir {output_dir}",
        f"--mode extract_embeddings", 
        f"--test-date {test_date}"
    ]
    
    # 添加基本配置
    maxlen = config.get('MAXLEN', 64)
    cmd_parts.append(f"--maxlen {maxlen}")
    
    # 检查是否使用增强模型
    if config.get('USE_ENHANCED_MODEL', False):
        cmd_parts.append("--use-enhanced-model")
        
        # 分析额外序列特征
        seq_map = config.get('MASTER_SEQUENCE_FEATURES_MAP', {})
        additional_seqs = []
        for seq_key in seq_map.keys():
            if seq_key not in ['action_seq', 'day_seq']:
                additional_seqs.append(seq_key)
        
        if additional_seqs:
            cmd_parts.append(f"--additional-sequences {' '.join(additional_seqs)}")
    
    # 检查是否包含静态特征
    if config.get('INCLUDE_STATIC_FEATURES', False):
        cmd_parts.append("--include-static-features")
    
    return " \\\n  ".join(cmd_parts)

def create_config_checker():
    """创建配置检查脚本"""
    checker_code = '''#!/usr/bin/env python3
"""
检查extract_embeddings命令的配置一致性
"""
import json
import sys
from pathlib import Path

def check_config_consistency(training_dir, args):
    """检查配置一致性"""
    config_path = Path(training_dir) / 'training_config.json'
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    issues = []
    
    # 检查基本参数
    if hasattr(args, 'maxlen') and config.get('MAXLEN') != args.maxlen:
        issues.append(f"MAXLEN不匹配: 训练={config.get('MAXLEN')}, 推理={args.maxlen}")
    
    if hasattr(args, 'use_enhanced_model') and config.get('USE_ENHANCED_MODEL') != args.use_enhanced_model:
        issues.append(f"增强模型设置不匹配: 训练={config.get('USE_ENHANCED_MODEL')}, 推理={args.use_enhanced_model}")
    
    if hasattr(args, 'include_static_features') and config.get('INCLUDE_STATIC_FEATURES') != args.include_static_features:
        issues.append(f"静态特征设置不匹配: 训练={config.get('INCLUDE_STATIC_FEATURES')}, 推理={args.include_static_features}")
    
    if issues:
        print("❌ 发现配置不一致:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 配置检查通过")
        return True

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--training-dir", required=True)
    parser.add_argument("--maxlen", type=int)
    parser.add_argument("--use-enhanced-model", action="store_true")  
    parser.add_argument("--include-static-features", action="store_true")
    args = parser.parse_args()
    
    success = check_config_consistency(args.training_dir, args)
    sys.exit(0 if success else 1)
'''
    
    with open('embedding/config_checker.py', 'w') as f:
        f.write(checker_code)
    os.chmod('embedding/config_checker.py', 0o755)

def main():
    parser = argparse.ArgumentParser(description="智能修复embedding维度不匹配问题")
    parser.add_argument("--test-date", default="20240531", help="测试数据日期")
    parser.add_argument("--auto-fix", action="store_true", help="自动选择最佳训练目录")
    parser.add_argument("--search-path", help="指定搜索路径")
    args = parser.parse_args()
    
    print("🔍 智能Embedding问题修复工具")
    print("=" * 50)
    
    # 1. 查找训练目录
    print("1. 查找可能的训练输出目录...")
    potential_dirs = find_potential_training_dirs()
    
    if not potential_dirs:
        print("❌ 未找到任何训练输出目录")
        print("\n💡 建议:")
        print("1. 确保已经完成模型训练")
        print("2. 检查训练输出是否保存在正确位置")
        print("3. 手动指定训练输出目录路径")
        return
    
    print(f"找到 {len(potential_dirs)} 个潜在目录:")
    for i, (path, score, files) in enumerate(potential_dirs):
        print(f"  {i+1}. {path} (得分: {score})")
        key_files = [f for f in files if f in ['training_config.json', 'trained_model.keras', 'all_vocabs.pkl']]
        print(f"     关键文件: {', '.join(key_files)}")
    
    # 2. 选择目录
    if args.auto_fix or len(potential_dirs) == 1:
        selected_dir, score, files = potential_dirs[0]
        print(f"\n✅ 自动选择: {selected_dir}")
    else:
        try:
            choice = int(input(f"\n请选择目录 (1-{len(potential_dirs)}): ")) - 1
            selected_dir, score, files = potential_dirs[choice]
        except (ValueError, IndexError):
            print("❌ 无效选择")
            return
    
    # 3. 分析目录
    print(f"\n2. 分析训练配置...")
    analysis = analyze_training_dir(selected_dir, files)
    
    if not analysis['config']:
        print("❌ 无法读取训练配置")
        return
    
    config = analysis['config']
    print(f"  ✅ 模型类型: {'增强模型' if config.get('USE_ENHANCED_MODEL') else '标准模型'}")
    print(f"  ✅ 静态特征: {'是' if config.get('INCLUDE_STATIC_FEATURES') else '否'} (维度: {analysis['static_dim']})")
    print(f"  ✅ 序列长度: {config.get('MAXLEN', 64)}")
    print(f"  ✅ 序列特征: {list(config.get('MASTER_SEQUENCE_FEATURES_MAP', {}).keys())}")
    if analysis['vocab_info']:
        print(f"  ✅ 词汇表大小: {analysis['vocab_info']}")
    
    # 4. 生成命令
    print(f"\n3. 生成修复命令...")
    extract_cmd = generate_extract_command(analysis, args.test_date, selected_dir.name)
    
    if extract_cmd:
        print(f"\n🎯 正确的extract_embeddings命令:")
        print("=" * 60)
        print(extract_cmd)
        print("=" * 60)
        
        # 保存到脚本文件
        script_path = selected_dir / 'run_extract_embeddings.sh'
        with open(script_path, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# 自动生成的正确extract_embeddings命令\n")
            f.write("# 基于训练配置生成，确保参数一致性\n\n")
            f.write("set -e  # 遇到错误时退出\n\n")
            f.write("echo '开始embedding提取...'\n")
            f.write(extract_cmd + "\n")
            f.write("echo '✅ embedding提取完成'\n")
        
        os.chmod(script_path, 0o755)
        print(f"\n💾 命令已保存到: {script_path}")
        print(f"可以直接运行: bash {script_path}")
        
        # 创建配置检查工具
        create_config_checker()
        print(f"\n🛠️  已创建配置检查工具: embedding/config_checker.py")
        
    else:
        print("❌ 无法生成extract命令")
    
    # 5. 提供额外建议
    print(f"\n💡 额外建议:")
    print("1. 运行前确保数据路径正确")
    print("2. 检查GPU内存是否充足")
    print("3. 如果仍有问题，检查日志文件获取详细错误信息")
    
    # 6. 诊断维度不匹配的具体原因
    total_expected_dim = 0
    print(f"\n🔬 维度分析:")
    
    # 序列特征维度
    if config.get('USE_ENHANCED_MODEL'):
        seq_count = len(config.get('MASTER_SEQUENCE_FEATURES_MAP', {}))
        print(f"  - 序列特征数量: {seq_count}")
    
    # 静态特征维度
    if config.get('INCLUDE_STATIC_FEATURES'):
        static_dim = analysis['static_dim']
        total_expected_dim += static_dim
        print(f"  - 静态特征维度: {static_dim}")
    
    if total_expected_dim > 0:
        print(f"  - 预期总维度: {total_expected_dim}")
        print(f"  - 您报告的维度: 387 vs 379")
        print(f"  - 差异: {387 - 379} = 8个维度")
        
        if abs(static_dim - 8) < 3:
            print("  🎯 很可能是静态特征配置不一致导致的问题")

if __name__ == "__main__":
    main() 