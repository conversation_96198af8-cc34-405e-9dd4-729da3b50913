"""
Loss functions for conversion rate prediction models.
"""
import tensorflow as tf


def sequnece_diff(pred):
    """
    Calculate sequence difference loss to enforce monotonicity.
    
    Args:
        pred (tf.Tensor): Prediction tensor of shape (batch_size, sequence_length).
        
    Returns:
        tf.Tensor: Sequence difference loss.
    """
    # Calculate differences between consecutive predictions
    diff = pred[:, 1:] - pred[:, 0:-1]
    
    # Clip differences to negative values only (penalize decreasing predictions)
    clipped_diff = tf.clip_by_value(diff, -1, 0)
    
    # Take absolute value
    abs_diff = tf.abs(clipped_diff)
    
    # Calculate mean and scale
    sum_abs_diff = tf.reduce_mean(abs_diff) * 0.1
    
    return sum_abs_diff


def cumsum_loss(y_true, y_pred):
    """
    Standard loss function combining binary cross-entropy and sequence difference.
    
    Args:
        y_true (tf.Tensor): True labels.
        y_pred (tf.Tensor): Predicted values.
        
    Returns:
        tf.Tensor: Combined loss.
    """
    # Binary cross-entropy for classification
    bce_loss = tf.keras.losses.BinaryCrossentropy()(y_true, y_pred)
    
    # Sequence difference loss for monotonicity
    seq_loss = sequnece_diff(y_pred)
    
    return bce_loss + seq_loss


def cumsum_mask_loss(y_true, y_pred):
    """
    Loss function with masking for time periods.
    
    Args:
        y_true (tf.Tensor): Combined tensor with true labels and masks.
        y_pred (tf.Tensor): Predicted values.
        
    Returns:
        tf.Tensor: Combined masked loss.
    """
    # Extract labels and masks from combined input
    y_true_value, y_mask_value = y_true[..., 0:6], y_true[..., 6:]
    
    # Apply mask to both true values and predictions
    y_true_mask = tf.where(y_mask_value > 0, y_true_value, tf.zeros_like(y_true_value))
    y_pred_mask = tf.where(y_mask_value > 0, y_pred, tf.zeros_like(y_pred))
    
    # Calculate masked binary cross-entropy and sequence difference
    masked_bce = tf.keras.losses.BinaryCrossentropy()(y_true_mask, y_pred_mask)
    seq_loss = sequnece_diff(y_pred)
    
    return masked_bce + seq_loss 