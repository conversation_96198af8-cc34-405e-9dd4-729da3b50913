#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SMOTE数据增强器
专门用于处理不平衡数据的SMOTE增强
"""
import os
import json
import logging
import pandas as pd
import numpy as np
import warnings
from typing import Tuple, Optional
from imblearn.over_sampling import SMOTE

# 抑制性能警告
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class SMOTEEnhancer:
    """SMOTE数据增强器"""
    
    def __init__(self, sampling_strategy: float = 0.05, random_state: int = 42, k_neighbors: int = 5):
        """
        初始化SMOTE增强器
        
        Args:
            sampling_strategy: SMOTE采样策略，正样本目标比例
            random_state: 随机种子
            k_neighbors: SMOTE的k近邻数量
        """
        self.sampling_strategy = sampling_strategy
        self.random_state = random_state
        self.k_neighbors = k_neighbors
        self.smote = SMOTE(
            sampling_strategy=sampling_strategy,
            random_state=random_state,
            k_neighbors=k_neighbors
        )
        self.scaler = StandardScaler()
        
    def enhance_dataset(self, 
                       train_data_path: str, 
                       test_data_path: str,
                       output_dir: str = "data/dataset_nio_new_car_v15_smote") -> Tuple[str, str]:
        """
        增强数据集
        
        Args:
            train_data_path: 训练数据路径
            test_data_path: 测试数据路径
            output_dir: 输出目录
            
        Returns:
            增强后的训练和测试数据路径
        """
        logger.info("=== 开始SMOTE数据增强 ===")
        
        # 1. 加载数据
        logger.info(f"加载训练数据: {train_data_path}")
        df_train = pd.read_parquet(train_data_path)
        
        logger.info(f"加载测试数据: {test_data_path}")
        df_test = pd.read_parquet(test_data_path)
        
        logger.info(f"原始训练集: {len(df_train)}条记录")
        logger.info(f"测试集: {len(df_test)}条记录")
        
        # 2. 准备特征和标签
        X_train, y_train = self._prepare_features_labels(df_train)
        
        if X_train is None or y_train is None:
            logger.error("特征或标签准备失败")
            return None, None
        
        logger.info(f"特征维度: {X_train.shape[1]}")
        logger.info(f"原始正样本比例: {y_train.mean():.4f}")
        
        # 3. 应用SMOTE
        logger.info("应用SMOTE重采样...")
        try:
            X_train_smote, y_train_smote = self.smote.fit_resample(X_train, y_train)
            logger.info(f"SMOTE后: {len(X_train_smote)}条记录")
            logger.info(f"SMOTE后正样本比例: {y_train_smote.mean():.4f}")
        except Exception as e:
            logger.error(f"SMOTE失败: {e}")
            return None, None
        
        # 4. 重构数据集
        df_train_enhanced = self._reconstruct_dataset(
            df_train, X_train_smote, y_train_smote
        )
        
        # 5. 保存增强数据集
        os.makedirs(output_dir, exist_ok=True)
        
        enhanced_train_path = os.path.join(output_dir, "datetime=20240430")
        enhanced_test_path = os.path.join(output_dir, "datetime=20240531")
        
        logger.info(f"保存增强训练集: {enhanced_train_path}")
        df_train_enhanced.to_parquet(enhanced_train_path, index=False)
        
        logger.info(f"保存测试集: {enhanced_test_path}")
        df_test.to_parquet(enhanced_test_path, index=False)
        
        # 6. 创建增强数据集配置
        self._create_enhanced_config(output_dir)
        
        logger.info("SMOTE数据增强完成")
        return enhanced_train_path, enhanced_test_path
    
    def _prepare_features_labels(self, df: pd.DataFrame) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """准备特征和标签"""
        try:
            logger.info(f"准备特征和标签，数据形状: {df.shape}")

            # 选择数值特征，排除非特征列
            exclude_cols = ['user_id', 'datetime', 'purchase_days_nio_new_car_total', 'm_purchase_days_nio_new_car']
            numeric_cols = []

            for col in df.columns:
                if col not in exclude_cols:
                    # 检查是否为数值类型或可转换为数值类型
                    try:
                        # 尝试转换为数值类型
                        pd.to_numeric(df[col], errors='raise')
                        numeric_cols.append(col)
                    except (ValueError, TypeError):
                        # 如果不能转换为数值，跳过
                        logger.debug(f"跳过非数值列: {col}")
                        continue

            logger.info(f"找到 {len(numeric_cols)} 个数值特征")

            # 限制特征数量以避免维度灾难和内存问题
            selected_features = numeric_cols[:100]  # 减少到100个特征
            logger.info(f"选择前 {len(selected_features)} 个特征用于SMOTE")

            # 提取特征并处理缺失值
            X = df[selected_features].copy()

            # 转换为数值类型并填充缺失值
            for col in selected_features:
                X[col] = pd.to_numeric(X[col], errors='coerce').fillna(0)

            X_values = X.values.astype(np.float32)  # 使用float32节省内存

            # 检查是否有无效值
            if np.any(np.isnan(X_values)) or np.any(np.isinf(X_values)):
                logger.warning("发现NaN或Inf值，进行清理")
                X_values = np.nan_to_num(X_values, nan=0.0, posinf=1e6, neginf=-1e6)

            # 标准化特征
            X_scaled = self.scaler.fit_transform(X_values)

            # 提取标签
            if 'purchase_days_nio_new_car_total' in df.columns:
                # 处理可能的字符串类型
                label_col = df['purchase_days_nio_new_car_total'].fillna(999)
                # 转换为数值类型
                label_col = pd.to_numeric(label_col, errors='coerce').fillna(999)
                y = (label_col < 180).astype(int).values

                logger.info(f"标签分布: 正样本={np.sum(y)}, 负样本={len(y)-np.sum(y)}, 正样本比例={np.mean(y):.4f}")
            else:
                logger.error("未找到标签列 purchase_days_nio_new_car_total")
                return None, None

            return X_scaled, y

        except Exception as e:
            logger.error(f"特征标签准备失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None, None
    
    def _reconstruct_dataset(self, 
                           original_df: pd.DataFrame, 
                           X_enhanced: np.ndarray, 
                           y_enhanced: np.ndarray) -> pd.DataFrame:
        """重构增强后的数据集"""
        logger.info("重构增强后的数据集...")
        
        # 获取数值特征列名（与_prepare_features_labels保持一致）
        exclude_cols = ['user_id', 'datetime', 'purchase_days_nio_new_car_total', 'm_purchase_days_nio_new_car']
        numeric_cols = []

        for col in original_df.columns:
            if col not in exclude_cols:
                try:
                    pd.to_numeric(original_df[col], errors='raise')
                    numeric_cols.append(col)
                except (ValueError, TypeError):
                    continue

        # 使用与训练时相同的特征数量
        selected_features = numeric_cols[:100]  # 与_prepare_features_labels保持一致

        logger.info(f"重构数据: X_enhanced形状={X_enhanced.shape}, 选择特征数={len(selected_features)}")

        # 确保特征数量匹配
        if X_enhanced.shape[1] != len(selected_features):
            min_features = min(X_enhanced.shape[1], len(selected_features))
            X_enhanced = X_enhanced[:, :min_features]
            selected_features = selected_features[:min_features]
            logger.warning(f"特征数量不匹配，调整为{min_features}个特征")

        # 反标准化特征
        X_original_scale = self.scaler.inverse_transform(X_enhanced)

        # 创建新的DataFrame
        df_enhanced = pd.DataFrame(X_original_scale, columns=selected_features)
        
        # 添加必要的列
        df_enhanced['user_id'] = range(len(df_enhanced))  # 生成新的user_id
        df_enhanced['datetime'] = '20240430'  # 保持原始日期
        
        # 根据标签生成purchase_days_nio_new_car_total
        df_enhanced['purchase_days_nio_new_car_total'] = np.where(
            y_enhanced == 1,
            np.random.randint(0, 180, size=len(y_enhanced)),  # 正样本：0-179天
            np.random.randint(180, 999, size=len(y_enhanced))  # 负样本：180-999天
        )
        
        # 优化：只保留核心列，避免处理过多列导致性能问题
        core_columns = ['user_id', 'datetime', 'purchase_days_nio_new_car_total'] + selected_features

        # 预计算核心列的填充值
        missing_cols = {}
        for col in core_columns:
            if col not in df_enhanced.columns:
                try:
                    if col in original_df.columns:
                        if original_df[col].dtype == 'object':
                            # 字符串列：使用最频繁的值
                            mode_val = original_df[col].mode()
                            fill_value = mode_val.iloc[0] if len(mode_val) > 0 else 'unknown'
                        else:
                            # 数值列：使用中位数
                            try:
                                median_val = original_df[col].median()
                                fill_value = median_val if not pd.isna(median_val) else 0
                            except:
                                fill_value = 0
                    else:
                        fill_value = 0 if col != 'user_id' and col != 'datetime' else 'unknown'

                    missing_cols[col] = fill_value

                except Exception as e:
                    logger.warning(f"处理列 {col} 失败: {e}, 使用默认值")
                    missing_cols[col] = 0 if col not in ['user_id', 'datetime'] else 'unknown'

        # 一次性添加所有缺失列，避免碎片化
        if missing_cols:
            logger.info(f"添加 {len(missing_cols)} 个缺失列")
            missing_df = pd.DataFrame({
                col: [value] * len(df_enhanced)
                for col, value in missing_cols.items()
            })
            df_enhanced = pd.concat([df_enhanced, missing_df], axis=1)

        # 只保留核心列，提高性能
        df_enhanced = df_enhanced[core_columns]

        logger.info(f"重构完成，最终数据形状: {df_enhanced.shape}")
        
        return df_enhanced
    
    def _create_enhanced_config(self, output_dir: str):
        """创建增强数据集的配置文件"""
        try:
            # 读取原始配置
            original_config_path = "src/configs/datasets/dataset_nio_new_car_v15.json"
            with open(original_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 修改配置
            config['dataset_name'] = 'dataset_nio_new_car_v15_smote'
            config['description'] = f'SMOTE增强数据集 (sampling_strategy={self.sampling_strategy})'
            config['enhancement'] = {
                'method': 'SMOTE',
                'sampling_strategy': self.sampling_strategy,
                'k_neighbors': self.k_neighbors,
                'random_state': self.random_state
            }
            
            # 保存增强配置
            enhanced_config_path = "src/configs/datasets/dataset_nio_new_car_v15_smote.json"
            with open(enhanced_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"增强数据集配置保存到: {enhanced_config_path}")
            
        except Exception as e:
            logger.warning(f"创建增强配置失败: {e}")

def create_smote_enhanced_dataset(sampling_strategy: float = 0.05) -> bool:
    """创建SMOTE增强数据集的便捷函数"""
    enhancer = SMOTEEnhancer(sampling_strategy=sampling_strategy)
    
    train_path = "data/dataset_nio_new_car_v15/datetime=20240430"
    test_path = "data/dataset_nio_new_car_v15/datetime=20240531"
    
    enhanced_train, enhanced_test = enhancer.enhance_dataset(train_path, test_path)
    
    return enhanced_train is not None and enhanced_test is not None

if __name__ == "__main__":
    # 测试SMOTE增强
    logging.basicConfig(level=logging.INFO)
    success = create_smote_enhanced_dataset(sampling_strategy=0.05)
    print(f"SMOTE增强{'成功' if success else '失败'}")
