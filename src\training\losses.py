"""
Loss functions for conversion rate prediction models.
"""
import tensorflow as tf


# 定义月份权重，更加注重Month_1，其次是Month_2至Month_4，最后是Month_5和Month_6
MONTH_WEIGHTS = [5.0, 2.0, 2.0, 2.0, 1.0, 1.0]


def sequnece_diff(pred):
    """
    Calculate sequence difference loss to enforce monotonicity.
    
    Args:
        pred (tf.Tensor): Prediction tensor of shape (batch_size, sequence_length).
        
    Returns:
        tf.Tensor: Sequence difference loss.
    """
    # Calculate differences between consecutive predictions
    diff = pred[:, 1:] - pred[:, 0:-1]
    
    # Clip differences to negative values only (penalize decreasing predictions)
    clipped_diff = tf.clip_by_value(diff, -1, 0)
    
    # Take absolute value
    abs_diff = tf.abs(clipped_diff)
    
    # Calculate mean and scale
    sum_abs_diff = tf.reduce_mean(abs_diff) * 0.1
    
    return sum_abs_diff


def cumsum_loss(y_true, y_pred, use_month_weights=False):
    """
    Standard loss function combining binary cross-entropy and sequence difference.
    
    Args:
        y_true (tf.Tensor): True labels.
        y_pred (tf.Tensor): Predicted values.
        use_month_weights (bool): Whether to use month-specific weights.
        
    Returns:
        tf.Tensor: Combined loss.
    """
    if use_month_weights:
        # 应用月份权重
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        
        # 计算每个月份的BCE损失
        per_month_loss = -(y_true * tf.math.log(tf.clip_by_value(y_pred, 1e-7, 1.0)) + 
                          (1 - y_true) * tf.math.log(tf.clip_by_value(1 - y_pred, 1e-7, 1.0)))
        
        # 应用月份权重
        weighted_loss = per_month_loss * tf.reshape(month_weights, [1, -1])
        
        # 计算平均损失
        bce_loss = tf.reduce_mean(weighted_loss)
    else:
        # 标准BCE损失
        bce_loss = tf.keras.losses.BinaryCrossentropy()(y_true, y_pred)
    
    # Sequence difference loss for monotonicity
    seq_loss = sequnece_diff(y_pred)
    
    return bce_loss + seq_loss


def weighted_cumsum_loss(y_true, y_pred, pos_weight=10.0, use_month_weights=False):
    """
    Weighted loss function that emphasizes recall by giving higher weight to positive samples.
    
    Args:
        y_true (tf.Tensor): True labels.
        y_pred (tf.Tensor): Predicted values.
        pos_weight (float): Weight to apply to positive samples (1's in y_true).
            Higher values will increase focus on recall.
        use_month_weights (bool): Whether to use month-specific weights.
        
    Returns:
        tf.Tensor: Combined weighted loss.
    """
    # Create sample weights tensor based on true values
    # Apply higher weight to positive samples (conversions)
    sample_weights = tf.where(y_true > 0, pos_weight, 1.0)
    
    # 如果使用月份权重，则应用月份权重
    if use_month_weights:
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        month_weights_expanded = tf.reshape(month_weights, [1, -1])
        sample_weights = sample_weights * month_weights_expanded
    
    # Calculate BCE loss manually to handle weighting correctly
    epsilon = 1e-7
    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)
    
    # Calculate per-element binary cross entropy
    cross_entropy = -(y_true * tf.math.log(y_pred) + 
                      (1 - y_true) * tf.math.log(1 - y_pred))
    
    # Apply weights
    weighted_loss = cross_entropy * sample_weights
    
    # Calculate mean
    bce_loss = tf.reduce_mean(weighted_loss)
    
    # Sequence difference loss for monotonicity
    seq_loss = sequnece_diff(y_pred)
    
    return bce_loss + seq_loss


def focal_cumsum_loss(y_true, y_pred, alpha=0.35, gamma=2.5, use_month_weights=False):
    """
    Focal loss implementation for conversion prediction, focusing more on hard-to-classify examples.
    Particularly effective for imbalanced datasets to improve recall.
    
    Args:
        y_true (tf.Tensor): True labels.
        y_pred (tf.Tensor): Predicted values.
        alpha (float): Weighting factor to balance positive/negative examples.
            Higher values give more importance to positive class.
        gamma (float): Focusing parameter that reduces the loss for well-classified examples.
            Higher values increase focus on hard, misclassified examples.
        use_month_weights (bool): Whether to use month-specific weights.
        
    Returns:
        tf.Tensor: Combined focal loss with sequence difference.
    """
    # Clip predictions to avoid numerical instability
    epsilon = 1e-7
    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)
    
    # Binary cross-entropy calculation components
    cross_entropy = -y_true * tf.math.log(y_pred) - (1 - y_true) * tf.math.log(1 - y_pred)
    
    # Apply the focal scaling
    p_t = (y_true * y_pred) + ((1 - y_true) * (1 - y_pred))
    focal_weight = tf.pow(1 - p_t, gamma)
    
    # Apply alpha weighting for class balance
    alpha_weight = y_true * alpha + (1 - y_true) * (1 - alpha)
    
    # 如果使用月份权重，则应用月份权重
    if use_month_weights:
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        month_weights_expanded = tf.reshape(month_weights, [1, -1])
        focal_weight = focal_weight * month_weights_expanded
    
    # Combine weights and cross entropy
    focal_loss = alpha_weight * focal_weight * cross_entropy
    
    # Take mean across all elements
    focal_loss = tf.reduce_mean(focal_loss)
    
    # Sequence difference loss for monotonicity
    seq_loss = sequnece_diff(y_pred)
    
    return focal_loss + seq_loss


def cumsum_mask_loss(y_true, y_pred, use_month_weights=False):
    """
    Loss function with masking for time periods.
    
    Args:
        y_true (tf.Tensor): Combined tensor with true labels and masks.
        y_pred (tf.Tensor): Predicted values.
        use_month_weights (bool): Whether to use month-specific weights.
        
    Returns:
        tf.Tensor: Combined masked loss.
    """
    # Extract labels and masks from combined input
    y_true_value, y_mask_value = y_true[..., 0:6], y_true[..., 6:]
    
    # Apply mask to both true values and predictions
    y_true_mask = tf.where(y_mask_value > 0, y_true_value, tf.zeros_like(y_true_value))
    y_pred_mask = tf.where(y_mask_value > 0, y_pred, tf.zeros_like(y_pred))
    
    if use_month_weights:
        # 应用月份权重
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        
        # 计算每个月份的BCE损失
        per_month_loss = -(y_true_mask * tf.math.log(tf.clip_by_value(y_pred_mask, 1e-7, 1.0)) + 
                          (1 - y_true_mask) * tf.math.log(tf.clip_by_value(1 - y_pred_mask, 1e-7, 1.0)))
        
        # 应用月份权重和掩码
        weighted_loss = per_month_loss * tf.reshape(month_weights, [1, -1]) * y_mask_value
        
        # 计算平均损失
        non_zero_elements = tf.reduce_sum(tf.cast(y_mask_value > 0, tf.float32))
        masked_bce = tf.reduce_sum(weighted_loss) / (non_zero_elements + 1e-7)
    else:
        # 标准BCE损失
        masked_bce = tf.keras.losses.BinaryCrossentropy()(y_true_mask, y_pred_mask)
    
    seq_loss = sequnece_diff(y_pred)
    
    return masked_bce + seq_loss


def multitask_loss(y_true, y_pred, task_weights=None):
    """改进的多任务损失函数，处理 y_true(12维) 和 y_pred(12维)"""
    # y_true 包含真实标签和掩码 (形状: [batch_size, 12])
    # y_pred 包含主任务预测和辅助任务预测 (形状: [batch_size, 12])

    y_true_labels = y_true[:, :6]  # Shape: [batch_size, 6]
    y_pred_main = y_pred[:, :6]    # Shape: [batch_size, 6]
    y_pred_aux = y_pred[:, 6:]     # Shape: [batch_size, 6]

    epsilon = tf.keras.backend.epsilon() # Small value for clipping

    # --- 主任务损失 ---
    # Clip predictions for numerical stability
    y_pred_main_clipped = tf.clip_by_value(y_pred_main, epsilon, 1. - epsilon)
    # Manual element-wise binary cross-entropy calculation
    bce_main_per_element = -(y_true_labels * tf.math.log(y_pred_main_clipped) +
                             (1. - y_true_labels) * tf.math.log(1. - y_pred_main_clipped))
    # Expected Shape: [batch_size, 6]

    # 根据真实标签 y_true_labels 对主任务损失进行加权
    weighted_bce_main_per_element = tf.where(
        y_true_labels > 0,           # Condition, Shape: [batch_size, 6]
        bce_main_per_element * 30.0, # Then, Shape should be: [batch_size, 6]
        bce_main_per_element         # Else, Shape should be: [batch_size, 6]
    ) # Shape: [batch_size, 6]

    # 计算加权主任务损失的均值
    mean_weighted_bce_main = tf.reduce_mean(weighted_bce_main_per_element)

    # --- 辅助任务损失 ---
    # Clip predictions for numerical stability
    y_pred_aux_clipped = tf.clip_by_value(y_pred_aux, epsilon, 1. - epsilon)
    # Manual element-wise binary cross-entropy calculation
    # 注意：这里仍然使用 y_true_labels 作为辅助任务的目标
    bce_aux_per_element = -(y_true_labels * tf.math.log(y_pred_aux_clipped) +
                            (1. - y_true_labels) * tf.math.log(1. - y_pred_aux_clipped))
    # Expected Shape: [batch_size, 6]

    # 计算辅助任务损失的均值
    mean_bce_aux = tf.reduce_mean(bce_aux_per_element)

    # 组合损失
    return mean_weighted_bce_main + 0.5 * mean_bce_aux


def weighted_cumsum_mask_loss(y_true, y_pred, pos_weight=10.0, use_month_weights=False):
    """
    Weighted loss function with masking for time periods that emphasizes recall.
    
    Args:
        y_true (tf.Tensor): Combined tensor with true labels and masks.
        y_pred (tf.Tensor): Predicted values.
        pos_weight (float): Weight to apply to positive samples (1's in y_true).
            Higher values will increase focus on recall.
        use_month_weights (bool): Whether to use month-specific weights.
        
    Returns:
        tf.Tensor: Combined weighted masked loss.
    """
    # Extract labels and masks from combined input
    y_true_value, y_mask_value = y_true[..., 0:6], y_true[..., 6:]
    
    # Apply mask to both true values and predictions
    y_true_mask = tf.where(y_mask_value > 0, y_true_value, tf.zeros_like(y_true_value))
    y_pred_mask = tf.where(y_mask_value > 0, y_pred, tf.zeros_like(y_pred))
    
    # Create sample weights tensor based on true values and handle multi-dimensional case
    # Apply higher weight to positive samples (conversions)
    sample_weights = tf.where(y_true_mask > 0, pos_weight, 1.0)
    
    # 如果使用月份权重，则应用月份权重
    if use_month_weights:
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        month_weights_expanded = tf.reshape(month_weights, [1, -1])
        sample_weights = sample_weights * month_weights_expanded
    
    # Check mask dimensionality and ensure it's applied correctly
    masked_weights = tf.multiply(sample_weights, y_mask_value)
    
    # Calculate BCE loss manually to handle weighting correctly
    epsilon = 1e-7
    y_pred_mask = tf.clip_by_value(y_pred_mask, epsilon, 1.0 - epsilon)
    
    # Calculate per-element binary cross entropy
    cross_entropy = -(y_true_mask * tf.math.log(y_pred_mask) + 
                       (1 - y_true_mask) * tf.math.log(1 - y_pred_mask))
    
    # Apply weights
    weighted_loss = cross_entropy * masked_weights
    
    # Calculate mean over non-zero mask elements
    non_zero_elements = tf.reduce_sum(tf.cast(y_mask_value > 0, tf.float32))
    masked_bce = tf.reduce_sum(weighted_loss) / (non_zero_elements + epsilon)
    
    # Sequence difference loss for monotonicity
    seq_loss = sequnece_diff(y_pred)
    
    return masked_bce + seq_loss


def asymmetric_focal_cumsum_loss(y_true, y_pred, gamma_pos=2.0, gamma_neg=4.0, alpha=0.25, 
                                pos_weight=30.0, use_month_weights=False):
    """
    非对称焦点损失函数，对正样本和负样本使用不同的gamma参数，更好地处理样本不平衡问题。
    特别适合于转化率预估中需要提高召回率的场景。
    
    Args:
        y_true (tf.Tensor): 真实标签。
        y_pred (tf.Tensor): 预测值。
        gamma_pos (float): 针对正样本的聚焦参数。较大的值会更关注难分类的正样本。
        gamma_neg (float): 针对负样本的聚焦参数。较大的值会更关注难分类的负样本。
        alpha (float): 正负样本平衡因子。值越高，越关注正样本。
        pos_weight (float): 正样本额外权重。更高的值会增加对正样本的关注。
        use_month_weights (bool): 是否使用月份特定权重。
        
    Returns:
        tf.Tensor: 结合序列差分的非对称焦点损失。
    """
    # 避免数值不稳定性
    epsilon = 1e-7
    y_pred = tf.clip_by_value(y_pred, epsilon, 1.0 - epsilon)
    
    # 提取正样本和负样本的掩码
    pos_mask = tf.cast(y_true > 0, tf.float32)
    neg_mask = tf.cast(y_true == 0, tf.float32)
    
    # 计算正样本的损失，使用gamma_pos作为聚焦参数
    pos_pt = y_pred  # 正样本的预测概率
    pos_focal_weight = tf.pow(1.0 - pos_pt, gamma_pos)
    pos_loss = -pos_weight * alpha * pos_mask * pos_focal_weight * tf.math.log(pos_pt)
    
    # 计算负样本的损失，使用gamma_neg作为聚焦参数
    neg_pt = 1.0 - y_pred  # 负样本的预测概率
    neg_focal_weight = tf.pow(1.0 - neg_pt, gamma_neg)
    neg_loss = -(1 - alpha) * neg_mask * neg_focal_weight * tf.math.log(neg_pt)
    
    # 组合正负样本损失
    focal_loss = pos_loss + neg_loss
    
    # 应用月份权重
    if use_month_weights:
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        month_weights_expanded = tf.reshape(month_weights, [1, -1])
        focal_loss = focal_loss * month_weights_expanded
    
    # 计算平均损失
    focal_loss = tf.reduce_mean(focal_loss)
    
    # 序列差分损失
    seq_loss = sequnece_diff(y_pred)
    
    return focal_loss + seq_loss


def asymmetric_focal_mask_loss(y_true, y_pred, gamma_pos=2.0, gamma_neg=4.0, alpha=0.25, 
                              pos_weight=30.0, use_month_weights=False):
    """
    带掩码的非对称焦点损失函数，适用于时间序列预测。
    
    Args:
        y_true (tf.Tensor): 真实标签和掩码的组合张量。
        y_pred (tf.Tensor): 预测值。
        gamma_pos (float): 针对正样本的聚焦参数。较大的值会更关注难分类的正样本。
        gamma_neg (float): 针对负样本的聚焦参数。较大的值会更关注难分类的负样本。
        alpha (float): 正负样本平衡因子。值越高，越关注正样本。
        pos_weight (float): 正样本额外权重。更高的值会增加对正样本的关注。
        use_month_weights (bool): 是否使用月份特定权重。
        
    Returns:
        tf.Tensor: 结合序列差分的掩码非对称焦点损失。
    """
    # 提取标签和掩码
    y_true_value, y_mask_value = y_true[..., 0:6], y_true[..., 6:]
    
    # 应用掩码到真实标签和预测值
    y_true_mask = tf.where(y_mask_value > 0, y_true_value, tf.zeros_like(y_true_value))
    y_pred_mask = tf.where(y_mask_value > 0, y_pred, tf.zeros_like(y_pred))
    
    # 避免数值不稳定性
    epsilon = 1e-7
    y_pred_mask = tf.clip_by_value(y_pred_mask, epsilon, 1.0 - epsilon)
    
    # 提取正样本和负样本的掩码
    pos_mask = tf.cast(y_true_mask > 0, tf.float32)
    neg_mask = tf.cast(y_true_mask == 0, tf.float32)
    
    # 计算正样本的损失
    pos_pt = y_pred_mask  # 正样本的预测概率
    pos_focal_weight = tf.pow(1.0 - pos_pt, gamma_pos)
    pos_loss = -pos_weight * alpha * pos_mask * pos_focal_weight * tf.math.log(pos_pt)
    
    # 计算负样本的损失
    neg_pt = 1.0 - y_pred_mask  # 负样本的预测概率
    neg_focal_weight = tf.pow(1.0 - neg_pt, gamma_neg)
    neg_loss = -(1 - alpha) * neg_mask * neg_focal_weight * tf.math.log(neg_pt)
    
    # 组合正负样本损失
    focal_loss = pos_loss + neg_loss
    
    # 应用月份权重和掩码
    if use_month_weights:
        month_weights = tf.constant(MONTH_WEIGHTS, dtype=tf.float32)
        month_weights_expanded = tf.reshape(month_weights, [1, -1])
        focal_loss = focal_loss * month_weights_expanded
    
    # 应用掩码
    focal_loss = focal_loss * y_mask_value
    
    # 计算掩码元素的平均损失
    non_zero_elements = tf.reduce_sum(tf.cast(y_mask_value > 0, tf.float32))
    masked_focal_loss = tf.reduce_sum(focal_loss) / (non_zero_elements + epsilon)
    
    # 序列差分损失
    seq_loss = sequnece_diff(y_pred)
    
    return masked_focal_loss + seq_loss