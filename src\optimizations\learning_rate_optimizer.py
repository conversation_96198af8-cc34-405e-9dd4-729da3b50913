#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
学习率优化器
通过调整学习率来优化模型性能
"""
import os
import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class LearningRateOptimizer:
    """学习率优化器"""
    
    def __init__(self):
        self.name = "learning_rate"
        self.description = "学习率优化"
    
    def optimize(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行学习率优化
        
        Args:
            config: 优化配置
            
        Returns:
            优化后的配置
        """
        logger.info("=== 执行学习率优化 ===")
        
        # 默认配置
        default_config = {
            'learning_rate': 0.001,  # 降低学习率
            'epochs': 15,
            'batch_size': 512
        }
        
        # 合并配置
        opt_config = {**default_config, **config}
        
        logger.info(f"优化配置: {opt_config}")
        
        return opt_config
    
    def create_training_args(self, config: Dict[str, Any], run_name: str, output_dir: str) -> list:
        """创建训练参数"""
        args = [
            'python', 'src/train.py',
            '--run_name', run_name,
            '--epochs', str(config.get('epochs', 15)),
            '--data_dir', 'data',
            '--output_dir', output_dir
        ]
        
        # 如果有学习率配置，创建临时配置文件
        if 'learning_rate' in config:
            self._create_lr_config(config, output_dir)
            args.extend(['--config_override', f'{output_dir}/lr_config.json'])
        
        return args
    
    def _create_lr_config(self, config: Dict[str, Any], output_dir: str):
        """创建学习率配置文件"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 读取基础配置
            base_config_path = "src/configs/models/sample_20250311_v7-20250311.json"
            with open(base_config_path, 'r', encoding='utf-8') as f:
                base_config = json.load(f)
            
            # 修改学习率
            if 'optimizer' not in base_config:
                base_config['optimizer'] = {}
            
            base_config['optimizer']['learning_rate'] = config['learning_rate']
            
            # 保存配置
            config_path = f"{output_dir}/lr_config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(base_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"学习率配置已保存: {config_path}")
            
        except Exception as e:
            logger.error(f"创建学习率配置失败: {e}")
