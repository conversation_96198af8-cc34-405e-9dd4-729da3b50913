#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Main training script for conversion rate prediction models.
"""
import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# Import modules
from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.training.trainer import ModelTrainer
from src.evaluation.evaluator import ModelEvaluator
from src.utils.config_utils import ConfigManager


def setup_logging(log_level=logging.INFO, log_file=None):
    """
    Set up logging configuration.
    
    Args:
        log_level (int): Logging level.
        log_file (str, optional): Path to log file.
    """
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Configure file handler if log file is provided
    if log_file:
        Path(os.path.dirname(log_file)).mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Silence third-party loggers
    logging.getLogger('qcloud_cos').setLevel(logging.WARNING)


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Train conversion rate prediction model')
    
    # Basic parameters
    parser.add_argument('--model_code', default='sample_20250311_v7-20250311',
                        help='Model code, corresponds to model parameter file name')
    parser.add_argument('--run_name', default=f"{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        help='Run code, example: 20241101_v1')
    parser.add_argument('--dataset_code', default='dataset_nio_new_car_v15',
                        help='Dataset location (fixed negative sampling factor)')
    parser.add_argument('--evaluate_file', default='20240531_随机采样1%.parquet',
                        help='Evaluation dataset filename (random sampling)')
    
    # Additional parameters
    parser.add_argument('--epochs', type=int, default=50,
                        help='Maximum number of training epochs')
    parser.add_argument('--batch_size', type=int, default=8192,
                        help='Training batch size')
    parser.add_argument('--patience', type=int, default=10,
                        help='Early stopping patience')
    parser.add_argument('--use_cross_layer', type=bool, default=True,
                        help='Whether to use cross layer')
    parser.add_argument('--use_time_attention', type=bool, default=True,
                        help='Whether to use time attention mechanism')
    parser.add_argument('--log_file', default=None,
                        help='Log file path')
    parser.add_argument('--data_dir', default='.',
                        help='Data directory path')
    parser.add_argument('--output_dir', default='src/evaluation',
                        help='Output directory for evaluation results')
    
    return parser.parse_args()


def main():
    """Main execution function."""
    # Parse arguments
    args = parse_args()
    
    # Setup logging
    log_file = args.log_file or f"{args.output_dir}/{args.run_name}/training.log"
    setup_logging(log_file=log_file)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting training run: {args.run_name}")
    logger.info(f"Model code: {args.model_code}")
    logger.info(f"Dataset code: {args.dataset_code}")
    
    try:
        # Load model configuration
        config_manager = ConfigManager()
        
        # 首先尝试从configs目录读取配置文件
        model_param_path = f"src/configs/models/{args.model_code}.json"
        if not os.path.exists(model_param_path):
            # 如果找不到，尝试从项目根目录读取
            model_param_path = f"{args.model_code}.json"
            if not os.path.exists(model_param_path):
                logger.error(f"Model parameter file not found: {model_param_path}")
                return 1
                
        logger.info(f"Loading model configuration from: {model_param_path}")
        model_config = config_manager.load_config(model_param_path)
        
        # 同样方式处理数据集配置
        dataset_config_path = f"src/configs/datasets/{args.dataset_code}.json"
        if not os.path.exists(dataset_config_path):
            dataset_config_path = f"{args.dataset_code}.json"
            if not os.path.exists(dataset_config_path):
                logger.error(f"Dataset configuration file not found: {dataset_config_path}")
                return 1
                
        logger.info(f"Loading dataset configuration from: {dataset_config_path}")
        
        # Override some config parameters with command line arguments
        model_config['batch_size'] = args.batch_size
        
        # Get model parameters
        network_name = model_config.get("network_name")
        predict_method = model_config.get("predict_method", "6m")
        train_dates = model_config.get("train_dates", [])
        test_dates = model_config.get("test_dates", [])
        mask_label = model_config.get("mask_label", None)
        dict_raw_features = model_config.get("RawFeature", {})
        extra_datasets = model_config.get("Extra", [])
        
        logger.info(f"Network name: {network_name}")
        logger.info(f"Prediction method: {predict_method}")
        logger.info(f"Train dates: {train_dates}")
        logger.info(f"Test dates: {test_dates}")
        
        # Prepare column lists
        list_raw_features = list(dict_raw_features.keys())
        list_raw_labels = ["purchase_days_nio_new_car_total"]
        
        logger.info(f"Feature count: {len(list_raw_features)}")
        logger.info(f"Label count: {len(list_raw_labels)}")
        
        # Determine columns to read
        read_columns = ["user_id", "datetime"] + list_raw_features + list_raw_labels
        dataset_read_columns = read_columns + ["m_purchase_days_nio_new_car"]
        
        if mask_label:
            dataset_read_columns.append(mask_label)
        
        # 1. Data Loading
        logger.info("Step 1: Loading data")
        # 构建完整的数据集路径
        dataset_path = os.path.join(args.data_dir, args.dataset_code)
        data_loader = DataLoader(dataset_path, dataset_config_path)
        
        # Load all date partitions for train/test
        dataset_dates = sorted(list(set(train_dates + test_dates)))
        df_raw = data_loader.load_dataset(
            dates=dataset_dates,
            columns=dataset_read_columns,
            extra_datasets=extra_datasets
        )
        
        # Load evaluation dataset
        df_evaluate = data_loader.load_evaluation_data(
            evaluate_file=args.evaluate_file,
            columns=read_columns,
            extra_datasets=extra_datasets
        )
        
        # 2. Data Preprocessing
        logger.info("Step 2: Data preprocessing")
        preprocessor = DataPreprocessor(data_loader.feature_padding_dict, train_dates=train_dates)
        
        # Preprocess features based on dataset configuration
        df_raw = preprocessor.preprocess_features(df_raw)
        df_evaluate = preprocessor.preprocess_features(df_evaluate)
        
        # Preprocess features based on model configuration
        df_raw = preprocessor.preprocess_model_features(df_raw, dict_raw_features)
        df_evaluate = preprocessor.preprocess_model_features(df_evaluate, dict_raw_features)
        
        # Process labels
        df_raw = preprocessor.process_purchase_labels(df_raw)
        df_evaluate = preprocessor.process_purchase_labels(
            df_evaluate, 
            source_column="purchase_days_nio_new_car_total", 
            target_column="m_purchase_days_nio_new_car"
        )
        
        # 3. Feature Engineering
        logger.info("Step 3: Feature engineering")
        feature_builder = FeatureBuilder()
        
        # Split data into train and test sets
        df_train_m, df_test_m = feature_builder.split_train_test(df_raw, test_dates)
        
        # Generate datasets for training
        label_name = "m_purchase_days_nio_new_car_consum"
        ds_train_m = feature_builder.generate_dataset(
            df_train_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method
        )
        
        ds_test_m = feature_builder.generate_dataset(
            df_test_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method
        )
        
        # 4. Model Training
        logger.info("Step 4: Model training")
        # 使用评估输出目录作为模型输出目录的基础
        output_dir = os.path.join(args.output_dir, args.run_name)
        trainer = ModelTrainer(model_config, args.run_name, output_dir)
        
        # Build model
        model = trainer.build_model(
            use_cross_layer=args.use_cross_layer,
            use_time_attention=args.use_time_attention
        )
        
        # Train model
        trainer.train(
            ds_train_m,
            ds_test_m,
            epochs=args.epochs,
            early_stopping_patience=args.patience
        )
        
        # 5. Model Inference
        logger.info("Step 5: Model inference")
        # Run inference on test set
        test_predictions = trainer.inference(df_test_m, dict_raw_features)
        df_test_m["result"] = list(test_predictions)
        
        # Run inference on evaluation set
        evaluate_predictions = trainer.inference(df_evaluate, dict_raw_features)
        df_evaluate["result"] = list(evaluate_predictions)
        
        # 6. Model Evaluation
        logger.info("Step 6: Model evaluation")
        evaluator = ModelEvaluator(output_dir)
        
        evaluation_results = evaluator.evaluate_model(
            df_test_m=df_test_m,
            df_evaluate=df_evaluate,
            label_column="m_purchase_days_nio_new_car",
            pred_column="result",
            mask_label_column=mask_label,
            model_code=args.model_code
        )
        
        logger.info("Training pipeline completed successfully")
        return 0
        
    except Exception as e:
        logger.exception(f"Error in training pipeline: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 