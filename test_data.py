#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据读取和基本信息
"""
import pandas as pd
import numpy as np
import time

def test_data_loading():
    """测试数据加载"""
    print("开始测试数据加载...")
    
    start_time = time.time()
    
    try:
        # 读取数据
        df = pd.read_parquet('data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet')
        
        load_time = time.time() - start_time
        print(f"数据加载成功，耗时: {load_time:.2f}秒")
        
        # 基本信息
        print(f"数据形状: {df.shape}")
        print(f"内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        # 列信息
        print(f"总列数: {len(df.columns)}")
        print(f"数值列数: {len(df.select_dtypes(include=[np.number]).columns)}")
        print(f"字符串列数: {len(df.select_dtypes(include=['object']).columns)}")
        
        # 查找目标列
        target_cols = [col for col in df.columns if 'purchase' in col.lower()]
        print(f"购买相关列: {target_cols}")
        
        # 检查前几列
        print(f"前10列: {list(df.columns[:10])}")
        
        # 检查是否有user_id
        if 'user_id' in df.columns:
            print(f"用户数: {df['user_id'].nunique()}")
        
        # 检查缺失值
        missing_cols = df.isnull().sum()
        missing_cols = missing_cols[missing_cols > 0]
        print(f"有缺失值的列数: {len(missing_cols)}")
        
        # 如果有购买标签，检查分布
        if target_cols:
            for col in target_cols[:3]:  # 只检查前3个
                if col in df.columns:
                    non_null_count = df[col].notna().sum()
                    if non_null_count > 0:
                        positive_count = (df[col] > 0).sum()
                        print(f"{col}: 非空值={non_null_count}, 正样本={positive_count}, 比例={positive_count/non_null_count:.4f}")
        
        return df
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def test_simple_model():
    """测试简单模型"""
    print("\n开始测试简单模型...")
    
    # 创建简单的测试数据
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = (X[:, 0] + X[:, 1] > 0).astype(int)
    
    print(f"测试数据形状: X={X.shape}, y={y.shape}")
    print(f"正样本比例: {y.mean():.4f}")
    
    # 测试TensorFlow
    import tensorflow as tf
    print(f"TensorFlow版本: {tf.__version__}")
    
    # 创建简单模型
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(16, activation='relu', input_shape=(10,)),
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    
    # 快速训练
    history = model.fit(X, y, epochs=5, batch_size=32, verbose=1, validation_split=0.2)
    
    # 预测
    y_pred = model.predict(X[:100])
    print(f"预测形状: {y_pred.shape}")
    print(f"预测范围: [{y_pred.min():.4f}, {y_pred.max():.4f}]")
    
    print("简单模型测试成功！")

if __name__ == "__main__":
    # 测试数据加载
    df = test_data_loading()
    
    # 测试简单模型
    test_simple_model()
    
    print("\n所有测试完成！")
