# 蔚来转化率预测模型 - 最终优化版本

## 🎯 项目概述

本项目是蔚来汽车转化率预测的深度学习解决方案，通过系统性优化达到了**AUC 0.8011**的优秀性能。

### 核心特点
- **极度不平衡数据处理**: 正样本率仅0.24%
- **模块化架构**: 清晰的代码组织和可扩展设计
- **集成学习**: 多模型融合提升性能
- **SMOTE重采样**: 有效处理数据不平衡问题

## 📊 性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| **AUC** | **0.8011** | 主要评估指标 |
| **准确率** | 99.64% | 整体预测准确性 |
| **精确率** | 14.08% | 正样本预测精度 |
| **召回率** | 9.62% | 正样本识别能力 |

## 🏗️ 项目结构

```
nio-eatv/
├── src/                          # 源代码模块
│   ├── data/                     # 数据处理模块
│   │   ├── nio_loader.py         # 蔚来数据加载器
│   │   └── loader.py             # 通用数据加载器
│   ├── models/                   # 模型定义模块
│   │   ├── core_models.py        # 核心模型类
│   │   ├── layers/               # 自定义层（已清理）
│   │   └── networks/             # 网络架构（已清理）
│   └── evaluation/               # 评估模块
│       └── evaluator.py          # 模型评估器
├── logs/                         # 训练日志和结果
│   ├── final_model_*.json        # 最新训练结果
│   └── experiment_*.json         # 实验记录
├── reports/                      # 优化报告
│   └── optimization_experiences.md  # 优化经验总结
├── data/                         # 数据目录
│   └── dataset_nio_new_car_v15/  # 蔚来数据集
└── train_final_model.py          # 最终训练脚本
```

## 🚀 快速开始

### 环境要求
```bash
pip install tensorflow pandas numpy scikit-learn imbalanced-learn
```

### 基础训练
```bash
# 使用默认参数训练
python train_final_model.py

# 自定义参数训练
python train_final_model.py --epochs=10 --batch_size=512 --ensemble_size=3
```

### 参数说明
- `--epochs`: 训练轮数（默认12）
- `--batch_size`: 批次大小（默认512）
- `--patience`: 早停耐心值（默认5）
- `--ensemble_size`: 集成模型数量（默认3）
- `--feature_count`: 特征数量（默认150）
- `--use_smote`: 使用SMOTE重采样（默认True）
- `--smote_ratio`: SMOTE采样比例（默认0.05）

## 🔧 核心技术

### 1. 数据处理
- **特征选择**: 150个最相关的数值特征
- **SMOTE重采样**: 5%采样比例，有效处理不平衡
- **数据分割**: 80%训练，20%测试，保持分层采样

### 2. 模型架构
- **深度网络**: 256→128→64→32→16→1
- **正则化**: BatchNormalization + Dropout
- **集成学习**: 3个不同架构模型融合

### 3. 训练策略
- **早停**: 防止过拟合
- **学习率衰减**: 自适应学习率调整
- **类别平衡**: SMOTE重采样处理不平衡

## 📈 优化历程

### 有效优化技术 ✅
1. **SMOTE重采样**: AUC提升+0.0231（最佳）
2. **深度网络架构**: AUC提升+0.0171
3. **集成学习**: AUC提升+0.0174
4. **更多特征**: 150特征优于50特征
5. **BatchNormalization**: 稳定训练过程

### 无效优化技术 ❌
1. **特征选择**: AUC下降-0.0140
2. **残差连接**: AUC下降-0.0074
3. **梯度裁剪**: AUC下降-0.0397
4. **Focal Loss**: AUC下降-0.0395

## 📊 使用示例

### 基础使用
```python
from src.data.nio_loader import NioDataLoader
from src.models.core_models import ModelTrainer
from src.evaluation.evaluator import ModelEvaluator

# 数据加载
loader = NioDataLoader()
X, y, features = loader.load_and_prepare(feature_count=150)
X_train, X_test, y_train, y_test = loader.split_data(X, y)

# 模型训练
trainer = ModelTrainer(use_smote=True, smote_ratio=0.05)
results = trainer.train_ensemble(X_train, y_train, X_test, y_test)

# 模型评估
evaluator = ModelEvaluator()
eval_results = evaluator.evaluate_ensemble(
    y_test, results['ensemble_prediction'], results['individual_predictions']
)
```

## 📝 实验记录

所有训练结果和实验记录保存在`/logs`目录下：
- `final_model_*.json`: 训练结果
- `experiment_*.json`: 实验配置和日志

## 🔍 性能分析

### 数据特点
- **样本数**: 212,611
- **特征数**: 150（从499中筛选）
- **正样本率**: 0.24%（极度不平衡）
- **SMOTE后正样本率**: 4.76%

### 模型表现
- **集成效果**: 相比最佳单模型提升+0.0070
- **训练稳定性**: 早停机制确保最佳性能
- **泛化能力**: 测试集AUC 0.8011

## 🎯 未来优化方向

1. **超参数调优**: 网格搜索最优参数组合
2. **特征工程**: 创建更多交互特征
3. **序列建模**: 处理用户行为时序信息
4. **多任务学习**: 6个月累积预测任务

## 📚 参考文档

- [优化经验报告](reports/optimization_experiences.md)
- [原始项目文档](README.md)

---

**项目状态**: 生产就绪  
**最后更新**: 2025年6月16日  
**维护者**: 蔚来AI团队
