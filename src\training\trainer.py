"""
Trainer module for conversion rate prediction models.
"""
import tensorflow as tf
import numpy as np
import pandas as pd
import logging
import json
import importlib
from pathlib import Path
import time


class ModelTrainer:
    """Model trainer class that handles model training and evaluation."""
    
    def __init__(self, model_config, run_name, output_dir=None):
        """
        Initialize model trainer.
        
        Args:
            model_config (dict): Model configuration.
            run_name (str): Name of this training run.
            output_dir (str, optional): Directory to store outputs.
        """
        self.model_config = model_config
        self.run_name = run_name
        self.output_dir = output_dir or run_name
        self.logger = logging.getLogger(__name__)
        
        # Create output directory
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        # Initialize model parameters
        self.network_name = model_config.get("network_name")
        self.predict_method = model_config.get("predict_method", "6m")
        self.mask_label = model_config.get("mask_label", None)
        self.val_metric = model_config.get("val_metric", "loss")
        self.batch_size = model_config.get("batch_size", 8192)
        self.model = None
        
        self.logger.info(f"Trainer initialized with network: {self.network_name}")
        self.logger.info(f"Prediction method: {self.predict_method}")
        self.logger.info(f"Validation metric: {self.val_metric}")
        self.logger.info(f"Batch size: {self.batch_size}")
    
    def _build_loss_function(self):
        """
        Build appropriate loss function.
        
        Returns:
            function: Loss function.
        """
        # Import necessary loss functions
        if self.mask_label:
            from src.training.losses import cumsum_mask_loss
            return cumsum_mask_loss
        else:
            from src.training.losses import cumsum_loss
            return cumsum_loss
    
    def build_model(self, use_cross_layer=True, use_time_attention=True, time_decay_factor=0.05):
        """
        Build model from configuration.
        
        Args:
            use_cross_layer (bool): Whether to use feature cross layer.
            use_time_attention (bool): Whether to use time attention mechanism.
            time_decay_factor (float): Time decay factor for attention.
            
        Returns:
            tf.keras.Model: Built model.
        """
        self.logger.info(f"Building model {self.network_name}")
        self.logger.info(f"Cross layer: {use_cross_layer}, Time attention: {use_time_attention}")
        
        # Dynamically import model module
        try:
            model_module = importlib.import_module(f"src.models.networks.{self.network_name}")
            model_class = getattr(model_module, f"{self.network_name}_Model")
            
            # Create model instance
            self.model = model_class(
                self.model_config,
                use_cross_layer=use_cross_layer,
                use_mixed_precision=False,  # Turn off mixed precision initially
                use_time_attention=use_time_attention,
                time_decay_factor=time_decay_factor
            )
            
            # Compile model with appropriate loss
            loss_func = self._build_loss_function()
            self.model.compile(
                loss=loss_func,
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.001, epsilon=1e-07)
            )
            
            return self.model
            
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to build model: {e}")
            raise
    
    def train(self, train_dataset, validation_dataset, epochs=50, early_stopping_patience=10):
        """
        Train model with provided datasets.
        
        Args:
            train_dataset (tf.data.Dataset): Training dataset.
            validation_dataset (tf.data.Dataset): Validation dataset.
            epochs (int): Maximum number of training epochs.
            early_stopping_patience (int): Early stopping patience.
            
        Returns:
            tf.keras.callbacks.History: Training history.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            raise ValueError("Model not built. Call build_model() first.")
            
        # Create output path for model weights
        model_path = f"{self.output_dir}/best_model.weights.h5"
        Path(model_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=early_stopping_patience,
                min_delta=0.001,
                mode='min',
                restore_best_weights=True
            ),
            tf.keras.callbacks.ModelCheckpoint(
                model_path,
                monitor='val_loss',
                mode='min',
                save_best_only=True,
                save_weights_only=True
            )
        ]
        
        # Log training start
        self.logger.info(f"Starting model training for {epochs} epochs")
        start_time = time.time()
        
        # Train model
        history = self.model.fit(
            train_dataset,
            validation_data=validation_dataset,
            epochs=epochs,
            callbacks=callbacks
        )
        
        # Save training history
        history_path = f"{self.output_dir}/training_history.json"
        with open(history_path, 'w') as f:
            json.dump(history.history, f)
            
        # Save model configuration
        config_path = f"{self.output_dir}/model_config.json"
        with open(config_path, 'w') as f:
            json.dump(self.model_config, f)
            
        # Log training completion
        training_time = time.time() - start_time
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        
        return history
    
    def load_model_weights(self, weights_path=None):
        """
        Load model weights from file.
        
        Args:
            weights_path (str, optional): Path to model weights file.
            
        Returns:
            bool: Whether loading was successful.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            return False
            
        if weights_path is None:
            weights_path = f"{self.output_dir}/best_model.weights.h5"
            
        try:
            self.model.load_weights(weights_path)
            self.logger.info(f"Model weights loaded from {weights_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to load model weights: {e}")
            return False
    
    def inference(self, df, raw_features, batch_size=10000):
        """
        Run model inference on dataframe.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            raw_features (dict): Raw feature configuration.
            batch_size (int): Maximum batch size for inference.
            
        Returns:
            np.ndarray: Model predictions.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            raise ValueError("Model not built. Call build_model() first.")
            
        from src.features.builder import FeatureBuilder
        feature_builder = FeatureBuilder()
        
        # For smaller datasets, run inference in one batch
        if len(df) <= batch_size:
            features = feature_builder.generate_dataset(df, raw_features)
            predictions = self.model(features).numpy()
        # For larger datasets, run inference in batches
        else:
            self.logger.info(f"Running batched inference with batch size {batch_size}")
            predictions_list = []
            inference_batches = int(len(df) / batch_size) + 1
            
            for idx in range(inference_batches):
                start, end = idx * batch_size, min((idx + 1) * batch_size, len(df))
                self.logger.info(f"Processing batch {idx+1}/{inference_batches}: {start}-{end}")
                
                batch_df = df.iloc[start:end]
                batch_features = feature_builder.generate_dataset(batch_df, raw_features)
                batch_predictions = self.model(batch_features).numpy()
                predictions_list.append(batch_predictions)
                
            predictions = np.concatenate(predictions_list, axis=0)
            
        # Post-process predictions to ensure sequential constraint
        for i in range(1, predictions.shape[1]):
            predictions[:, i] = np.maximum(predictions[:, i], predictions[:, i-1])
        
        # Ensure predictions are in valid range
        predictions = np.clip(predictions, 0.0, 0.999999)
        
        # Calculate incremental predictions if needed
        incremental = np.concatenate(
            (predictions[:, 0:1], np.clip(np.diff(predictions, axis=-1), 0.0, 1.0)),
            axis=-1
        )
        
        return predictions 