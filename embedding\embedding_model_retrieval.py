import pandas as pd
import numpy as np
import json
from pathlib import Path
import logging
import glob
import pickle
import faiss # Needs installation: pip install faiss-cpu or faiss-gpu
import os
from datetime import datetime
import argparse

# --- Global Constants ---
USER_ID_COL = "user_id"
# This will be the target label we check for in retrieved items
# It should match what embedding_model_train.py -> prepare_dataset creates
DEFAULT_LABEL_COL = "target_purchase_next_30d" 
DEFAULT_CANDIDATE_DATE = "20240531" # Default date for candidate user data
DEFAULT_DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")

# --- Logging Setup ---
def setup_logging(output_dir: Path):
    log_dir = output_dir / "logs_retrieval"
    log_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"retrieval_{timestamp}.log"
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    logging.info(f"Retrieval logs will be saved to: {log_file}")
    return log_file

# --- Data Loading Functions ---
def load_user_embeddings(embeddings_file_path: Path):
    """Loads user_ids and their embeddings from a pickle file."""
    if not embeddings_file_path.exists():
        logging.error(f"Embeddings file not found: {embeddings_file_path}")
        return None, None
    try:
        with open(embeddings_file_path, 'rb') as f:
            data = pickle.load(f)
        # Expected format: a dict with 'user_ids' (list/array) and 'embeddings' (numpy array)
        # This was the format used in the last run of embedding_model_train.py (extract_embeddings)
        if isinstance(data, dict) and 'user_ids' in data and 'embeddings' in data:
             user_ids = np.array(data['user_ids'])
             embeddings = np.array(data['embeddings'])
             logging.info(f"Loaded {len(user_ids)} user embeddings from {embeddings_file_path}")
             if embeddings.ndim == 2:
                 return user_ids, embeddings
             else:
                 logging.error(f"Embeddings array in {embeddings_file_path} does not have 2 dimensions (shape: {embeddings.shape}).")
                 return None, None

        # Fallback for older format where the pickle file might directly be (user_ids, embeddings) tuple
        elif isinstance(data, tuple) and len(data) == 2:
            logging.warning("Loaded embeddings from an older tuple format. Consider re-saving in dict format {'user_ids': ..., 'embeddings': ...}")
            user_ids, embeddings = data
            user_ids = np.array(user_ids)
            embeddings = np.array(embeddings)
            if embeddings.ndim == 2:
                 return user_ids, embeddings
            else:
                 logging.error(f"Embeddings array in {embeddings_file_path} (tuple format) does not have 2 dimensions (shape: {embeddings.shape}).")
                 return None, None
        else:
            logging.error(f"Unrecognized format in embeddings file: {embeddings_file_path}. Expected dict with 'user_ids' and 'embeddings' or a tuple.")
            return None, None

    except Exception as e:
        logging.error(f"Error loading embeddings from {embeddings_file_path}: {e}")
        return None, None

def load_candidate_data(candidate_data_dir: Path, user_id_col: str, label_col: str):
    """Loads candidate user data (user_id and label) from Parquet files in a specific directory."""
    # data_path = dataset_base_path / f"datetime={candidate_date}"
    data_path = candidate_data_dir # Use the directly provided path
    if not data_path.exists():
        logging.error(f"Candidate data path not found: {data_path}")
        return None

    files_to_load = glob.glob(str(data_path / "**/*.parquet"), recursive=True)
    if not files_to_load:
        logging.error(f"No Parquet files found in {data_path}")
        return None

    # REMOVED max_files logic for candidate loading
    # if max_files:
    #     files_to_load = files_to_load[:max_files]
    
    # logging.info(f"Loading {len(files_to_load)} candidate data files from {data_path} for date {candidate_date}...")
    logging.info(f"Loading {len(files_to_load)} candidate data files from {data_path}...")
    
    df_list = []
    required_cols = [user_id_col, label_col] 
    
    raw_label_col_for_processing = "m_purchase_days_nio_new_car"


    for f_path in files_to_load:
        try:
            cols_to_read_from_parquet = {user_id_col}
            if raw_label_col_for_processing != label_col and label_col == 'target_purchase_next_30d': 
                 cols_to_read_from_parquet.add(raw_label_col_for_processing)
            else: 
                 cols_to_read_from_parquet.add(label_col)

            df_part = pd.read_parquet(f_path, columns=list(cols_to_read_from_parquet))
            
            if label_col == 'target_purchase_next_30d' and raw_label_col_for_processing in df_part.columns:
                def safe_parse_purchase_days(val):
                    if isinstance(val, list) and len(val) > 0 and val[0] == 1: return 1
                    if isinstance(val, str):
                        try: 
                            parsed_val = json.loads(val)
                            if isinstance(parsed_val, list) and len(parsed_val) > 0 and parsed_val[0] == 1: return 1
                        except: return 0
                    return 0
                df_part[label_col] = df_part[raw_label_col_for_processing].apply(safe_parse_purchase_days)
            
            if user_id_col not in df_part.columns or label_col not in df_part.columns:
                logging.warning(f"File {f_path} is missing required columns ({user_id_col} or {label_col}). Actual cols: {df_part.columns}. Skipping.")
                continue
            df_list.append(df_part[required_cols])
        except Exception as e:
            logging.error(f"Error loading or processing candidate file {f_path}: {e}")
            
    if not df_list:
        logging.error("Failed to load any valid candidate data.")
        return None
        
    candidate_df = pd.concat(df_list, ignore_index=True).drop_duplicates(subset=[user_id_col])
    logging.info(f"Loaded {len(candidate_df)} unique candidate users with labels.")
    return candidate_df

# --- Faiss and Retrieval Functions ---
def build_faiss_index(embeddings: np.ndarray):
    """Builds a Faiss index for the given embeddings."""
    if embeddings is None or embeddings.shape[0] == 0:
        logging.error("Cannot build Faiss index: No embeddings provided.")
        return None
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    index.add(embeddings.astype(np.float32)) # Faiss expects float32
    logging.info(f"Built Faiss index with {index.ntotal} vectors of dimension {dimension}.")
    return index

def retrieve_similar_users_faiss(faiss_index: faiss.Index, query_embeddings: np.ndarray, all_indexed_user_ids: np.ndarray, top_k: int):
    """Retrieves top_k similar users for query_embeddings from the Faiss index."""
    if faiss_index is None:
        logging.error("Faiss index not available for retrieval.")
        return None, None
    
    distances, indices = faiss_index.search(query_embeddings.astype(np.float32), top_k) 
    
    retrieved_details = [] # List of lists, one per query embedding
    for i in range(indices.shape[0]): # For each query
        query_retrieved_list = []
        for j in range(indices.shape[1]): # For each retrieved item for that query
            retrieved_user_idx = indices[i, j]
            if retrieved_user_idx == -1: # Faiss might return -1 if not enough neighbors or if query is outside indexed items
                continue
            retrieved_user_id_actual = all_indexed_user_ids[retrieved_user_idx]
            distance_score = distances[i,j]
            query_retrieved_list.append((retrieved_user_id_actual, float(distance_score)))
        retrieved_details.append(query_retrieved_list)
        
    return retrieved_details


# --- Evaluation Metrics ---
def calculate_retrieval_evaluation_metrics(
    seed_to_retrieved_map: dict, 
    candidate_df_with_labels: pd.DataFrame, 
    user_id_col: str, 
    label_col: str, 
    k_values_for_eval: list
):
    metrics = {"precision_at_k": {}, "recall_at_k": {}}
    if not seed_to_retrieved_map:
        logging.warning("No retrieval results to evaluate.")
        return metrics

    true_labels_map = pd.Series(candidate_df_with_labels[label_col].values, index=candidate_df_with_labels[user_id_col]).to_dict()
    all_relevant_candidate_ids = set(candidate_df_with_labels[candidate_df_with_labels[label_col] == 1][user_id_col])
    total_relevant_in_candidates = len(all_relevant_candidate_ids)
    logging.info(f"Total relevant items (label=1) in candidate set for global recall: {total_relevant_in_candidates}")

    for k_eval in k_values_for_eval:
        sum_precision_at_k_for_all_seeds = 0.0
        num_valid_seed_users_for_precision = 0
        
        distinct_relevant_retrieved_ids_for_global_recall = set()

        for seed_id, retrieved_items_with_scores in seed_to_retrieved_map.items():
            if not retrieved_items_with_scores: continue
            num_valid_seed_users_for_precision +=1

            top_k_retrieved_ids = [item[0] for item in retrieved_items_with_scores[:k_eval]]
            
            hits_at_k_for_this_seed = 0
            for retrieved_user_id in top_k_retrieved_ids:
                if true_labels_map.get(retrieved_user_id, 0) == 1:
                    hits_at_k_for_this_seed += 1
                    distinct_relevant_retrieved_ids_for_global_recall.add(retrieved_user_id) # For global recall
            
            sum_precision_at_k_for_all_seeds += (hits_at_k_for_this_seed / k_eval) if k_eval > 0 else 0
            
        mean_precision_at_k = (sum_precision_at_k_for_all_seeds / num_valid_seed_users_for_precision) if num_valid_seed_users_for_precision > 0 else 0
        metrics["precision_at_k"][f"P@{k_eval}"] = mean_precision_at_k
        
        num_distinct_relevant_retrieved_globally = len(distinct_relevant_retrieved_ids_for_global_recall)
        global_recall_at_k = (num_distinct_relevant_retrieved_globally / total_relevant_in_candidates) if total_relevant_in_candidates > 0 else 0
        metrics["recall_at_k"][f"R@{k_eval}"] = global_recall_at_k

        logging.info(f"Metrics @{k_eval}: Mean_Precision={mean_precision_at_k:.4f} (over {num_valid_seed_users_for_precision} seeds), Global_Recall={global_recall_at_k:.4f} (hits={num_distinct_relevant_retrieved_globally}/total_relevant={total_relevant_in_candidates})")
    return metrics

# --- Main Orchestration ---
def main(args):
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    log_file = setup_logging(output_dir)
    logging.info(f"Starting retrieval process with args: {args}")

    embeddings_file = Path(args.embeddings_file)
    all_user_ids_from_pickle, all_embeddings_from_pickle = load_user_embeddings(embeddings_file)
    if all_user_ids_from_pickle is None: exit(1)
    
    # DataFrame of all users with embeddings (this is our Faiss candidate pool)
    embeddings_df = pd.DataFrame(all_embeddings_from_pickle, index=all_user_ids_from_pickle)

    candidate_info_df = load_candidate_data(
        Path(args.candidate_data_dir),
        args.user_id_col, 
        args.label_col,
    )
    if candidate_info_df is None: 
        logging.warning("Failed to load candidate info data. Proceeding with embeddings only, but evaluation will be limited.")
        # Create an empty df with correct columns if eval is still desired but no candidate info for labels
        candidate_info_df = pd.DataFrame(columns=[args.user_id_col, args.label_col])


    # Identify Seed Users: users in candidate_info_df with label=1 AND present in embeddings_df
    if args.label_col in candidate_info_df.columns:
        seed_user_ids_potential = candidate_info_df[candidate_info_df[args.label_col] == 1][args.user_id_col].unique()
    else:
        logging.warning(f"Label column '{args.label_col}' not found in candidate_info_df. Cannot identify seed users based on labels.")
        seed_user_ids_potential = np.array([])

    valid_seed_user_ids = embeddings_df.index.intersection(seed_user_ids_potential)
    
    if len(valid_seed_user_ids) == 0:
        logging.warning(f"No seed users (label='{args.label_col}'==1 and having embeddings) identified. Retrieval evaluation for these seeds will be skipped.")
        query_embeddings_for_seeds = np.array([])
    else:
        logging.info(f"Identified {len(valid_seed_user_ids)} valid seed users for retrieval.")
        query_embeddings_for_seeds = embeddings_df.loc[valid_seed_user_ids].values

    # Build Faiss Index on ALL users from the embeddings file
    # The user_ids corresponding to rows in all_embeddings_from_pickle is all_user_ids_from_pickle
    faiss_index = build_faiss_index(all_embeddings_from_pickle)
    if faiss_index is None: exit(1)

    all_retrieved_results_for_seeds = {} # {seed_id: [(retrieved_id, score), ...]}
    if query_embeddings_for_seeds.shape[0] > 0:
        # Retrieve top_k + 1 to handle self-retrieval
        # `all_user_ids_from_pickle` are the IDs for the `all_embeddings_from_pickle` that were indexed
        retrieved_details_per_query = retrieve_similar_users_faiss(
            faiss_index, 
            query_embeddings_for_seeds, 
            all_user_ids_from_pickle, # This is the array of IDs corresponding to the Faiss index order
            args.top_k_retrieval + 1 
        )

        for i, seed_id in enumerate(valid_seed_user_ids):
            filtered_retrieved_for_this_seed = []
            if retrieved_details_per_query and i < len(retrieved_details_per_query):
                for retrieved_user_id_actual, distance_score in retrieved_details_per_query[i]:
                    if str(retrieved_user_id_actual) == str(seed_id): # Ensure type consistency for comparison
                        continue 
                    filtered_retrieved_for_this_seed.append((retrieved_user_id_actual, distance_score))
                    if len(filtered_retrieved_for_this_seed) >= args.top_k_retrieval:
                        break
            all_retrieved_results_for_seeds[str(seed_id)] = filtered_retrieved_for_this_seed
        logging.info(f"Performed retrieval for {len(valid_seed_user_ids)} seed users.")
    else:
        logging.info("No seed user embeddings to query. Skipping retrieval loop for seeds.")

    retrieval_output_path = output_dir / "retrieval_results.json"
    with open(retrieval_output_path, 'w') as f:
        json.dump(all_retrieved_results_for_seeds, f, indent=4)
    logging.info(f"Retrieval results saved to {retrieval_output_path}")

    if valid_seed_user_ids.size > 0 and all_retrieved_results_for_seeds and not candidate_info_df.empty and args.label_col in candidate_info_df.columns:
        eval_k_values = sorted(list(set(filter(None,[10, 20, 50, 100, args.top_k_retrieval if args.top_k_retrieval else None]))))
        if not eval_k_values: eval_k_values = [100] # Default if top_k_retrieval is small or None
        
        retrieval_metrics = calculate_retrieval_evaluation_metrics(
            all_retrieved_results_for_seeds,
            candidate_info_df, # This df contains the labels for users
            args.user_id_col,
            args.label_col,
            eval_k_values
        )
        metrics_output_path = output_dir / "retrieval_evaluation_metrics.json"
        with open(metrics_output_path, 'w') as f:
            json.dump(retrieval_metrics, f, indent=4)
        logging.info(f"Retrieval evaluation metrics saved to {metrics_output_path}")
    else:
        logging.info("Skipping retrieval evaluation metrics: no valid seeds, no retrievals, candidate info missing, or label column missing.")
        
    logging.info(f"Retrieval script finished. Outputs in {output_dir}")
    logging.info(f"Main log file for retrieval: {log_file}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="User Embedding Retrieval Script using Faiss.")
    parser.add_argument("--embeddings_file", type=str, required=True, help="Path to the .pkl file containing user_ids and embeddings dictionary.")
    parser.add_argument("--candidate_data_dir", type=str, required=True, help="Full path to the directory containing candidate user Parquet files (e.g., data/dataset.../datetime=YYYYMMDD).")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save retrieval results and logs.")
    parser.add_argument("--user_id_col", type=str, default=USER_ID_COL, help=f"Name of the user ID column. Default: {USER_ID_COL}")
    parser.add_argument("--label_col", type=str, default=DEFAULT_LABEL_COL, help=f"Name of the true label column in candidate data. Default: {DEFAULT_LABEL_COL}")
    parser.add_argument("--top_k_retrieval", type=int, default=100, help="Number of top similar users to retrieve for each seed user. Default: 100")
    args = parser.parse_args()
    main(args) 