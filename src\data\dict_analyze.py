#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NIO数据字典简单分析工具
"""
import json
import pandas as pd
import numpy as np
import argparse
import os
from pathlib import Path

def analyze_data_dictionary(dict_path, output_path=None):
    """
    分析数据字典并生成简单文本报告
    
    Args:
        dict_path: 数据字典JSON文件路径
        output_path: 输出CSV文件路径（可选）
    """
    print(f"开始分析数据字典: {dict_path}")
    
    # 加载数据字典
    try:
        with open(dict_path, 'r', encoding='utf-8') as f:
            data_dict = json.load(f)
        print(f"数据字典加载成功: {len(data_dict)}个特征")
    except Exception as e:
        print(f"数据字典加载失败: {e}")
        return
    
    # 提取特征统计信息
    feature_stats = []
    for feature_name, feature_info in data_dict.items():
        stats = {}
        stats['feature_name'] = feature_name
        stats['description'] = feature_info.get('description', '')
        stats['business_domain'] = feature_info.get('business_domain', '未知')
        stats['data_type'] = feature_info.get('data_type', '未知')
        
        # 提取统计信息
        if 'statistics' in feature_info:
            statistics = feature_info['statistics']
            stats['count'] = statistics.get('count', 0)
            
            # 修正: 限制缺失率在 0-1 之间
            missing_rate = statistics.get('missing_rate', 0)
            if isinstance(missing_rate, (int, float)):
                if missing_rate > 1.0:
                    print(f"警告: 特征 {feature_name} 的缺失率异常: {missing_rate}，将修正为1.0")
                    missing_rate = min(1.0, missing_rate / 100) # 假定是百分比值
                missing_rate = max(0.0, min(1.0, missing_rate))  # 确保在0-1范围内
            else:
                missing_rate = 0.0
            stats['missing_rate'] = missing_rate
            
            # 不同类型特征的特殊统计
            if 'mean' in statistics:  # 数值型特征
                stats['min'] = statistics.get('min', 0)
                stats['max'] = statistics.get('max', 0)
                stats['mean'] = statistics.get('mean', 0)
                stats['median'] = statistics.get('median', 0)
                stats['std'] = statistics.get('std', 0)
                
                # 修正: 限制零值率在 0-1 之间
                zero_rate = statistics.get('zero_rate', 0)
                if isinstance(zero_rate, (int, float)):
                    if zero_rate > 1.0:
                        print(f"警告: 特征 {feature_name} 的零值率异常: {zero_rate}，将修正为1.0")
                        zero_rate = min(1.0, zero_rate / 100)  # 假定是百分比值
                    zero_rate = max(0.0, min(1.0, zero_rate))  # 确保在0-1范围内
                else:
                    zero_rate = 0.0
                stats['zero_rate'] = zero_rate
                
                stats['feature_type'] = 'numeric'
            elif 'unique_values' in statistics:  # 类别型特征
                stats['unique_values'] = statistics.get('unique_values', 0)
                stats['top_value'] = statistics.get('top_value', '')
                
                # 修正: 限制主要类别占比在 0-1 之间
                top_value_ratio = statistics.get('top_value_ratio', 0)
                if isinstance(top_value_ratio, (int, float)):
                    if top_value_ratio > 1.0:
                        top_value_ratio = min(1.0, top_value_ratio / 100)  # 假定是百分比值
                    top_value_ratio = max(0.0, min(1.0, top_value_ratio))  # 确保在0-1范围内
                else:
                    top_value_ratio = 0.0
                stats['top_value_ratio'] = top_value_ratio
                
                stats['feature_type'] = 'categorical'
            elif 'is_sequence' in statistics:  # 序列型特征
                stats['seq_min_length'] = statistics.get('seq_min_length', 0)
                stats['seq_max_length'] = statistics.get('seq_max_length', 0)
                stats['seq_mean_length'] = statistics.get('seq_mean_length', 0)
                
                # 修正: 限制非空率在 0-1 之间
                non_empty_rate = statistics.get('non_empty_rate', 0)
                if isinstance(non_empty_rate, (int, float)):
                    if non_empty_rate > 1.0:
                        non_empty_rate = min(1.0, non_empty_rate / 100)  # 假定是百分比值
                    non_empty_rate = max(0.0, min(1.0, non_empty_rate))  # 确保在0-1范围内
                else:
                    non_empty_rate = 0.0
                stats['non_empty_rate'] = non_empty_rate
                
                stats['feature_type'] = 'sequence'
            else:
                stats['feature_type'] = 'unknown'
        
        feature_stats.append(stats)
    
    # 转换为DataFrame
    df = pd.DataFrame(feature_stats)
    
    # 输出基本统计信息
    print("\n===== 特征类型统计 =====")
    type_counts = df['feature_type'].value_counts()
    for feature_type, count in type_counts.items():
        print(f"{feature_type:<12}: {count:>5} ({count/len(df)*100:.1f}%)")
    
    print("\n===== 业务域统计 =====")
    domain_counts = df['business_domain'].value_counts()
    for domain, count in domain_counts.items():
        if count > 1:  # 只显示有多个特征的业务域
            print(f"{domain:<30}: {count:>5} ({count/len(df)*100:.1f}%)")
    
    # 分析缺失值
    print("\n===== 缺失值统计 =====")
    missing_bins = [0, 0.01, 0.05, 0.1, 0.2, 0.5, 0.8, 0.95, 1.0]
    missing_counts = pd.cut(df['missing_rate'], bins=missing_bins).value_counts().sort_index()
    for i, (bin_range, count) in enumerate(missing_counts.items()):
        if i < len(missing_bins) - 1:  # 防止索引越界
            bin_start, bin_end = missing_bins[i], missing_bins[i+1]
            print(f"缺失率 {bin_start*100:>3.0f}%-{bin_end*100:.0f}%: {count:>5} ({count/len(df)*100:.1f}%)")
    
    # 数值特征零值分析
    numeric_df = df[df['feature_type'] == 'numeric']
    if not numeric_df.empty and 'zero_rate' in numeric_df.columns:
        print("\n===== 数值特征零值统计 =====")
        zero_bins = [0, 0.01, 0.05, 0.1, 0.2, 0.5, 0.8, 0.95, 1.0]
        zero_counts = pd.cut(numeric_df['zero_rate'], bins=zero_bins).value_counts().sort_index()
        for i, (bin_range, count) in enumerate(zero_counts.items()):
            if i < len(zero_bins) - 1:  # 防止索引越界
                bin_start, bin_end = zero_bins[i], zero_bins[i+1]
                print(f"零值率 {bin_start*100:>3.0f}%-{bin_end*100:.0f}%: {count:>5} ({count/len(numeric_df)*100:.1f}%)")
    
    # 识别问题特征
    print("\n===== 问题特征识别 =====")
    
    # 缺失率高的特征
    high_missing = df[df['missing_rate'] > 0.8]['feature_name'].tolist()
    extreme_missing = df[df['missing_rate'] > 0.95]['feature_name'].tolist()
    
    # 零值率高的特征
    high_zero_rate = []
    extreme_zero_rate = []
    if not numeric_df.empty and 'zero_rate' in numeric_df.columns:
        high_zero_rate = numeric_df[numeric_df['zero_rate'] > 0.8]['feature_name'].tolist()
        extreme_zero_rate = numeric_df[numeric_df['zero_rate'] > 0.95]['feature_name'].tolist()
    
    # 低方差特征
    low_variance = []
    if not numeric_df.empty and 'std' in numeric_df.columns and 'mean' in numeric_df.columns:
        # 避免除以0的情况
        cv = numeric_df.apply(lambda x: np.nan if pd.isna(x['mean']) or x['mean'] == 0 else abs(x['std'] / x['mean']), axis=1)
        low_variance = numeric_df[cv < 0.1].dropna()['feature_name'].tolist()
    
    # 类别特征分析
    dominated_category = []
    categorical_df = df[df['feature_type'] == 'categorical']
    if not categorical_df.empty and 'top_value_ratio' in categorical_df.columns:
        dominated_category = categorical_df[categorical_df['top_value_ratio'] > 0.95]['feature_name'].tolist()
    
    # 序列特征分析
    short_sequence = []
    sequence_df = df[df['feature_type'] == 'sequence']
    if not sequence_df.empty and 'seq_mean_length' in sequence_df.columns:
        short_sequence = sequence_df[sequence_df['seq_mean_length'] < 2]['feature_name'].tolist()
    
    # 输出问题特征数量
    print(f"高缺失率特征 (>80%): {len(high_missing)} ({len(high_missing)/len(df)*100:.1f}%)")
    print(f"极高缺失率特征 (>95%): {len(extreme_missing)} ({len(extreme_missing)/len(df)*100:.1f}%)")
    if not numeric_df.empty:
        print(f"高零值率特征 (>80%): {len(high_zero_rate)} ({len(high_zero_rate)/len(numeric_df)*100:.1f}% 数值特征)")
        print(f"极高零值率特征 (>95%): {len(extreme_zero_rate)} ({len(extreme_zero_rate)/len(numeric_df)*100:.1f}% 数值特征)")
        print(f"低方差特征: {len(low_variance)} ({len(low_variance)/len(numeric_df)*100:.1f}% 数值特征)")
    if not categorical_df.empty:
        print(f"单一类别主导特征: {len(dominated_category)} ({len(dominated_category)/len(categorical_df)*100:.1f}% 类别特征)")
    if not sequence_df.empty:
        print(f"序列过短特征: {len(short_sequence)} ({len(short_sequence)/len(sequence_df)*100:.1f}% 序列特征)")
    
    # 列出极高缺失率特征示例
    if extreme_missing:
        print("\n极高缺失率特征示例 (最多显示5个):")
        for feature in extreme_missing[:5]:
            feature_row = df[df['feature_name'] == feature].iloc[0]
            print(f"  {feature} ({feature_row['business_domain']}): 缺失率 {feature_row['missing_rate']*100:.1f}%")
    
    # 列出极高零值率特征示例
    if extreme_zero_rate:
        print("\n极高零值率特征示例 (最多显示5个):")
        for feature in extreme_zero_rate[:5]:
            feature_row = df[df['feature_name'] == feature].iloc[0]
            print(f"  {feature} ({feature_row['business_domain']}): 零值率 {feature_row['zero_rate']*100:.1f}%")
    
    # 输出特征统计文件
    if output_path:
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        df.to_csv(output_path, index=False)
        print(f"\n特征统计已保存至: {output_path}")
    
    # 简单建议
    print("\n===== 数据改进建议 =====")
    print("1. 缺失值处理:")
    print(f"   - 考虑移除缺失率超过95%的{len(extreme_missing)}个特征")
    print(f"   - 对其他高缺失率特征，使用合适的填充策略")
    
    print("2. 特征选择:")
    print(f"   - 考虑移除零值率极高或低方差的特征")
    print(f"   - 使用特征重要性评估来选择更具信息量的特征子集")
    
    if not sequence_df.empty:
        print("3. 序列特征处理:")
        print(f"   - 对于序列长度过短的{len(short_sequence)}个特征，考虑转换为数值特征")
        print(f"   - 使用序列填充和截断处理不等长序列")
    
    return df

def main():
    parser = argparse.ArgumentParser(description='NIO简单数据字典分析工具')
    parser.add_argument('--dict_path', type=str, 
                        default='data/dict/json/data_dictionary.json',
                        help='数据字典JSON文件路径')
    parser.add_argument('--output_csv', type=str, 
                        default='logs/feature_stats.csv',
                        help='输出CSV文件路径（可选）')
    
    args = parser.parse_args()
    analyze_data_dictionary(args.dict_path, args.output_csv)

if __name__ == "__main__":
    main() 