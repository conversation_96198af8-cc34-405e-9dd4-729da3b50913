# Imbalanced Learning Module

This module provides implementations of various techniques for handling class imbalance problems in machine learning.

## Overview

Class imbalance is a common problem in machine learning, where the distribution of classes in the training data is not equal. This can lead to biased models that perform poorly on minority classes. This module provides several techniques to address this issue.

## Features

### Resampling Techniques

- **SMOTE (Synthetic Minority Over-sampling Technique)**  
  Creates synthetic samples of the minority class to balance the dataset.
  
- **SMOTE-ENN**  
  Combines SMOTE with Edited Nearest Neighbors to first oversample the minority class and then clean the resulting dataset by removing samples that are misclassified by their nearest neighbors.
  
- **Optimized SMOTE-ENN**  
  Extends SMOTE-ENN by allowing control over the ratio of positive samples to generate relative to the number of negative samples.

### Dataset Balancing

- **Balanced Dataset Creation**  
  Creates a dataset with a controlled ratio of positive to negative samples in each batch.
  
- **Dynamic Balanced Dataset**  
  Allows changing the positive sample ratio during training.
  
- **Positive Sample Oversampling**  
  Duplicates positive samples to achieve a more balanced dataset.

## Usage

### Example: SMOTE

```python
from src.features.imbalance import apply_smote

# Apply SMOTE to balance the dataset
X_resampled, y_resampled = apply_smote(
    X_train, y_train, 
    k_neighbors=5, 
    random_state=42
)
```

### Example: SMOTE-ENN

```python
from src.features.imbalance import apply_smote_enn

# Apply SMOTE-ENN for more robust resampling
X_resampled, y_resampled = apply_smote_enn(
    X_train, y_train, 
    k_neighbors=5, 
    enn_k=3, 
    random_state=42
)
```

### Example: Optimized SMOTE-ENN

```python
from src.features.imbalance import apply_optimized_smote_enn

# Apply Optimized SMOTE-ENN with controlled positive ratio
X_resampled, y_resampled = apply_optimized_smote_enn(
    X_train, y_train, 
    smote_ratio=0.5,  # Target ratio of positive to negative samples
    k_neighbors=3, 
    enn_k=3, 
    random_state=42
)
```

### Example: Balanced Dataset

```python
from src.features.imbalance import create_balanced_dataset

# Create a balanced dataset with 30% positive samples in each batch
balanced_ds = create_balanced_dataset(
    original_dataset,
    pos_ratio=0.3,
    batch_size=4096
)
```

## References

1. Chawla, N. V., Bowyer, K. W., Hall, L. O., & Kegelmeyer, W. P. (2002). SMOTE: synthetic minority over-sampling technique. Journal of artificial intelligence research, 16, 321-357.

2. Batista, G. E., Prati, R. C., & Monard, M. C. (2004). A study of the behavior of several methods for balancing machine learning training data. ACM SIGKDD explorations newsletter, 6(1), 20-29.

3. He, H., & Garcia, E. A. (2009). Learning from imbalanced data. IEEE Transactions on knowledge and data engineering, 21(9), 1263-1284. 