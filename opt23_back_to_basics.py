#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化23: 回到基础 - 基于已验证有效技术的微调优化
停止大幅改动，回到基线方法，进行精细微调
基线: AUC 0.8219, PR-AUC 0.0467, Recall@840 0.2885
目标: 在保持AUC的基础上，微调提升PR-AUC和Recall
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def create_baseline_model_v1(input_dim):
    """创建基线模型版本1 - 与最佳基线相同的架构"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.1),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def create_baseline_model_v2(input_dim):
    """创建基线模型版本2 - 微调版本"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.1),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.0008),  # 微调学习率
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def create_baseline_model_v3(input_dim):
    """创建基线模型版本3 - 微调版本"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.35),  # 微调dropout
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.25),  # 微调dropout
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(16, activation='relu'),
        tf.keras.layers.Dropout(0.1),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.0012),  # 微调学习率
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def train_and_evaluate():
    """训练和评估回到基础的模型"""
    # 1. 加载数据 - 使用与基线完全相同的方法
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 特征选择 - 使用与基线相同的方法
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
    
    # 选择前150个特征（与基线相同）
    selected_features = numeric_cols[:150]
    X = df[selected_features].fillna(0).values
    y = data_loader.prepare_labels(df)
    
    logger.info(f"特征数: {len(selected_features)}")
    logger.info(f"数据形状: {X.shape}")
    
    # 3. 特征标准化 - 与基线相同
    logger.info("特征标准化...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 4. 数据分割 - 与基线相同
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. SMOTE重采样 - 与基线相同的参数
    logger.info("应用SMOTE重采样...")
    smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    logger.info(f"SMOTE重采样: {len(X_train)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
    
    # 6. 训练参数 - 与基线相同
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5, min_lr=1e-6)
    ]
    
    # 7. 训练多个基线模型
    logger.info("=== 训练基线模型集成 ===")
    
    models = []
    predictions = []
    model_creators = [create_baseline_model_v1, create_baseline_model_v2, create_baseline_model_v3]
    
    for i, create_model in enumerate(model_creators, 1):
        logger.info(f"=== 训练基线模型{i} ===")
        model = create_model(X_train_smote.shape[1])
        
        if i == 1:
            model.summary()
        
        model.fit(
            X_train_smote, y_train_smote,
            validation_data=(X_test, y_test),
            epochs=12, batch_size=512,  # 与基线相同的参数
            callbacks=callbacks,
            verbose=0
        )
        
        pred = model.predict(X_test, verbose=0)
        predictions.append(pred)
        models.append(model)
        
        # 单模型评估
        auc_score = roc_auc_score(y_test, pred)
        pr_auc = calculate_pr_auc(y_test, pred)
        precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
        
        logger.info(f"基线模型{i} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
        logger.info(f"基线模型{i} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
    
    # 8. 集成预测 - 简单平均
    ensemble_pred = np.mean(predictions, axis=0)
    
    # 9. 详细评估
    ensemble_auc = roc_auc_score(y_test, ensemble_pred)
    ensemble_pr_auc = calculate_pr_auc(y_test, ensemble_pred)
    ensemble_precision_at_840, ensemble_recall_at_840 = calculate_precision_recall_at_k(y_test, ensemble_pred, k=840)
    
    logger.info(f"=== 回到基础的模型结果 ===")
    logger.info(f"集成AUC: {ensemble_auc:.4f}")
    logger.info(f"集成PR-AUC: {ensemble_pr_auc:.4f}")
    logger.info(f"集成P@840: {ensemble_precision_at_840:.4f}")
    logger.info(f"集成R@840: {ensemble_recall_at_840:.4f}")
    
    # 与基线对比
    baseline_auc = 0.8219
    baseline_pr_auc = 0.0467
    baseline_recall = 0.2885
    
    auc_vs_baseline = ensemble_auc - baseline_auc
    pr_auc_vs_baseline = ensemble_pr_auc - baseline_pr_auc
    recall_vs_baseline = ensemble_recall_at_840 - baseline_recall
    
    logger.info(f"相比基线: AUC {auc_vs_baseline:+.4f}, PR-AUC {pr_auc_vs_baseline:+.4f}, Recall@840 {recall_vs_baseline:+.4f}")
    
    return {
        'optimization': 'back_to_basics',
        'ensemble_auc': float(ensemble_auc),
        'ensemble_pr_auc': float(ensemble_pr_auc),
        'precision_at_840': float(ensemble_precision_at_840),
        'recall_at_840': float(ensemble_recall_at_840),
        'feature_count': len(selected_features),
        'model_count': len(models),
        'baseline_auc': baseline_auc,
        'baseline_pr_auc': baseline_pr_auc,
        'baseline_recall': baseline_recall,
        'auc_vs_baseline': float(auc_vs_baseline),
        'pr_auc_vs_baseline': float(pr_auc_vs_baseline),
        'recall_vs_baseline': float(recall_vs_baseline),
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in predictions],
        'individual_pr_aucs': [float(calculate_pr_auc(y_test, pred)) for pred in predictions],
        'individual_recalls': [float(calculate_precision_recall_at_k(y_test, pred, k=840)[1]) for pred in predictions],
        'effective': bool(auc_vs_baseline > -0.01 and (pr_auc_vs_baseline > 0.001 or recall_vs_baseline > 0.01))
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_back_to_basics"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化23完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"集成AUC: {result['ensemble_auc']:.4f}")
    print(f"集成PR-AUC: {result['ensemble_pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: {result['feature_count']}")
    print(f"模型数: {result['model_count']}")
    print(f"相比基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"相比基线PR-AUC: {result['pr_auc_vs_baseline']:+.4f}")
    print(f"相比基线Recall@840: {result['recall_vs_baseline']:+.4f}")
    print(f"单模型AUC: {[f'{auc:.4f}' for auc in result['individual_aucs']]}")
    print(f"单模型PR-AUC: {[f'{pr_auc:.4f}' for pr_auc in result['individual_pr_aucs']]}")
    print(f"单模型Recall@840: {[f'{recall:.4f}' for recall in result['individual_recalls']]}")
    print(f"结果保存到: {exp_dir}/results.json")
