# NIO-EATV: Conversion Rate Prediction Framework

A modular machine learning framework for predicting conversion rates with systematic optimization capabilities. This framework has achieved significant performance improvements through iterative optimization, with PR-AUC improvements of over 100%.

## Project Structure

```
.
├── README.md                  # 项目说明文档
├── data/                      # 数据存储目录
│   └── dataset_nio_new_car_v15/ # 数据集
│       ├── 20240531_随机采样1%.parquet
│       ├── datetime=20240430/
│       └── datetime=20240531/
│
├── src/                       # 源代码目录
│   ├── configs/               # 配置文件目录
│   │   ├── models/            # 模型配置文件
│   │   │   └── sample_20250311_v7-20250311.json
│   │   └── datasets/          # 数据集配置文件
│   │       └── dataset_nio_new_car_v15.json
│   │
│   ├── data/                  # 数据处理相关模块
│   │   ├── __init__.py
│   │   ├── loader.py          # 数据加载器
│   │   └── preprocessor.py    # 数据预处理器
│   │
│   ├── features/              # 特征工程相关模块
│   │   ├── __init__.py
│   │   └── builder.py         # 特征构建器
│   │
│   ├── models/                # 模型相关模块
│   │   ├── __init__.py
│   │   ├── networks/          # 网络模型实现
│   │   │   ├── __init__.py
│   │   │   └── EPMMOENet.py   # 主要模型实现
│   │   └── layers/            # 自定义网络层
│   │       ├── __init__.py
│   │       └── layers.py      # 层实现
│   │
│   ├── training/              # 训练相关模块
│   │   ├── __init__.py
│   │   ├── trainer.py         # 模型训练器
│   │   └── losses.py          # 损失函数实现
│   │
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   ├── config_utils.py    # 配置管理器
│   │   └── migration_helper.py # 目录结构迁移助手
│   │
│   ├── evaluation/            # 评估相关模块和结果
│   │   ├── __init__.py
│   │   ├── evaluator.py       # 模型评估器
│   │   ├── 20250317_v1/       # 评估结果目录
│   │   └── 20250317_v2/ # 评估结果目录
│   │
│   └── train.py               # 主训练脚本
│
└── report/          # 报告目录
```

## Usage

### Quick Start with Optimization Framework

The framework includes an automated optimization system that can systematically improve model performance:

```bash
# Run baseline test
python run_optimization.py --mode baseline

# Run specific optimization
python run_optimization.py --mode optimize --opt batch_size

# Run optimization sequence
python run_optimization.py --mode sequence
```

### Training a Model

To train a model directly, use the `train.py` script:

```bash
python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=20250320_v1 --dataset_code=dataset_nio_new_car_v15

python src/train.py --model_code=sample_20250311_v7-20250311 --run_name=20250320_v2 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=10

运行模型训练
python src/train.py --model_code=sample_20250311_v7-20250311 --dataset_code=dataset_nio_new_car_v15 --evaluate_file="20240531_随机采样1%.parquet" --data_dir="data" --epochs=50 --patience=10
```

#### Arguments:

- `--model_code`: Model configuration file name (without .json extension)
- `--run_name`: Name for this training run
- `--dataset_code`: Dataset code/location
- `--evaluate_file`: Evaluation dataset filename
- `--epochs`: Maximum training epochs (default: 150)
- `--batch_size`: Training batch size (default: 8192)
- `--patience`: Early stopping patience (default: 10)
- `--use_cross_layer`: Whether to use feature cross layer (default: True)
- `--use_time_attention`: Whether to use time attention mechanism (default: True)
- `--log_file`: Custom log file path (optional)
- `--data_dir`: Root directory for data (default: current directory)
- `--output_dir`: Output directory for evaluation results (default: src/evaluation)

### Configuration Files

The framework uses two types of configuration files:

1. **Model configuration** (e.g., `src/configs/models/sample_20250311_v7-20250311.json`): Defines model architecture, features, and training parameters.
2. **Dataset configuration** (e.g., `src/configs/datasets/dataset_nio_new_car_v15.json`): Defines dataset preprocessing parameters.

### Directory Organization

- **Data**: All datasets should be placed in the `data/` directory
- **Configurations**: All configuration files should be placed in the `src/configs/` directory
- **Evaluation results**: All evaluation results will be stored in the `src/evaluation/` directory, organized by run name

## Performance Achievements

### Current Best Results
- **Month_1 AUC**: 0.9122 (99th percentile performance)
- **Month_1 PR-AUC**: 0.0758 (107% improvement over baseline)
- **Overall AUC**: 0.8241 (1.68% improvement)
- **Overall PR-AUC**: 0.0171 (90% improvement)

### Successful Optimizations
1. **Batch Size Optimization**: Reducing batch_size from 8192 to 256 → +107% PR-AUC improvement
2. **Extended Training**: Increasing epochs from 15 to 25 → +79% PR-AUC improvement

## Model Features

- **EPMMOENet Architecture**: Expert-based Multi-Modal Mixture of Experts Network
- Multimodal architecture with time series attention
- Feature crossing for enhanced feature interactions
- Time decay attention for sequential features
- Mixed precision training for better performance
- Early stopping and automatic evaluation
- Systematic optimization framework with automated evaluation

## Contributing

To contribute to this framework:

1. Add new model implementations in `src/models/networks/`
2. Add new layer implementations in `src/models/layers/`
3. Extend evaluation metrics in `src/evaluation/` 


---
```
机器学习模型全流程
├── 1. 数据加载与准备
│   ├── DataLoader
│   │   ├── 加载训练集数据 (train_dates)
│   │   ├── 加载测试集数据 (test_dates)
│   │   ├── 加载评估集数据 (evaluate_file)
│   │   └── 支持外挂数据集 (extra_datasets)
│   └── 数据配置
│       ├── 特征列配置 (feature_padding_dict)
│       └── 日期分区配置
│
├── 2. 数据预处理
│   ├── DataPreprocessor
│   │   ├── 基本特征处理 (preprocess_features)
│   │   │   ├── 类型转换
│   │   │   ├── 缺失值填充
│   │   │   └── 异常值处理
│   │   ├── 模型特征处理 (preprocess_model_features)
│   │   │   ├── 非序列特征
│   │   │   │   ├── 类别特征 (StringLookup)
│   │   │   │   │   ├── 词表生成
│   │   │   │   │   └── 低频值合并
│   │   │   │   ├── 连续特征 (Bucket)
│   │   │   │   ├── 降维特征 (Dense)
│   │   │   │   └── 直接特征 (Embedding)
│   │   │   └── 序列特征 (VarLen)
│   │   │       ├── 序列填充
│   │   │       └── 序列截断
│   │   └── 标签处理 (process_purchase_labels)
│   │       ├── 转换购买标签
│   │       └── 生成累计标签
│   │
│   └── 数据集分割
│       ├── 训练集
│       └── 测试集
│
├── 3. 特征工程
│   └── FeatureBuilder
│       ├── 特征转换
│       │   ├── 非序列特征转换
│       │   └── 序列特征转换
│       └── 数据集生成 (generate_dataset)
│           ├── 训练数据集
│           ├── 测试数据集
│           └── 推理特征字典
│
├── 4. 模型架构
│   ├── EPMMOENet_Model
│   │   ├── 输入层
│   │   │   ├── 非序列特征嵌入
│   │   │   └── 序列特征嵌入
│   │   ├── 特征处理层
│   │   │   ├── 非序列特征分支
│   │   │   ├── 序列特征分支
│   │   │   │   ├── GRU层处理
│   │   │   │   └── 时间衰减注意力机制
│   │   │   └── 场景特征加权
│   │   ├── 特征交叉层 (可选)
│   │   └── 预测塔层
│   │       ├── BatchNormalization
│   │       ├── Dropout
│   │       └── Dense层级
│   │
│   └── 自定义层
│       ├── CrossLayer (特征交叉增强)
│       └── TimeSeriesAttention (时间衰减注意力)
│
├── 5. 模型训练
│   └── ModelTrainer
│       ├── 构建模型
│       ├── 配置损失函数
│       │   ├── 累计损失 (cumsum_loss)
│       │   └── 带掩码累计损失 (cumsum_mask_loss)
│       ├── 训练配置
│       │   ├── 批量大小 (batch_size)
│       │   ├── 训练轮数 (epochs)
│       │   ├── 早停策略 (early_stopping_patience)
│       │   └── 混合精度训练 (mixed_precision)
│       └── 模型保存
│
└── 6. 模型评估
    └── ModelEvaluator
        ├── 预测生成
        ├── 指标计算
        │   ├── ROC-AUC
        │   ├── PR-AUC
        │   ├── Precision@K
        │   └── Recall@K
        ├── 评估维度
        │   ├── 整体评估 (calculate_overall_metrics)
        │   └── 按月评估 (calculate_monthly_metrics)
        └── 结果存储与可视化
```

# 转换率预测框架流程脑图

```
机器学习模型全流程
├── 1. 数据加载与准备
│   ├── DataLoader
│   │   ├── 加载训练集数据 (train_dates)
│   │   ├── 加载测试集数据 (test_dates)
│   │   ├── 加载评估集数据 (evaluate_file)
│   │   └── 支持外挂数据集 (extra_datasets)
│   └── 数据配置
│       ├── 特征列配置 (feature_padding_dict)
│       └── 日期分区配置
│
├── 2. 数据预处理
│   ├── DataPreprocessor
│   │   ├── 基本特征处理 (preprocess_features)
│   │   │   ├── 类型转换
│   │   │   ├── 缺失值填充
│   │   │   └── 异常值处理
│   │   ├── 模型特征处理 (preprocess_model_features)
│   │   │   ├── 非序列特征
│   │   │   │   ├── 类别特征 (StringLookup)
│   │   │   │   │   ├── 词表生成
│   │   │   │   │   └── 低频值合并
│   │   │   │   ├── 连续特征 (Bucket)
│   │   │   │   ├── 降维特征 (Dense)
│   │   │   │   └── 直接特征 (Embedding)
│   │   │   └── 序列特征 (VarLen)
│   │   │       ├── 序列填充
│   │   │       └── 序列截断
│   │   └── 标签处理 (process_purchase_labels)
│   │       ├── 转换购买标签
│   │       └── 生成累计标签
│   │
│   └── 数据集分割
│       ├── 训练集
│       └── 测试集
│
├── 3. 特征工程
│   └── FeatureBuilder
│       ├── 特征转换
│       │   ├── 非序列特征转换
│       │   └── 序列特征转换
│       └── 数据集生成 (generate_dataset)
│           ├── 训练数据集
│           ├── 测试数据集
│           └── 推理特征字典
│
├── 4. 模型架构
│   ├── EPMMOENet_Model
│   │   ├── 输入层
│   │   │   ├── 非序列特征嵌入
│   │   │   └── 序列特征嵌入
│   │   ├── 特征处理层
│   │   │   ├── 非序列特征分支
│   │   │   ├── 序列特征分支
│   │   │   │   ├── GRU层处理
│   │   │   │   └── 时间衰减注意力机制
│   │   │   └── 场景特征加权
│   │   ├── 特征交叉层 (可选)
│   │   └── 预测塔层
│   │       ├── BatchNormalization
│   │       ├── Dropout
│   │       └── Dense层级
│   │
│   └── 自定义层
│       ├── CrossLayer (特征交叉增强)
│       └── TimeSeriesAttention (时间衰减注意力)
│
├── 5. 模型训练
│   └── ModelTrainer
│       ├── 构建模型
│       ├── 配置损失函数
│       │   ├── 累计损失 (cumsum_loss)
│       │   └── 带掩码累计损失 (cumsum_mask_loss)
│       ├── 训练配置
│       │   ├── 批量大小 (batch_size)
│       │   ├── 训练轮数 (epochs)
│       │   ├── 早停策略 (early_stopping_patience)
│       │   └── 混合精度训练 (mixed_precision)
│       └── 模型保存
│
└── 6. 模型评估
    └── ModelEvaluator
        ├── 预测生成
        ├── 指标计算
        │   ├── ROC-AUC
        │   ├── PR-AUC
        │   ├── Precision@K
        │   └── Recall@K
        ├── 评估维度
        │   ├── 整体评估 (calculate_overall_metrics)
        │   └── 按月评估 (calculate_monthly_metrics)
        └── 结果存储与可视化
```