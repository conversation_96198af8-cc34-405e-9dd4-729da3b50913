{"answer_sales_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "接听销售电话行为发生天序列"}, "answer_sales_call_duration_s_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "接听销售电话行为时间长度序列"}, "universe_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "全量ATV核心行为发生天序列"}, "universe_action_code_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "全量ATV核心行为code序列"}, "universe_action_cnt_1d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天的全量ATV核心行为发生次数"}, "universe_action_cnt_7d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天的全量ATV核心行为发生次数"}, "universe_action_cnt_14d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去14天的全量ATV核心行为发生次数"}, "universe_action_cnt_30d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天的全量ATV核心行为发生次数"}, "universe_action_cnt_60d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天的全量ATV核心行为发生次数"}, "universe_action_cnt_90d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天的全量ATV核心行为发生次数"}, "universe_action_cnt_180d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天的全量ATV核心行为发生次数"}, "finance_calc_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "APP金融计算器行为发生天序列"}, "finance_calc_veh_model_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "APP金融计算器行为配置车型序列"}, "reg_leads_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "留资行为发生天序列"}, "reg_leads_action_code_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "留资相关行为发生名称序列"}, "reg_leads_reg_channel_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "留资行为一级渠道序列"}, "test_drive_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为发生天序列"}, "test_drive_td_veh_model_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为车型序列"}, "test_drive_td_type_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为类型序列"}, "test_drive_is_main_participant_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为参与者序列"}, "test_drive_td_score_level_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为得分等级序列"}, "test_drive_td_duration_min_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾行为持续时间序列"}, "veh_cgf_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "APP心愿单相关行为发生天序列"}, "veh_cgf_action_code_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "APP心愿单相关行为发生名称序列"}, "veh_cgf_cfg_veh_model_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "APP心愿单行为配置车型序列"}, "fellow_follow_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天销售跟进次数"}, "fellow_follow_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天销售跟进次数"}, "fellow_follow_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天销售跟进次数"}, "fellow_follow_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天销售跟进次数"}, "fellow_follow_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天销售跟进次数"}, "fellow_follow_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天销售跟进次数"}, "fellow_follow_180d_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生销售跟进距今的天数"}, "answer_sales_call_duration_s_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天接听销售电话时长"}, "answer_sales_call_duration_s_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天接听销售电话时长"}, "answer_sales_call_duration_s_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天接听销售电话时长"}, "answer_sales_call_duration_s_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天接听销售电话时长"}, "answer_sales_call_duration_s_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天接听销售电话时长"}, "answer_sales_call_duration_s_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天接听销售电话时长"}, "answer_sales_call_duration_s_last": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "最后一次接听销售电话时长"}, "user_core_user_gender": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "性别"}, "user_core_user_age_group": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "年龄段"}, "user_core_resident_city": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "常驻城市"}, "user_core_is_nio_employee": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "当前是否员工"}, "user_core_pred_career_type": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "预测当前职业"}, "user_core_pred_marriage_status": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "预测当前婚姻状态"}, "user_core_pred_has_children": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "预测当前是否有孩子"}, "user_core_pred_has_other_vehicle": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "预测当前是否有非本品车"}, "user_core_pred_other_vehicle_brand": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "预测当前有非本品车品牌"}, "user_core_pred_other_vehicle_age_year": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "预测当前非本品车拥车时长(年份)"}, "user_core_nio_user_identity": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "nio用户身份"}, "user_core_onvo_user_identity": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "onvo用户身份"}, "user_core_nio_owned_vehicle_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "拥有nio车辆数"}, "user_core_nio_community_identity": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "社区身份"}, "user_core_nio_value": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "蔚来值"}, "user_core_user_curr_credit_amount": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "积分"}, "user_core_user_app_fans_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "app粉丝数据"}, "user_core_nio_has_inviter": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "nio是否有邀请人"}, "fellow_follow_planned_purchase_time": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_purchase_reason": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_compare_brands": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_battery_endurance": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_brand_recognition": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_has_car_owner_friends": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_design_appearance": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_decision_maker": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_current_brands": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_business_operation": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_compare_car_models": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_intention_nio_confirm": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_intention_test_drive": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_intention_nio_car_models": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_cost_performance": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_waiting_for_price_off": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_satisfaction_safety": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_approved_energy_system": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_place_of_residence": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_main_car_user": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_current_car_models": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_is_bba_car_owner": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_has_consulted_exchange": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_factor_sex": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_charging_pile_installation": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_factor_budget": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_fail_car_brand": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_purchase_status": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_preferred_car_model": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_fail_car_model": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_color_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_children_num": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_current_vehicle_age": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_income_level": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_factor_age": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_plate_qualification": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_has_drivers_license": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_has_fixed_parking_space": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_loan_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_compare_car_type": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_length_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_size_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_comfort_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_performance_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_capacity_preference": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_waiting_for_new_model": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_concerns_benefit_drop": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_family_opinions_divided": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_car_ownership_count": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_purchased_gas_car": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_purchased_electric_car": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_factor_industry": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_business_type": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_education_level": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_factor_hobbies": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_added_fl_wechat": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_visited_store_proactively": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_satisfaction_test_drive": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "fellow_follow_payer_type": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": null}, "intention_status": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "意向状态(0:未知/1:进行中/2:已大定/3:已交付/4:已战败)"}, "intention_level": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "意向等级(n:未评级/1:购车1/2:购车2/3:购车3/4:购车l/5:定/6:锁/7:退)"}, "intention_test_drive_level": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "试驾意向等级(n:未评级/1:常规邀约/2:重点邀约/3:待安排/4:已安排/5:未出席"}, "intention_stage": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "意向阶段(0:意向阶段/1:销售机会阶段)"}, "intention_src_camp_l1_channel": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "意向来源一级渠道"}, "intention_product_type": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "有意向车型"}, "intention_fail_reason_code": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "战败原因(1:无法取得有效联系/2:主观原因放弃购买或者决定购买竞品/3:无购车计划或不愿意体验/4:不满足购车条件/5:共同用车人或授权用车人/9:大定退单/10:疑义线索/99:其他"}, "intention_is_active": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "是否主动意向"}, "intention_is_walkin": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "是否walkin意向"}, "intention_is_first": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "是否该用户的第一次意向"}, "intention_create_time_days": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "创建时间离当前的日期间隔"}, "intention_opportunity_create_days": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "意向进入机会阶段时间离当前的日期间隔"}, "intention_intention_fail_days": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "意向战败时间离当前的日期间隔"}, "intention_latest_in_forecast_days": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "最近进组离当前的日期间隔"}, "intention_latest_out_forecast_days": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "最近出组离当前的日期间隔"}, "user_create_days": {"base_value": "1440", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "用户创建时间"}, "user_register_days": {"base_value": "1440", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "用户注册时间"}, "user_core_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "ATV核心行为发生天序列"}, "user_core_action_code_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "ATV核心行为code序列"}, "user_core_action_cnt_1d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天的ATV核心行为发生次数"}, "user_core_action_cnt_7d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天的ATV核心行为发生次数"}, "user_core_action_cnt_14d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去14天的ATV核心行为发生次数"}, "user_core_action_cnt_30d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天的ATV核心行为发生次数"}, "user_core_action_cnt_60d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天的ATV核心行为发生次数"}, "user_core_action_cnt_90d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天的ATV核心行为发生次数"}, "user_core_action_cnt_180d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天的ATV核心行为发生次数"}, "user_car_core_action_day_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "ATV核心行为发生天序列"}, "user_car_core_action_code_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "ATV核心行为code序列"}, "user_car_core_action_veh_model_seq": {"base_value": null, "data_type": "string", "min_value": null, "max_value": null, "feature_name": "ATV核心行为对应的车型序列"}, "user_car_core_action_cnt_1d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天的ATV核心行为发生次数"}, "user_car_core_action_cnt_7d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天的ATV核心行为发生次数"}, "user_car_core_action_cnt_14d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去14天的ATV核心行为发生次数"}, "user_car_core_action_cnt_30d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天的ATV核心行为发生次数"}, "user_car_core_action_cnt_60d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天的ATV核心行为发生次数"}, "user_car_core_action_cnt_90d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天的ATV核心行为发生次数"}, "user_car_core_action_cnt_180d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天的ATV核心行为发生次数"}, "user_core_visit_nioapp_login_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App登陆态访问的次数"}, "user_core_visit_nioapp_login_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App登陆态访问的次数"}, "user_core_visit_nioapp_login_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App登陆态访问的次数"}, "user_core_visit_nioapp_login_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App登陆态访问的次数"}, "user_core_visit_nioapp_login_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App登陆态访问的次数"}, "user_core_visit_nioapp_login_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App登陆态访问的次数"}, "user_core_exp_charging_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生体验/使用NIO充电服务的次数"}, "user_core_exp_charging_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生体验/使用NIO充电服务的次数"}, "user_core_exp_charging_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生体验/使用NIO充电服务的次数"}, "user_core_exp_charging_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生体验/使用NIO充电服务的次数"}, "user_core_exp_charging_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生体验/使用NIO充电服务的次数"}, "user_core_exp_charging_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生体验/使用NIO充电服务的次数"}, "user_core_exp_maintenance_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生体验/使用NIO自费维保的次数"}, "user_core_exp_maintenance_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生体验/使用NIO自费维保的次数"}, "user_core_exp_maintenance_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生体验/使用NIO自费维保的次数"}, "user_core_exp_maintenance_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生体验/使用NIO自费维保的次数"}, "user_core_exp_maintenance_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生体验/使用NIO自费维保的次数"}, "user_core_exp_maintenance_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生体验/使用NIO自费维保的次数"}, "user_core_checkin_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App签到的次数"}, "user_core_checkin_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App签到的次数"}, "user_core_checkin_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App签到的次数"}, "user_core_checkin_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App签到的次数"}, "user_core_checkin_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App签到的次数"}, "user_core_checkin_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App签到的次数"}, "user_core_collect_checkin_prize_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App领取签到盲盒的次数"}, "user_core_collect_checkin_prize_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App领取签到盲盒的次数"}, "user_core_collect_checkin_prize_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App领取签到盲盒的次数"}, "user_core_collect_checkin_prize_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App领取签到盲盒的次数"}, "user_core_collect_checkin_prize_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App领取签到盲盒的次数"}, "user_core_collect_checkin_prize_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App领取签到盲盒的次数"}, "user_core_view_nl_hp_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览NIOLife首页的次数"}, "user_core_view_nl_hp_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览NIOLife首页的次数"}, "user_core_view_nl_hp_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览NIOLife首页的次数"}, "user_core_view_nl_hp_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览NIOLife首页的次数"}, "user_core_view_nl_hp_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览NIOLife首页的次数"}, "user_core_view_nl_hp_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览NIOLife首页的次数"}, "user_core_buy_nl_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App下单NIO Life的次数"}, "user_core_buy_nl_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App下单NIO Life的次数"}, "user_core_buy_nl_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App下单NIO Life的次数"}, "user_core_buy_nl_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App下单NIO Life的次数"}, "user_core_buy_nl_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App下单NIO Life的次数"}, "user_core_buy_nl_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App下单NIO Life的次数"}, "user_core_view_cm_hp_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览车商城首页的次数"}, "user_core_view_cm_hp_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览车商城首页的次数"}, "user_core_view_cm_hp_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览车商城首页的次数"}, "user_core_view_cm_hp_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览车商城首页的次数"}, "user_core_view_cm_hp_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览车商城首页的次数"}, "user_core_view_cm_hp_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览车商城首页的次数"}, "user_core_view_cm_dp_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览车商城商详页的次数"}, "user_core_view_cm_dp_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览车商城商详页的次数"}, "user_core_view_cm_dp_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览车商城商详页的次数"}, "user_core_view_cm_dp_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览车商城商详页的次数"}, "user_core_view_cm_dp_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览车商城商详页的次数"}, "user_core_view_cm_dp_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览车商城商详页的次数"}, "user_core_sign_up_comm_act_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App报名社区活动的次数"}, "user_core_sign_up_comm_act_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App报名社区活动的次数"}, "user_core_sign_up_comm_act_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App报名社区活动的次数"}, "user_core_sign_up_comm_act_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App报名社区活动的次数"}, "user_core_sign_up_comm_act_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App报名社区活动的次数"}, "user_core_sign_up_comm_act_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App报名社区活动的次数"}, "user_core_view_hp_rec_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_rec_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_rec_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_rec_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_rec_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_rec_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App首页推荐浏览的次数"}, "user_core_view_hp_news_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App浏览首页资讯的次数"}, "user_core_view_hp_news_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App浏览首页资讯的次数"}, "user_core_view_hp_news_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App浏览首页资讯的次数"}, "user_core_view_hp_news_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App浏览首页资讯的次数"}, "user_core_view_hp_news_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App浏览首页资讯的次数"}, "user_core_view_hp_news_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App浏览首页资讯的次数"}, "user_core_view_pgc_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App浏览PGC的次数"}, "user_core_view_pgc_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App浏览PGC的次数"}, "user_core_view_pgc_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App浏览PGC的次数"}, "user_core_view_pgc_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App浏览PGC的次数"}, "user_core_view_pgc_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App浏览PGC的次数"}, "user_core_view_pgc_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App浏览PGC的次数"}, "user_core_view_ugc_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App浏览UGC的次数"}, "user_core_view_ugc_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App浏览UGC的次数"}, "user_core_view_ugc_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App浏览UGC的次数"}, "user_core_view_ugc_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App浏览UGC的次数"}, "user_core_view_ugc_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App浏览UGC的次数"}, "user_core_view_ugc_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App浏览UGC的次数"}, "user_core_search_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO App搜索的次数"}, "user_core_search_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO App搜索的次数"}, "user_core_search_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO App搜索的次数"}, "user_core_search_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO App搜索的次数"}, "user_core_search_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO App搜索的次数"}, "user_core_search_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO App搜索的次数"}, "user_core_view_veh_intro_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览车的产品介绍的次数"}, "user_core_view_veh_intro_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览车的产品介绍的次数"}, "user_core_view_veh_intro_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览车的产品介绍的次数"}, "user_core_view_veh_intro_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览车的产品介绍的次数"}, "user_core_view_veh_intro_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览车的产品介绍的次数"}, "user_core_view_veh_intro_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览车的产品介绍的次数"}, "user_core_view_service_intro_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览服务介绍的次数"}, "user_core_view_service_intro_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览服务介绍的次数"}, "user_core_view_service_intro_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览服务介绍的次数"}, "user_core_view_service_intro_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览服务介绍的次数"}, "user_core_view_service_intro_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览服务介绍的次数"}, "user_core_view_service_intro_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览服务介绍的次数"}, "user_core_view_nh_intro_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览门店介绍的次数"}, "user_core_view_nh_intro_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览门店介绍的次数"}, "user_core_view_nh_intro_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览门店介绍的次数"}, "user_core_view_nh_intro_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览门店介绍的次数"}, "user_core_view_nh_intro_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览门店介绍的次数"}, "user_core_view_nh_intro_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览门店介绍的次数"}, "user_core_view_swap_intro_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览换电布局介绍的次数"}, "user_core_view_swap_intro_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览换电布局介绍的次数"}, "user_core_view_swap_intro_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览换电布局介绍的次数"}, "user_core_view_swap_intro_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览换电布局介绍的次数"}, "user_core_view_swap_intro_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览换电布局介绍的次数"}, "user_core_view_swap_intro_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览换电布局介绍的次数"}, "user_core_view_sim_swap_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App浏览模拟换电的次数"}, "user_core_view_sim_swap_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App浏览模拟换电的次数"}, "user_core_view_sim_swap_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App浏览模拟换电的次数"}, "user_core_view_sim_swap_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App浏览模拟换电的次数"}, "user_core_view_sim_swap_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App浏览模拟换电的次数"}, "user_core_view_sim_swap_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App浏览模拟换电的次数"}, "user_core_visit_nio_wmp_login_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO小程序登陆态访问的次数"}, "user_core_visit_nio_wmp_login_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO小程序登陆态访问的次数"}, "user_core_visit_nio_wmp_login_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO小程序登陆态访问的次数"}, "user_core_visit_nio_wmp_login_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO小程序登陆态访问的次数"}, "user_core_visit_nio_wmp_login_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO小程序登陆态访问的次数"}, "user_core_visit_nio_wmp_login_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO小程序登陆态访问的次数"}, "user_core_visit_veh_cgf_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO打开心愿单配置的次数"}, "user_core_visit_veh_cgf_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO打开心愿单配置的次数"}, "user_core_visit_veh_cgf_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO打开心愿单配置的次数"}, "user_core_visit_veh_cgf_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO打开心愿单配置的次数"}, "user_core_visit_veh_cgf_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO打开心愿单配置的次数"}, "user_core_visit_veh_cgf_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO打开心愿单配置的次数"}, "user_core_save_veh_cgf_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO保存心愿单配置的次数"}, "user_core_save_veh_cgf_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO保存心愿单配置的次数"}, "user_core_save_veh_cgf_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO保存心愿单配置的次数"}, "user_core_save_veh_cgf_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO保存心愿单配置的次数"}, "user_core_save_veh_cgf_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO保存心愿单配置的次数"}, "user_core_save_veh_cgf_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO保存心愿单配置的次数"}, "user_core_del_veh_cgf_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO删除心愿单配置的次数"}, "user_core_del_veh_cgf_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO删除心愿单配置的次数"}, "user_core_del_veh_cgf_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO删除心愿单配置的次数"}, "user_core_del_veh_cgf_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO删除心愿单配置的次数"}, "user_core_del_veh_cgf_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO删除心愿单配置的次数"}, "user_core_del_veh_cgf_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO删除心愿单配置的次数"}, "user_core_view_veh_cfg_params_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App查看车的详细配置表的次数"}, "user_core_view_veh_cfg_params_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App查看车的详细配置表的次数"}, "user_core_view_veh_cfg_params_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App查看车的详细配置表的次数"}, "user_core_view_veh_cfg_params_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App查看车的详细配置表的次数"}, "user_core_view_veh_cfg_params_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App查看车的详细配置表的次数"}, "user_core_view_veh_cfg_params_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App查看车的详细配置表的次数"}, "user_core_view_finance_calc_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App查看金融计算器的次数"}, "user_core_view_finance_calc_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App查看金融计算器的次数"}, "user_core_view_finance_calc_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App查看金融计算器的次数"}, "user_core_view_finance_calc_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App查看金融计算器的次数"}, "user_core_view_finance_calc_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App查看金融计算器的次数"}, "user_core_view_finance_calc_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App查看金融计算器的次数"}, "user_core_view_mileage_calc_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App查看里程计算器的次数"}, "user_core_view_mileage_calc_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App查看里程计算器的次数"}, "user_core_view_mileage_calc_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App查看里程计算器的次数"}, "user_core_view_mileage_calc_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App查看里程计算器的次数"}, "user_core_view_mileage_calc_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App查看里程计算器的次数"}, "user_core_view_mileage_calc_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App查看里程计算器的次数"}, "user_core_answer_sales_pc_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生接听销售电话的次数"}, "user_core_answer_sales_pc_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生接听销售电话的次数"}, "user_core_answer_sales_pc_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生接听销售电话的次数"}, "user_core_answer_sales_pc_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生接听销售电话的次数"}, "user_core_answer_sales_pc_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生接听销售电话的次数"}, "user_core_answer_sales_pc_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生接听销售电话的次数"}, "user_core_fl_offline_im_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_offline_im_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_offline_im_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_offline_im_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_offline_im_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_offline_im_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生FL即时通讯（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生FL面谈（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生FL面谈（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生FL面谈（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生FL面谈（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生FL面谈（人工录入）的次数"}, "user_core_fl_f2f_interview_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生FL面谈（人工录入）的次数"}, "user_core_view_mate_materials_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生Mate分享素材查看的次数"}, "user_core_view_mate_materials_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生Mate分享素材查看的次数"}, "user_core_view_mate_materials_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生Mate分享素材查看的次数"}, "user_core_view_mate_materials_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生Mate分享素材查看的次数"}, "user_core_view_mate_materials_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生Mate分享素材查看的次数"}, "user_core_view_mate_materials_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生Mate分享素材查看的次数"}, "user_core_buy_cm_nioapp_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App下单车商城的次数"}, "user_core_buy_cm_nioapp_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App下单车商城的次数"}, "user_core_buy_cm_nioapp_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App下单车商城的次数"}, "user_core_buy_cm_nioapp_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App下单车商城的次数"}, "user_core_buy_cm_nioapp_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App下单车商城的次数"}, "user_core_buy_cm_nioapp_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App下单车商城的次数"}, "user_core_book_td_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生预约试驾的次数"}, "user_core_book_td_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生预约试驾的次数"}, "user_core_book_td_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生预约试驾的次数"}, "user_core_book_td_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生预约试驾的次数"}, "user_core_book_td_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生预约试驾的次数"}, "user_core_book_td_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生预约试驾的次数"}, "user_core_book_td_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生预约试驾距今的天数"}, "user_core_exp_td_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生体验试驾的次数"}, "user_core_exp_td_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生体验试驾的次数"}, "user_core_exp_td_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生体验试驾的次数"}, "user_core_exp_td_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生体验试驾的次数"}, "user_core_exp_td_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生体验试驾的次数"}, "user_core_exp_td_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生体验试驾的次数"}, "user_core_exp_td_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生体验试驾距今的天数"}, "user_core_pay_ncar_intention_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO支付意向金的次数"}, "user_core_pay_ncar_intention_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生NIO支付意向金距今的天数"}, "user_core_pay_ncar_dp_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO支付定金的次数"}, "user_core_pay_ncar_dp_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生NIO支付定金距今的天数"}, "user_core_lock_ncar_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO锁单的次数"}, "user_core_lock_ncar_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生NIO锁单距今的天数"}, "user_core_cancal_ncar_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO取消订单的次数"}, "user_core_cancal_ncar_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生NIO取消订单距今的天数"}, "user_core_take_ncar_dl_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生NIO提车的次数"}, "user_core_take_ncar_dl_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生NIO提车距今的天数"}, "user_core_view_used_veh_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生App查看二手车服务的次数"}, "user_core_view_used_veh_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生App查看二手车服务的次数"}, "user_core_view_used_veh_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生App查看二手车服务的次数"}, "user_core_view_used_veh_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生App查看二手车服务的次数"}, "user_core_view_used_veh_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生App查看二手车服务的次数"}, "user_core_view_used_veh_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生App查看二手车服务的次数"}, "user_core_view_used_veh_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生App查看二手车服务距今的天数"}, "user_core_visit_nh_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生线下到门店的次数"}, "user_core_visit_nh_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生线下到门店的次数"}, "user_core_visit_nh_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生线下到门店的次数"}, "user_core_visit_nh_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生线下到门店的次数"}, "user_core_visit_nh_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生线下到门店的次数"}, "user_core_visit_nh_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生线下到门店的次数"}, "user_core_visit_nh_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生线下到门店距今的天数"}, "user_core_visit_roadshow_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生线下到路演点的次数"}, "user_core_visit_roadshow_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生线下到路演点的次数"}, "user_core_visit_roadshow_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生线下到路演点的次数"}, "user_core_visit_roadshow_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生线下到路演点的次数"}, "user_core_visit_roadshow_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生线下到路演点的次数"}, "user_core_visit_roadshow_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生线下到路演点的次数"}, "user_core_visit_roadshow_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生线下到路演点距今的天数"}, "user_core_visit_autoshow_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天发生线下参加车展的次数"}, "user_core_visit_autoshow_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天发生线下参加车展的次数"}, "user_core_visit_autoshow_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天发生线下参加车展的次数"}, "user_core_visit_autoshow_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天发生线下参加车展的次数"}, "user_core_visit_autoshow_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天发生线下参加车展的次数"}, "user_core_visit_autoshow_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天发生线下参加车展的次数"}, "user_core_visit_autoshow_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生线下参加车展距今的天数"}, "user_core_unfirst_reg_leads_nio_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在1天内发生的次数"}, "user_core_unfirst_reg_leads_nio_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在7天内发生的次数"}, "user_core_unfirst_reg_leads_nio_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在30天内发生的次数"}, "user_core_unfirst_reg_leads_nio_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在60天内发生的次数"}, "user_core_unfirst_reg_leads_nio_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在90天内发生的次数"}, "user_core_unfirst_reg_leads_nio_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在180天内发生的次数"}, "user_core_unfirst_reg_leads_nio_1d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在1天内发生的天数"}, "user_core_unfirst_reg_leads_nio_7d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在7天内发生的天数"}, "user_core_unfirst_reg_leads_nio_30d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在30天内发生的天数"}, "user_core_unfirst_reg_leads_nio_60d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在60天内发生的天数"}, "user_core_unfirst_reg_leads_nio_90d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在90天内发生的天数"}, "user_core_unfirst_reg_leads_nio_180d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为在180天内发生的天数"}, "user_core_unfirst_reg_leads_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "非首次留资行为最近一次发生距今天数(180天窗口)"}, "user_core_follow_fellow_wecom_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在1天内发生的次数"}, "user_core_follow_fellow_wecom_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在7天内发生的次数"}, "user_core_follow_fellow_wecom_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在30天内发生的次数"}, "user_core_follow_fellow_wecom_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在60天内发生的次数"}, "user_core_follow_fellow_wecom_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在90天内发生的次数"}, "user_core_follow_fellow_wecom_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在180天内发生的次数"}, "user_core_follow_fellow_wecom_1d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在1天内发生的天数"}, "user_core_follow_fellow_wecom_7d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在7天内发生的天数"}, "user_core_follow_fellow_wecom_30d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在30天内发生的天数"}, "user_core_follow_fellow_wecom_60d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在60天内发生的天数"}, "user_core_follow_fellow_wecom_90d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在90天内发生的天数"}, "user_core_follow_fellow_wecom_180d_day": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信在180天内发生的天数"}, "user_core_follow_fellow_wecom_DSLA": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "加fl企业微信最近一次发生距今天数"}, "user_core_delete_fellow_wecom_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在1天内发生的次数"}, "user_core_delete_fellow_wecom_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在7天内发生的次数"}, "user_core_delete_fellow_wecom_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在30天内发生的次数"}, "user_core_delete_fellow_wecom_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在60天内发生的次数"}, "user_core_delete_fellow_wecom_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在90天内发生的次数"}, "user_core_delete_fellow_wecom_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在180天内发生的次数"}, "user_core_delete_fellow_wecom_1d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在1天内发生的天数"}, "user_core_delete_fellow_wecom_7d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在7天内发生的天数"}, "user_core_delete_fellow_wecom_30d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在30天内发生的天数"}, "user_core_delete_fellow_wecom_60d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在60天内发生的天数"}, "user_core_delete_fellow_wecom_90d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在90天内发生的天数"}, "user_core_delete_fellow_wecom_180d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信在180天内发生的天数"}, "user_core_delete_fellow_wecom_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "删除fl企业微信最近一次发生距今天数"}, "user_core_im_consult_wecom_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在1天内发生的次数"}, "user_core_im_consult_wecom_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在7天内发生的次数"}, "user_core_im_consult_wecom_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在30天内发生的次数"}, "user_core_im_consult_wecom_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在60天内发生的次数"}, "user_core_im_consult_wecom_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在90天内发生的次数"}, "user_core_im_consult_wecom_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在180天内发生的次数"}, "user_core_im_consult_wecom_1d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在1天内发生的天数"}, "user_core_im_consult_wecom_7d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在7天内发生的天数"}, "user_core_im_consult_wecom_30d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在30天内发生的天数"}, "user_core_im_consult_wecom_60d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在60天内发生的天数"}, "user_core_im_consult_wecom_90d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在90天内发生的天数"}, "user_core_im_consult_wecom_180d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询在180天内发生的天数"}, "user_core_im_consult_wecom_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信发起咨询最近一次发生距今天数"}, "user_core_message_group_wecom_1d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在1天内发生的次数"}, "user_core_message_group_wecom_7d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在7天内发生的次数"}, "user_core_message_group_wecom_30d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在30天内发生的次数"}, "user_core_message_group_wecom_60d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在60天内发生的次数"}, "user_core_message_group_wecom_90d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在90天内发生的次数"}, "user_core_message_group_wecom_180d_cnt": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在180天内发生的次数"}, "user_core_message_group_wecom_1d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在1天内发生的天数"}, "user_core_message_group_wecom_7d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在7天内发生的天数"}, "user_core_message_group_wecom_30d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在30天内发生的天数"}, "user_core_message_group_wecom_60d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在60天内发生的天数"}, "user_core_message_group_wecom_90d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在90天内发生的天数"}, "user_core_message_group_wecom_180d_day": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息在180天内发生的天数"}, "user_core_message_group_wecom_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "企业微信群发消息最近一次发生距今天数"}, "user_core_first_reg_leads_nio_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天最近一次发生首次留资距今的天数"}, "app_search_intention_cnt_1d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去1天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_7d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去7天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_14d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去14天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_30d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去30天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_60d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去60天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_90d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去90天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_cnt_180d": {"base_value": null, "data_type": "double", "min_value": null, "max_value": null, "feature_name": "过去180天的APP首页-高意向词-搜索行为发生次数"}, "app_search_intention_DSLA": {"base_value": "180", "data_type": "double", "min_value": null, "max_value": null, "feature_name": "最近一次APP首页-高意向词-搜索行为发生天"}}