#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进版训练脚本 - 逐步接近原始功能
"""
import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='改进版转化率预测模型训练')
    
    parser.add_argument('--run_name', type=str, default=f'improved_run_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                        help='训练运行名称')
    parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=1024, help='批次大小')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心值')
    parser.add_argument('--use_multitask', action='store_true', help='使用多任务学习')
    parser.add_argument('--use_enhanced_features', action='store_true', help='使用增强特征')
    
    return parser.parse_args()

def load_config():
    """加载配置文件"""
    logger.info("加载配置文件...")
    
    # 加载模型配置
    model_config_path = "src/configs/sample_20250311_v7-20250311.json"
    with open(model_config_path, 'r') as f:
        model_config = json.load(f)
    
    # 加载数据集配置
    dataset_config_path = "data/configs/dataset_nio_new_car_v15.json"
    with open(dataset_config_path, 'r') as f:
        dataset_config = json.load(f)
    
    logger.info("配置文件加载成功")
    return model_config, dataset_config

def load_data():
    """加载数据"""
    logger.info("加载数据...")
    
    # 加载评估数据集
    eval_file = "data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet"
    df = pd.read_parquet(eval_file)
    
    logger.info(f"数据加载成功: {df.shape}")
    return df

def prepare_features(df, model_config, use_enhanced=False):
    """准备特征"""
    logger.info("准备特征...")
    
    # 获取配置中的特征
    raw_features = model_config.get("RawFeature", {})
    general_features = model_config.get("InputGeneral", {}).get("features", [])
    
    # 选择数值特征
    numeric_cols = []
    for col in df.columns:
        if df[col].dtype in ['int64', 'float64'] and col not in ['user_id', 'datetime']:
            numeric_cols.append(col)
    
    # 限制特征数量
    selected_features = numeric_cols[:100]  # 使用更多特征
    
    if use_enhanced:
        logger.info("应用特征工程...")
        # 简单的特征工程
        df['action_density'] = df.get('universe_action_cnt_7d', 0) / 7
        df['action_trend'] = df.get('universe_action_cnt_7d', 0) - df.get('universe_action_cnt_1d', 0)
        selected_features.extend(['action_density', 'action_trend'])
    
    logger.info(f"选择特征数: {len(selected_features)}")
    
    # 准备特征矩阵
    X = df[selected_features].fillna(0).values
    
    # 准备标签
    if 'purchase_days_nio_new_car_total' in df.columns:
        target_col = df['purchase_days_nio_new_car_total']
        target_numeric = pd.to_numeric(target_col, errors='coerce').fillna(0)
        y = (target_numeric > 0).astype(int).values
    else:
        logger.warning("未找到目标列，使用随机标签")
        y = np.random.randint(0, 2, size=len(df))
    
    logger.info(f"特征形状: {X.shape}")
    logger.info(f"正样本比例: {y.mean():.4f}")
    
    return X, y, selected_features

def create_model(input_dim, use_multitask=False):
    """创建模型"""
    logger.info(f"创建模型，输入维度: {input_dim}")
    
    if use_multitask:
        # 多任务模型
        inputs = tf.keras.layers.Input(shape=(input_dim,))
        
        # 共享层
        shared = tf.keras.layers.Dense(128, activation='relu')(inputs)
        shared = tf.keras.layers.Dropout(0.3)(shared)
        shared = tf.keras.layers.Dense(64, activation='relu')(shared)
        shared = tf.keras.layers.Dropout(0.3)(shared)
        
        # 任务特定层
        task1 = tf.keras.layers.Dense(32, activation='relu')(shared)
        task1_output = tf.keras.layers.Dense(1, activation='sigmoid', name='month_1')(task1)
        
        task2 = tf.keras.layers.Dense(32, activation='relu')(shared)
        task2_output = tf.keras.layers.Dense(1, activation='sigmoid', name='month_3')(task2)
        
        task3 = tf.keras.layers.Dense(32, activation='relu')(shared)
        task3_output = tf.keras.layers.Dense(1, activation='sigmoid', name='month_6')(task3)
        
        model = tf.keras.Model(inputs=inputs, outputs=[task1_output, task2_output, task3_output])
        
        model.compile(
            optimizer='adam',
            loss={'month_1': 'binary_crossentropy', 'month_3': 'binary_crossentropy', 'month_6': 'binary_crossentropy'},
            loss_weights={'month_1': 1.0, 'month_3': 0.5, 'month_6': 0.3},
            metrics=['accuracy', 'auc']
        )
    else:
        # 单任务模型
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy', 'auc']
        )
    
    return model

def train_model(model, X_train, y_train, X_test, y_test, args):
    """训练模型"""
    logger.info("开始训练...")
    
    # 设置回调
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=args.patience, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5),
        tf.keras.callbacks.ModelCheckpoint(f'{args.run_name}_best_model.h5', save_best_only=True)
    ]
    
    # 处理类别不平衡
    pos_weight = len(y_train) / (2 * np.sum(y_train)) if np.sum(y_train) > 0 else 1.0
    logger.info(f"正样本权重: {pos_weight:.2f}")
    
    # 训练
    if args.use_multitask:
        # 多任务训练
        y_train_dict = {'month_1': y_train, 'month_3': y_train, 'month_6': y_train}
        y_test_dict = {'month_1': y_test, 'month_3': y_test, 'month_6': y_test}
        
        history = model.fit(
            X_train, y_train_dict,
            validation_data=(X_test, y_test_dict),
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=callbacks,
            verbose=1
        )
    else:
        # 单任务训练
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=callbacks,
            class_weight={0: 1.0, 1: pos_weight},
            verbose=1
        )
    
    return history

def evaluate_model(model, X_test, y_test, args):
    """评估模型"""
    logger.info("评估模型...")
    
    if args.use_multitask:
        predictions = model.predict(X_test)
        y_pred_proba = predictions[0]  # 使用第一个任务的预测
    else:
        y_pred_proba = model.predict(X_test)
    
    y_pred = (y_pred_proba > 0.5).astype(int)
    
    # 计算指标
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    logger.info(f"测试准确率: {accuracy:.4f}")
    logger.info(f"测试AUC: {auc:.4f}")
    
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    return {'accuracy': accuracy, 'auc': auc}

def main():
    """主函数"""
    args = parse_args()
    logger.info(f"开始改进版训练: {args.run_name}")
    
    # 1. 加载配置
    model_config, dataset_config = load_config()
    
    # 2. 加载数据
    df = load_data()
    
    # 3. 准备特征
    X, y, feature_names = prepare_features(df, model_config, args.use_enhanced_features)
    
    # 4. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. 创建模型
    model = create_model(X_train.shape[1], args.use_multitask)
    model.summary()
    
    # 6. 训练模型
    history = train_model(model, X_train, y_train, X_test, y_test, args)
    
    # 7. 评估模型
    results = evaluate_model(model, X_test, y_test, args)
    
    # 8. 保存结果
    results.update({
        'run_name': args.run_name,
        'feature_count': len(feature_names),
        'train_samples': len(X_train),
        'test_samples': len(X_test),
        'positive_rate': float(y.mean()),
        'use_multitask': args.use_multitask,
        'use_enhanced_features': args.use_enhanced_features
    })
    
    with open(f'{args.run_name}_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"改进版训练完成！结果保存到: {args.run_name}_results.json")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
