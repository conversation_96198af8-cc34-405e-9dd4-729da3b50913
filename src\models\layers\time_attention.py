"""
时间注意力层模块
"""
import tensorflow as tf
from tensorflow.keras.layers import Layer

class TimeAttentionLayer(Layer):
    """
    时间注意力层，根据时间步长应用衰减因子
    """
    
    def __init__(self, decay_factor=0.05, **kwargs):
        """
        初始化时间注意力层
        
        Args:
            decay_factor (float): 衰减因子，控制随时间步增加的衰减程度
        """
        super(TimeAttentionLayer, self).__init__(**kwargs)
        self.decay_factor = decay_factor
    
    def build(self, input_shape):
        """
        构建层
        
        Args:
            input_shape: 输入形状
        """
        # 不需要权重
        super(TimeAttentionLayer, self).build(input_shape)
    
    def call(self, inputs):
        """
        前向传播
        
        Args:
            inputs: [features, time_step] 包含特征张量和时间步索引
            
        Returns:
            tf.Tensor: 时间调整后的特征
        """
        features, time_step = inputs
        
        # 计算时间衰减权重
        if isinstance(time_step, int):
            # 如果time_step是整数常量
            decay = tf.exp(-self.decay_factor * time_step)
        else:
            # 如果time_step是张量
            decay = tf.exp(-self.decay_factor * tf.cast(time_step, tf.float32))
        
        # 应用衰减
        attentioned_features = features * decay
        
        return attentioned_features
    
    def get_config(self):
        """获取配置"""
        config = super(TimeAttentionLayer, self).get_config()
        config.update({
            "decay_factor": self.decay_factor
        })
        return config
        
    @classmethod
    def from_config(cls, config):
        """从配置创建实例"""
        return cls(**config) 