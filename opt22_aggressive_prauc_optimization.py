#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化22: 激进的PR-AUC和Recall优化
停止瞎搞，专注于真正有效的技术，激进优化PR-AUC和Recall
基线: AUC 0.8219, PR-AUC 0.0467, Recall@840 0.2885
目标: 大幅提升PR-AUC和Recall，即使AUC略有下降也可以接受
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler, RobustScaler
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.combine import SMOTEENN, SMOTETomek

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def aggressive_feature_selection(df):
    """激进的特征选择 - 专注于高信息量特征"""
    logger.info("激进特征选择...")
    
    # 1. 基础数值特征
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
    
    # 2. 激进选择：只要高方差、高相关性的特征
    df_numeric = df[numeric_cols].fillna(0)
    
    # 计算特征方差
    feature_vars = df_numeric.var()
    high_var_features = feature_vars[feature_vars > feature_vars.quantile(0.7)].index.tolist()
    
    # 计算与目标的相关性（使用简单的相关系数）
    # 处理目标变量的数据类型问题
    target_col = df['purchase_days_nio_new_car_total'].copy()
    target_col = pd.to_numeric(target_col, errors='coerce')  # 转换为数值，无法转换的设为NaN
    y = (target_col.fillna(999) < 180).astype(int)
    correlations = df_numeric.corrwith(pd.Series(y))
    high_corr_features = correlations[abs(correlations) > correlations.abs().quantile(0.8)].index.tolist()
    
    # 合并高方差和高相关性特征
    selected_features = list(set(high_var_features + high_corr_features))
    
    # 确保至少有100个特征
    if len(selected_features) < 100:
        remaining_features = [col for col in numeric_cols if col not in selected_features]
        selected_features.extend(remaining_features[:100-len(selected_features)])
    
    # 最多200个特征
    selected_features = selected_features[:200]
    
    logger.info(f"激进特征选择完成: {len(selected_features)}个特征")
    
    return df[selected_features].fillna(0).values, selected_features

def create_prauc_optimized_model(input_dim, focus_on_recall=True):
    """创建专门优化PR-AUC和Recall的模型"""
    
    # 使用更深的网络，专门为不平衡数据设计
    model = tf.keras.Sequential([
        # 输入层 - 更大的容量
        tf.keras.layers.Dense(1024, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.5),
        
        # 深度特征提取
        tf.keras.layers.Dense(512, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(256, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        # 输出层
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    # 专门为不平衡数据优化的编译配置
    if focus_on_recall:
        # 优化Recall：使用更高的class_weight
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['auc', 'precision', 'recall']
        )
    else:
        # 平衡优化
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['auc', 'precision', 'recall']
        )
    
    return model

def train_with_aggressive_sampling(X_train, y_train, X_test, y_test):
    """使用激进的采样策略训练多个模型"""
    
    models = []
    predictions = []
    sampling_methods = []
    
    # 1. 激进SMOTE - 更高的采样比例
    logger.info("=== 训练激进SMOTE模型 ===")
    smote_aggressive = SMOTE(sampling_strategy=0.1, random_state=42, k_neighbors=3)
    X_smote, y_smote = smote_aggressive.fit_resample(X_train, y_train)
    
    model1 = create_prauc_optimized_model(X_train.shape[1], focus_on_recall=True)
    
    # 使用class_weight进一步平衡
    class_weight = {0: 1.0, 1: 10.0}  # 给正样本更高权重
    
    model1.fit(
        X_smote, y_smote,
        validation_data=(X_test, y_test),
        epochs=20, batch_size=256,
        class_weight=class_weight,
        callbacks=[
            tf.keras.callbacks.EarlyStopping(patience=8, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(patience=4, factor=0.5)
        ],
        verbose=0
    )
    
    pred1 = model1.predict(X_test, verbose=0)
    models.append(model1)
    predictions.append(pred1)
    sampling_methods.append("Aggressive_SMOTE")
    
    # 评估第一个模型
    auc1 = roc_auc_score(y_test, pred1)
    pr_auc1 = calculate_pr_auc(y_test, pred1)
    p_at_840_1, r_at_840_1 = calculate_precision_recall_at_k(y_test, pred1, k=840)
    logger.info(f"激进SMOTE模型 - AUC: {auc1:.4f}, PR-AUC: {pr_auc1:.4f}, P@840: {p_at_840_1:.4f}, R@840: {r_at_840_1:.4f}")
    
    # 2. ADASYN - 自适应合成采样
    logger.info("=== 训练ADASYN模型 ===")
    try:
        adasyn = ADASYN(sampling_strategy=0.08, random_state=42, n_neighbors=3)
        X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
        
        model2 = create_prauc_optimized_model(X_train.shape[1], focus_on_recall=True)
        
        model2.fit(
            X_adasyn, y_adasyn,
            validation_data=(X_test, y_test),
            epochs=20, batch_size=256,
            class_weight={0: 1.0, 1: 8.0},
            callbacks=[
                tf.keras.callbacks.EarlyStopping(patience=8, restore_best_weights=True),
                tf.keras.callbacks.ReduceLROnPlateau(patience=4, factor=0.5)
            ],
            verbose=0
        )
        
        pred2 = model2.predict(X_test, verbose=0)
        models.append(model2)
        predictions.append(pred2)
        sampling_methods.append("ADASYN")
        
        # 评估第二个模型
        auc2 = roc_auc_score(y_test, pred2)
        pr_auc2 = calculate_pr_auc(y_test, pred2)
        p_at_840_2, r_at_840_2 = calculate_precision_recall_at_k(y_test, pred2, k=840)
        logger.info(f"ADASYN模型 - AUC: {auc2:.4f}, PR-AUC: {pr_auc2:.4f}, P@840: {p_at_840_2:.4f}, R@840: {r_at_840_2:.4f}")
        
    except Exception as e:
        logger.warning(f"ADASYN训练失败: {e}")
    
    # 3. BorderlineSMOTE - 边界线SMOTE
    logger.info("=== 训练BorderlineSMOTE模型 ===")
    try:
        borderline_smote = BorderlineSMOTE(sampling_strategy=0.07, random_state=42, k_neighbors=3)
        X_borderline, y_borderline = borderline_smote.fit_resample(X_train, y_train)
        
        model3 = create_prauc_optimized_model(X_train.shape[1], focus_on_recall=False)
        
        model3.fit(
            X_borderline, y_borderline,
            validation_data=(X_test, y_test),
            epochs=20, batch_size=256,
            class_weight={0: 1.0, 1: 6.0},
            callbacks=[
                tf.keras.callbacks.EarlyStopping(patience=8, restore_best_weights=True),
                tf.keras.callbacks.ReduceLROnPlateau(patience=4, factor=0.5)
            ],
            verbose=0
        )
        
        pred3 = model3.predict(X_test, verbose=0)
        models.append(model3)
        predictions.append(pred3)
        sampling_methods.append("BorderlineSMOTE")
        
        # 评估第三个模型
        auc3 = roc_auc_score(y_test, pred3)
        pr_auc3 = calculate_pr_auc(y_test, pred3)
        p_at_840_3, r_at_840_3 = calculate_precision_recall_at_k(y_test, pred3, k=840)
        logger.info(f"BorderlineSMOTE模型 - AUC: {auc3:.4f}, PR-AUC: {pr_auc3:.4f}, P@840: {p_at_840_3:.4f}, R@840: {r_at_840_3:.4f}")
        
    except Exception as e:
        logger.warning(f"BorderlineSMOTE训练失败: {e}")
    
    # 4. 极端重采样 - 非常高的正样本比例
    logger.info("=== 训练极端重采样模型 ===")
    smote_extreme = SMOTE(sampling_strategy=0.15, random_state=42, k_neighbors=3)
    X_extreme, y_extreme = smote_extreme.fit_resample(X_train, y_train)
    
    model4 = create_prauc_optimized_model(X_train.shape[1], focus_on_recall=True)
    
    model4.fit(
        X_extreme, y_extreme,
        validation_data=(X_test, y_test),
        epochs=15, batch_size=128,  # 更小的batch size
        class_weight={0: 1.0, 1: 15.0},  # 极端的class weight
        callbacks=[
            tf.keras.callbacks.EarlyStopping(patience=6, restore_best_weights=True),
            tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5)
        ],
        verbose=0
    )
    
    pred4 = model4.predict(X_test, verbose=0)
    models.append(model4)
    predictions.append(pred4)
    sampling_methods.append("Extreme_SMOTE")
    
    # 评估第四个模型
    auc4 = roc_auc_score(y_test, pred4)
    pr_auc4 = calculate_pr_auc(y_test, pred4)
    p_at_840_4, r_at_840_4 = calculate_precision_recall_at_k(y_test, pred4, k=840)
    logger.info(f"极端重采样模型 - AUC: {auc4:.4f}, PR-AUC: {pr_auc4:.4f}, P@840: {p_at_840_4:.4f}, R@840: {r_at_840_4:.4f}")
    
    return models, predictions, sampling_methods

def train_and_evaluate():
    """训练和评估激进PR-AUC优化模型"""
    # 1. 加载数据
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 激进特征选择
    X, feature_names = aggressive_feature_selection(df)
    y = data_loader.prepare_labels(df)
    
    # 3. 使用RobustScaler而不是StandardScaler（对异常值更鲁棒）
    logger.info("特征标准化...")
    scaler = RobustScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 4. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    logger.info(f"训练集正样本比例: {y_train.mean():.4f}, 测试集正样本比例: {y_test.mean():.4f}")
    
    # 5. 激进采样训练
    models, predictions, sampling_methods = train_with_aggressive_sampling(X_train, y_train, X_test, y_test)
    
    # 6. 智能集成 - 根据PR-AUC加权
    logger.info("=== 智能集成优化 ===")
    
    # 计算每个模型的PR-AUC权重
    pr_aucs = []
    for pred in predictions:
        pr_auc = calculate_pr_auc(y_test, pred)
        pr_aucs.append(pr_auc)
    
    # 使用PR-AUC作为权重进行加权平均
    pr_aucs = np.array(pr_aucs)
    weights = pr_aucs / pr_aucs.sum()
    
    # 加权集成
    weighted_ensemble = np.average(predictions, axis=0, weights=weights)
    
    # 7. 详细评估
    ensemble_auc = roc_auc_score(y_test, weighted_ensemble)
    ensemble_pr_auc = calculate_pr_auc(y_test, weighted_ensemble)
    ensemble_precision_at_840, ensemble_recall_at_840 = calculate_precision_recall_at_k(y_test, weighted_ensemble, k=840)
    
    logger.info(f"=== 激进PR-AUC优化结果 ===")
    logger.info(f"加权集成AUC: {ensemble_auc:.4f}")
    logger.info(f"加权集成PR-AUC: {ensemble_pr_auc:.4f}")
    logger.info(f"加权集成P@840: {ensemble_precision_at_840:.4f}")
    logger.info(f"加权集成R@840: {ensemble_recall_at_840:.4f}")
    
    # 与基线对比
    baseline_auc = 0.8219
    baseline_pr_auc = 0.0467
    baseline_recall = 0.2885
    
    auc_vs_baseline = ensemble_auc - baseline_auc
    pr_auc_vs_baseline = ensemble_pr_auc - baseline_pr_auc
    recall_vs_baseline = ensemble_recall_at_840 - baseline_recall
    
    logger.info(f"相比基线: AUC {auc_vs_baseline:+.4f}, PR-AUC {pr_auc_vs_baseline:+.4f}, Recall@840 {recall_vs_baseline:+.4f}")
    
    # 打印权重信息
    logger.info("=== 模型权重信息 ===")
    for i, (method, weight, pr_auc) in enumerate(zip(sampling_methods, weights, pr_aucs)):
        logger.info(f"{method}: 权重={weight:.3f}, PR-AUC={pr_auc:.4f}")
    
    return {
        'optimization': 'aggressive_prauc_optimization',
        'ensemble_auc': float(ensemble_auc),
        'ensemble_pr_auc': float(ensemble_pr_auc),
        'precision_at_840': float(ensemble_precision_at_840),
        'recall_at_840': float(ensemble_recall_at_840),
        'feature_count': len(feature_names),
        'model_count': len(models),
        'sampling_methods': sampling_methods,
        'model_weights': weights.tolist(),
        'individual_pr_aucs': pr_aucs.tolist(),
        'baseline_auc': baseline_auc,
        'baseline_pr_auc': baseline_pr_auc,
        'baseline_recall': baseline_recall,
        'auc_vs_baseline': float(auc_vs_baseline),
        'pr_auc_vs_baseline': float(pr_auc_vs_baseline),
        'recall_vs_baseline': float(recall_vs_baseline),
        'effective': bool(pr_auc_vs_baseline > 0.01 or recall_vs_baseline > 0.05)
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_aggressive_prauc"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化22完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"加权集成AUC: {result['ensemble_auc']:.4f}")
    print(f"加权集成PR-AUC: {result['ensemble_pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: {result['feature_count']}")
    print(f"模型数: {result['model_count']}")
    print(f"相比基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"相比基线PR-AUC: {result['pr_auc_vs_baseline']:+.4f}")
    print(f"相比基线Recall@840: {result['recall_vs_baseline']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
