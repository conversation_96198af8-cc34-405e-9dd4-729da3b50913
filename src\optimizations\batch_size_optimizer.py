#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批次大小优化器
通过调整批次大小来优化模型性能
"""
import os
import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BatchSizeOptimizer:
    """批次大小优化器"""
    
    def __init__(self):
        self.name = "batch_size"
        self.description = "批次大小优化"
    
    def optimize(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行批次大小优化
        
        Args:
            config: 优化配置
            
        Returns:
            优化后的配置
        """
        logger.info("=== 执行批次大小优化 ===")
        
        # 默认配置
        default_config = {
            'batch_size': 256,  # 减小批次大小
            'epochs': 15,
            'learning_rate': 0.002
        }
        
        # 合并配置
        opt_config = {**default_config, **config}
        
        logger.info(f"优化配置: {opt_config}")
        
        return opt_config
    
    def create_training_args(self, config: Dict[str, Any], run_name: str, output_dir: str) -> list:
        """创建训练参数"""
        args = [
            'python', 'src/train.py',
            '--run_name', run_name,
            '--epochs', str(config.get('epochs', 15)),
            '--data_dir', 'data',
            '--output_dir', output_dir
        ]
        
        # 如果有批次大小配置，创建临时配置文件
        if 'batch_size' in config:
            self._create_batch_config(config, output_dir)
            args.extend(['--config_override', f'{output_dir}/batch_config.json'])
        
        return args
    
    def _create_batch_config(self, config: Dict[str, Any], output_dir: str):
        """创建批次大小配置文件"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 读取基础配置
            base_config_path = "src/configs/models/sample_20250311_v7-20250311.json"
            with open(base_config_path, 'r', encoding='utf-8') as f:
                base_config = json.load(f)
            
            # 修改批次大小
            if 'training' not in base_config:
                base_config['training'] = {}
            
            base_config['training']['batch_size'] = config['batch_size']
            
            if 'learning_rate' in config:
                if 'optimizer' not in base_config:
                    base_config['optimizer'] = {}
                base_config['optimizer']['learning_rate'] = config['learning_rate']
            
            # 保存配置
            config_path = f"{output_dir}/batch_config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(base_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"批次大小配置已保存: {config_path}")
            
        except Exception as e:
            logger.error(f"创建批次大小配置失败: {e}")
