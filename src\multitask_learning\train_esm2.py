import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import roc_auc_score, accuracy_score, log_loss
import tensorflow as tf
import logging
import argparse

# Assuming DNN class is in esm2_model or will be moved to utils
from esm2_model import ESM2, compile_esm2_model, DNN 

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set random seeds
np.random.seed(42)
tf.random.set_seed(42)

def load_and_process_adult_data_for_esm2(data_path, test_size=0.2):
    """
    Load and preprocess UCI Adult dataset specifically for ESM2 simulation.
    Simulates: Impression -> Click -> Action1 -> Conversion
    - Click: income_50k > 50K
    - Action1: marital_status == Married-civ-spouse
    - Conversion: capital_gain > 0 (only if Action1 happened)
    """
    logger.info("Loading and preprocessing Adult data for ESM2...")
    column_names = [
        'age', 'workclass', 'fnlwgt', 'education', 'education_num', 
        'marital_status', 'occupation', 'relationship', 'race', 'gender',
        'capital_gain', 'capital_loss', 'hours_per_week', 'native_country', 'income_50k'
    ]
    df = pd.read_csv(data_path, names=column_names, sep=',\s+', engine='python', na_values='?')
    df = df.dropna()

    # --- Define Task Indicators ---
    y_click_indicator = (df['income_50k'].str.strip() == '>50K').astype(int)
    y_action1_indicator = (df['marital_status'].str.strip() == 'Married-civ-spouse').astype(int)
    y_conversion_indicator = (df['capital_gain'] > 0).astype(int)

    # --- Define ESM2 Labels (Joint Probabilities) ---
    label_ctr = y_click_indicator  # P(Click | Impression)
    label_ctavr1 = y_click_indicator * y_action1_indicator # P(Click, Action1 | Impression)
    label_ctcvr = y_click_indicator * y_action1_indicator * y_conversion_indicator # P(Click, Action1, Conversion | Impression)

    logger.info(f"Task Distribution (Indicators):")
    logger.info(f"  Click (>50K): {y_click_indicator.mean():.4f}")
    logger.info(f"  Action1 (Married): {y_action1_indicator.mean():.4f}")
    logger.info(f"  Conversion (Cap Gain > 0): {y_conversion_indicator.mean():.4f}")
    logger.info(f"ESM2 Label Distribution:")
    logger.info(f"  label_ctr (pC): {label_ctr.mean():.4f}")
    logger.info(f"  label_ctavr1 (pCA1): {label_ctavr1.mean():.4f}")
    logger.info(f"  label_ctcvr (pCA1C): {label_ctcvr.mean():.4f}")

    # --- Feature Processing (Same as other scripts) ---
    categorical_cols = ['workclass', 'education', 'marital_status', 'occupation', 
                         'relationship', 'race', 'gender', 'native_country']
    numerical_cols = ['age', 'fnlwgt', 'education_num', 'capital_gain', 'capital_loss', 'hours_per_week']
    
    scaler = StandardScaler()
    df[numerical_cols] = scaler.fit_transform(df[numerical_cols])
    
    encoders = {}
    for col in categorical_cols:
        encoders[col] = LabelEncoder()
        df[col] = encoders[col].fit_transform(df[col])
        
    feature_cols = numerical_cols + categorical_cols
    X = df[feature_cols].values
    
    # Split data
    X_train, X_test, \
    y_train_ctr, y_test_ctr, \
    y_train_ctavr1, y_test_ctavr1, \
    y_train_ctcvr, y_test_ctcvr = train_test_split(
        X, label_ctr, label_ctavr1, label_ctcvr, test_size=test_size, random_state=42
    )
    
    feature_config = {
        'numerical_cols': numerical_cols,
        'categorical_cols': categorical_cols,
        'categorical_dims': {col: len(encoders[col].classes_) for col in categorical_cols},
        'feature_cols': feature_cols
    }
    
    train_labels = {'ctr_output': y_train_ctr, 'ctavr1_output': y_train_ctavr1, 'ctcvr_output': y_train_ctcvr}
    test_labels = {'ctr_output': y_test_ctr, 'ctavr1_output': y_test_ctavr1, 'ctcvr_output': y_test_ctcvr}

    return (X_train, train_labels), (X_test, test_labels), feature_config

def prepare_input_data(X, feature_config):
    """Prepare model input data (same as other scripts)"""
    inputs = {}
    num_numerical = len(feature_config['numerical_cols'])
    for i, col in enumerate(feature_config['numerical_cols']):
        inputs[f'numerical_{col}'] = X[:, i:i+1]
    for i, col in enumerate(feature_config['categorical_cols']):
        inputs[f'categorical_{col}'] = X[:, num_numerical+i:num_numerical+i+1]
    return inputs

def plot_training_history(history, task_names, output_path="esm2_history.png"):
    """Plot training history for ESM2."""
    num_tasks = len(task_names)
    fig, axs = plt.subplots(num_tasks, 2, figsize=(15, 5 * num_tasks))
    fig.suptitle('ESM2 Training History', fontsize=16)

    for i, task_name in enumerate(task_names):
        # Plot Loss
        loss_key = task_name + '_loss' # Keras auto-names loss based on output name
        val_loss_key = 'val_' + loss_key
        ax_loss = axs[i, 0] if num_tasks > 1 else axs[0]
        ax_loss.plot(history.history[loss_key])
        ax_loss.plot(history.history[val_loss_key])
        ax_loss.set_title(f'{task_name} Loss')
        ax_loss.set_ylabel('Loss')
        ax_loss.set_xlabel('Epoch')
        ax_loss.legend(['Train', 'Validation'], loc='upper right')

        # Plot AUC (Keras names metrics like output_name_metric_index, e.g., ctr_output_auc_1)
        # Find the correct AUC key
        auc_key = ""
        val_auc_key = ""
        for key in history.history.keys():
             if key.startswith(task_name + '_auc'):
                 auc_key = key
                 val_auc_key = 'val_' + key
                 break
        
        ax_auc = axs[i, 1] if num_tasks > 1 else axs[1]
        if auc_key and val_auc_key:
            ax_auc.plot(history.history[auc_key])
            ax_auc.plot(history.history[val_auc_key])
            ax_auc.set_title(f'{task_name} AUC')
            ax_auc.set_ylabel('AUC')
            ax_auc.set_xlabel('Epoch')
            ax_auc.legend(['Train', 'Validation'], loc='lower right')
        else:
             ax_auc.set_title(f'{task_name} AUC (Not Found)')
        
    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to prevent title overlap
    plt.savefig(output_path)
    logger.info(f"Training history plot saved to {output_path}")
    plt.close()

def save_esm2_model(model, model_dir="../../models/esm2"):
    """Save the ESM2 model and its configuration."""
    logger.info(f"Saving ESM2 model to {model_dir}...")
    os.makedirs(model_dir, exist_ok=True)
    model.save(os.path.join(model_dir, "esm2_model.keras"))
    logger.info(f"Model saved successfully to {model_dir}/esm2_model.keras")
    
def load_esm2_model(model_dir="../../models/esm2"):
    """Load a saved ESM2 model."""
    logger.info(f"Loading ESM2 model from {model_dir}...")
    model_path = os.path.join(model_dir, "esm2_model.keras")
    if not os.path.exists(model_path):
        logger.error(f"Model not found at {model_path}")
        return None
    
    model = tf.keras.models.load_model(model_path)
    logger.info(f"Model loaded successfully from {model_path}")
    return model

def visualize_feature_importance(model, feature_config, task_index=0, top_n=10, output_path="esm2_feature_importance.png"):
    """
    Visualize feature importance for the ESM2 model.
    Uses a simple approach by examining the embedding weights.
    
    Args:
        model: Trained ESM2 model
        feature_config: Feature configuration dictionary
        task_index: Which task to analyze (0 for CTR, 1 for CTAVR1, 2 for CTCVR)
        top_n: Number of top features to display
        output_path: Path to save the visualization
    """
    logger.info(f"Analyzing feature importance for task {task_index}...")
    
    # Get all weights from embedding layers
    embedding_weights = {}
    feature_importance = {}
    
    for layer in model.layers:
        if 'embedding' in layer.name:
            feature_name = layer.name.split('_', 1)[1]  # Remove 'embedding_' prefix
            weights = layer.get_weights()[0]
            # Use L2 norm of embedding vectors as importance
            importance = np.linalg.norm(weights, axis=1).mean()
            embedding_weights[feature_name] = weights
            feature_importance[feature_name] = importance
    
    # For numerical features, we don't have direct access to importance
    # So we'll just add them with a placeholder importance
    for col in feature_config['numerical_cols']:
        feature_importance[col] = 0  # Placeholder, will need gradient analysis for proper importance
    
    # Sort features by importance
    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
    
    # Plot top N features
    plt.figure(figsize=(10, 6))
    features = [f[0] for f in sorted_features[:top_n]]
    importances = [f[1] for f in sorted_features[:top_n]]
    
    plt.barh(features, importances)
    plt.xlabel('Importance (Embedding L2 Norm)')
    plt.ylabel('Feature')
    plt.title(f'Top {top_n} Feature Importance for ESM2 Model')
    plt.tight_layout()
    plt.savefig(output_path)
    logger.info(f"Feature importance visualization saved to {output_path}")
    plt.close()
    
    return feature_importance

def evaluate_esm2_model(model, X, y_true_dict, feature_config, phase="测试集"):
    """Evaluate ESM2 model."""
    logger.info(f"Evaluating ESM2 model on {phase}...")
    inputs = prepare_input_data(X, feature_config)
    # model.predict returns a dict when outputs are named
    y_pred_dict = model.predict(inputs)
    
    task_names = list(y_true_dict.keys()) # Or get from model.output_names
    metrics = {}

    print(f"\n{phase}评估结果:")
    # Iterate through task names and access predictions by name
    for task_name in task_names:
        if task_name not in y_pred_dict:
            logger.warning(f"Prediction for task '{task_name}' not found in model output dictionary. Skipping.")
            continue
            
        y_true = y_true_dict[task_name]
        y_pred = y_pred_dict[task_name].flatten()
        
        try:
            auc = roc_auc_score(y_true, y_pred)
        except ValueError:
            logger.warning(f"Could not calculate AUC for task '{task_name}' (likely only one class present in labels). Setting AUC to NaN.")
            auc = float('nan')
            
        acc = accuracy_score(y_true, (y_pred > 0.5).astype(int))
        
        try:
             # Remove eps for compatibility with older scikit-learn versions
             logloss = log_loss(y_true, y_pred)
        except ValueError:
            logger.warning(f"Could not calculate LogLoss for task '{task_name}'. Setting LogLoss to NaN.")

        metrics[f'{task_name}_auc'] = auc
        metrics[f'{task_name}_acc'] = acc
        metrics[f'{task_name}_log_loss'] = logloss
        
        print(f"  Task '{task_name}': AUC={auc:.4f}, Accuracy={acc:.4f}, LogLoss={logloss:.4f}")
        
    return metrics

def main():
    """Main function for training ESM2."""
    # Settings
    data_path = "../../data/adult/adult.data" 
    epochs = 10  # Increased for better performance
    batch_size = 1024
    task_names = ('ctr_output', 'ctavr1_output', 'ctcvr_output') # Must match order in data loading and model
    model_dir = "../../models/esm2"
    
    # Add argument parsing
    parser = argparse.ArgumentParser(description='ESM2 Multi-task Learning Model')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'eval', 'analyze'],
                        help='Mode: train (train model), eval (evaluate model), analyze (analyze feature importance)')
    parser.add_argument('--epochs', type=int, default=epochs, help='Number of epochs for training')
    parser.add_argument('--batch_size', type=int, default=batch_size, help='Batch size for training')
    args = parser.parse_args()
                      
    logger.info("========== ESM2 多任务学习模型训练和评估 ==========")
    
    # Set GPU memory growth if available
    physical_devices = tf.config.list_physical_devices('GPU')
    if physical_devices:
        try:
            tf.config.experimental.set_memory_growth(physical_devices[0], True)
            logger.info("Enabled GPU memory growth.")
        except:
            logger.warning("Failed to set memory growth on GPU.")
            
    # 1. Load Data
    logger.info("1. 加载和预处理数据...")
    try:
        (X_train, y_train_dict), (X_test, y_test_dict), feature_config = load_and_process_adult_data_for_esm2(data_path)
        logger.info(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")
    except FileNotFoundError:
        logger.error(f"数据文件未找到: {data_path}")
        logger.error("请确保 Adult 数据集已下载并放置在 'data/adult/' 目录下.")
        return
    
    # Check if we should load a pre-trained model
    if args.mode in ['eval', 'analyze'] and os.path.exists(os.path.join(model_dir, "esm2_model.keras")):
        logger.info("加载预训练模型...")
        model = load_esm2_model(model_dir)
        if model is None:
            logger.error("模型加载失败，退出.")
            return
    else:
        # 2. Build Model
        logger.info("2. 构建 ESM2 模型...")
        model = ESM2(
            feature_config,
            embedding_dim=16,  # Increased for better representation
            dnn_hidden_units=(128, 64),  # Increased for better learning
            dnn_dropout=0.1,
            dnn_use_bn=True,
            l2_reg_embedding=1e-6,
            l2_reg_dnn=1e-6,
            task_names=task_names 
        )
        model.summary()

        # 3. Compile Model
        logger.info("3. 编译 ESM2 模型...")
        # Start with equal weights
        loss_weights = {name: 1.0 for name in task_names} 
        model = compile_esm2_model(model, task_names=task_names, learning_rate=0.002, loss_weights=loss_weights)

    # If training mode or no pre-trained model exists
    if args.mode == 'train' or not os.path.exists(os.path.join(model_dir, "esm2_model.keras")):
        # 4. Train Model
        logger.info("4. 训练 ESM2 模型...")
        train_inputs = prepare_input_data(X_train, feature_config)
        
        early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True)
        reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=3, min_lr=0.0001)
        
        history = model.fit(
            train_inputs,
            y_train_dict,
            validation_split=0.2,
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=[early_stopping, reduce_lr],
            verbose=1
        )

        # 5. Evaluate Model
        logger.info("5. 评估 ESM2 模型...")
        test_metrics = evaluate_esm2_model(model, X_test, y_test_dict, feature_config, phase="测试集")
        
        # 6. Plot History
        logger.info("6. 绘制训练历史...")
        plot_training_history(history, task_names)

        # 7. Save Model
        logger.info("7. 保存 ESM2 模型...")
        save_esm2_model(model)
    else:
        # Evaluation only
        logger.info("5. 评估 ESM2 模型...")
        test_metrics = evaluate_esm2_model(model, X_test, y_test_dict, feature_config, phase="测试集")
    
    # 8. Visualize Feature Importance (in any mode)
    logger.info("8. 可视化特征重要性...")
    feature_importance = visualize_feature_importance(model, feature_config)
    
    # Print top 10 important features
    logger.info("Top 10 Important Features:")
    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
    for feature, importance in sorted_features:
        logger.info(f"  {feature}: {importance:.6f}")

    logger.info("========== ESM2 训练和评估完成 ==========")

if __name__ == "__main__":
    # Ensure data directory exists
    os.makedirs("../../data/adult", exist_ok=True) 
    # Simple check if data exists, basic prompt if not
    if not os.path.exists("../../data/adult/adult.data"):
        print("错误: adult.data 文件未在 ../../data/adult/ 目录下找到。")
        print("请从 https://archive.ics.uci.edu/ml/machine-learning-databases/adult/adult.data 下载")
        print("并将其放置在 ../../data/adult/ 目录中，然后重新运行。")
    else:
        main() 