#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
蔚来转化率预测 - 最终优化版本
基于成功验证的架构，保持简洁高效
"""
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='蔚来转化率预测模型训练')
    parser.add_argument('--run_name', type=str, default=f'nio_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    parser.add_argument('--epochs', type=int, default=15, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=1024, help='批次大小')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心值')
    parser.add_argument('--feature_count', type=int, default=100, help='使用的特征数量')
    parser.add_argument('--multitask', action='store_true', help='使用多任务学习')
    return parser.parse_args()

def load_data():
    """加载数据"""
    logger.info("加载数据...")
    df = pd.read_parquet('data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet')
    logger.info(f"数据加载成功: {df.shape}")
    return df

def prepare_features(df, feature_count=100):
    """准备特征和标签"""
    logger.info("准备特征...")
    
    # 选择数值特征
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime']]
    
    # 限制特征数量
    selected_features = numeric_cols[:feature_count]
    logger.info(f"选择特征数: {len(selected_features)}")
    
    # 准备特征矩阵
    X = df[selected_features].fillna(0).values
    
    # 准备标签
    target_col = df['purchase_days_nio_new_car_total']
    target_numeric = pd.to_numeric(target_col, errors='coerce').fillna(0)
    y = (target_numeric > 0).astype(int).values
    
    logger.info(f"特征形状: {X.shape}, 正样本比例: {y.mean():.4f}")
    return X, y

def create_model(input_dim, multitask=False):
    """创建模型"""
    logger.info(f"创建{'多任务' if multitask else '单任务'}模型")
    
    if multitask:
        # 多任务模型
        inputs = tf.keras.layers.Input(shape=(input_dim,))
        shared = tf.keras.layers.Dense(128, activation='relu')(inputs)
        shared = tf.keras.layers.Dropout(0.3)(shared)
        shared = tf.keras.layers.Dense(64, activation='relu')(shared)
        shared = tf.keras.layers.Dropout(0.3)(shared)
        
        # 三个时间窗口的预测任务
        month1 = tf.keras.layers.Dense(32, activation='relu')(shared)
        month1_out = tf.keras.layers.Dense(1, activation='sigmoid', name='month_1')(month1)
        
        month3 = tf.keras.layers.Dense(32, activation='relu')(shared)
        month3_out = tf.keras.layers.Dense(1, activation='sigmoid', name='month_3')(month3)
        
        month6 = tf.keras.layers.Dense(32, activation='relu')(shared)
        month6_out = tf.keras.layers.Dense(1, activation='sigmoid', name='month_6')(month6)
        
        model = tf.keras.Model(inputs=inputs, outputs=[month1_out, month3_out, month6_out])
        model.compile(
            optimizer='adam',
            loss={'month_1': 'binary_crossentropy', 'month_3': 'binary_crossentropy', 'month_6': 'binary_crossentropy'},
            loss_weights={'month_1': 1.0, 'month_3': 0.5, 'month_6': 0.3},
            metrics={'month_1': ['auc'], 'month_3': ['auc'], 'month_6': ['auc']}
        )
    else:
        # 单任务模型（已验证有效）
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
    
    return model

def train_model(model, X_train, y_train, X_test, y_test, args):
    """训练模型"""
    logger.info("开始训练...")
    
    # 计算类别权重
    pos_weight = len(y_train) / (2 * np.sum(y_train)) if np.sum(y_train) > 0 else 1.0
    logger.info(f"正样本权重: {pos_weight:.2f}")
    
    # 回调函数
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=args.patience, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5),
        tf.keras.callbacks.ModelCheckpoint(f'{args.run_name}_best.keras', save_best_only=True)
    ]
    
    # 训练
    if args.multitask:
        y_train_dict = {'month_1': y_train, 'month_3': y_train, 'month_6': y_train}
        y_test_dict = {'month_1': y_test, 'month_3': y_test, 'month_6': y_test}
        history = model.fit(
            X_train, y_train_dict,
            validation_data=(X_test, y_test_dict),
            epochs=args.epochs, batch_size=args.batch_size,
            callbacks=callbacks, verbose=1
        )
    else:
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=args.epochs, batch_size=args.batch_size,
            callbacks=callbacks,
            class_weight={0: 1.0, 1: pos_weight},
            verbose=1
        )
    
    return history

def evaluate_model(model, X_test, y_test, args):
    """评估模型"""
    logger.info("评估模型...")
    
    if args.multitask:
        predictions = model.predict(X_test, verbose=0)
        y_pred_proba = predictions[0]  # 使用month_1的预测
    else:
        y_pred_proba = model.predict(X_test, verbose=0)
    
    y_pred = (y_pred_proba > 0.5).astype(int)
    
    # 计算指标
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    logger.info(f"测试准确率: {accuracy:.4f}")
    logger.info(f"测试AUC: {auc:.4f}")
    
    # 详细报告
    print("\n=== 模型评估结果 ===")
    print(f"AUC: {auc:.4f}")
    print(f"准确率: {accuracy:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    return {'accuracy': accuracy, 'auc': auc}

def main():
    """主函数"""
    args = parse_args()
    logger.info(f"开始训练: {args.run_name}")
    
    # 1. 加载数据
    df = load_data()
    
    # 2. 准备特征
    X, y = prepare_features(df, args.feature_count)
    
    # 3. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 4. 创建模型
    model = create_model(X_train.shape[1], args.multitask)
    model.summary()
    
    # 5. 训练模型
    history = train_model(model, X_train, y_train, X_test, y_test, args)
    
    # 6. 评估模型
    results = evaluate_model(model, X_test, y_test, args)
    
    # 7. 保存结果
    results.update({
        'run_name': args.run_name,
        'feature_count': args.feature_count,
        'train_samples': len(X_train),
        'test_samples': len(X_test),
        'positive_rate': float(y.mean()),
        'multitask': args.multitask,
        'epochs_trained': len(history.history['loss'])
    })
    
    result_file = f'{args.run_name}_results.json'
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"训练完成！结果保存到: {result_file}")
    logger.info(f"最佳模型保存到: {args.run_name}_best.keras")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
