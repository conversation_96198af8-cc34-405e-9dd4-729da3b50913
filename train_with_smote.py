#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
支持SMOTE增强的训练脚本
基于标准训练流程，添加SMOTE数据增强功能
"""
import os
import sys
import argparse
import logging
import subprocess
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.smote_enhancer import SMOTEEnhancer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练支持SMOTE增强的模型')
    
    # 基本参数
    parser.add_argument('--run_name', default=f"smote_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        help='运行名称')
    parser.add_argument('--epochs', type=int, default=15,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=8192,
                        help='批量大小')
    parser.add_argument('--patience', type=int, default=10,
                        help='早停耐心')
    
    # SMOTE参数
    parser.add_argument('--use_smote', action='store_true',
                        help='是否使用SMOTE增强')
    parser.add_argument('--sampling_strategy', type=float, default=0.05,
                        help='SMOTE采样策略')
    parser.add_argument('--k_neighbors', type=int, default=5,
                        help='SMOTE的k近邻数量')
    
    # 数据参数
    parser.add_argument('--data_dir', default='data',
                        help='数据目录')
    parser.add_argument('--dataset_code', default='dataset_nio_new_car_v15',
                        help='数据集代码')
    
    return parser.parse_args()

def create_smote_dataset(args):
    """创建SMOTE增强数据集"""
    logger.info("=== 创建SMOTE增强数据集 ===")
    
    enhancer = SMOTEEnhancer(
        sampling_strategy=args.sampling_strategy,
        k_neighbors=args.k_neighbors
    )
    
    # 原始数据路径
    train_path = f"{args.data_dir}/{args.dataset_code}/datetime=20240430"
    test_path = f"{args.data_dir}/{args.dataset_code}/datetime=20240531"
    
    # 增强数据输出目录
    enhanced_dataset_dir = f"{args.data_dir}/{args.dataset_code}_smote"
    
    # 执行增强
    enhanced_train, enhanced_test = enhancer.enhance_dataset(
        train_path, test_path, enhanced_dataset_dir
    )
    
    if enhanced_train and enhanced_test:
        logger.info("SMOTE数据集创建成功")
        return f"{args.dataset_code}_smote"
    else:
        logger.error("SMOTE数据集创建失败")
        return None

def run_training(args, dataset_code):
    """运行训练"""
    logger.info("=== 开始模型训练 ===")
    
    # 构建训练命令
    cmd = [
        "python", "src/train.py",
        "--run_name", args.run_name,
        "--epochs", str(args.epochs),
        "--batch_size", str(args.batch_size),
        "--patience", str(args.patience),
        "--data_dir", args.data_dir,
        "--dataset_code", dataset_code
    ]
    
    logger.info(f"执行训练命令: {' '.join(cmd)}")
    
    try:
        # 执行训练
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600,  # 1小时超时
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            logger.info("训练完成")
            return result.stdout, result.stderr
        else:
            logger.error(f"训练失败: {result.stderr}")
            return None, result.stderr
            
    except subprocess.TimeoutExpired:
        logger.error("训练超时")
        return None, "训练超时"
    except Exception as e:
        logger.error(f"训练异常: {e}")
        return None, str(e)

def parse_training_results(output):
    """解析训练结果"""
    if not output:
        return None
    
    results = {}
    lines = output.split('\n')
    
    for line in lines:
        # 解析Month_1指标
        if 'Month_1:' in line and 'ROC-AUC' in line:
            parts = line.split()
            for i, part in enumerate(parts):
                if part == 'ROC-AUC' and i + 2 < len(parts):
                    try:
                        results['month_1_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
                if part == 'PR-AUC' and i + 2 < len(parts):
                    try:
                        results['month_1_pr_auc'] = float(parts[i + 2])
                    except:
                        pass
        
        # 解析Overall指标
        if 'Overall:' in line and 'ROC-AUC' in line:
            parts = line.split()
            for i, part in enumerate(parts):
                if part == 'ROC-AUC' and i + 2 < len(parts):
                    try:
                        results['overall_auc'] = float(parts[i + 2].rstrip(','))
                    except:
                        pass
                if part == 'PR-AUC' and i + 2 < len(parts):
                    try:
                        results['overall_pr_auc'] = float(parts[i + 2])
                    except:
                        pass
    
    return results

def save_results(args, results, output, error):
    """保存结果"""
    import json
    
    # 创建结果目录
    results_dir = f"optimization_results/{args.run_name}"
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存结果
    full_results = {
        'run_name': args.run_name,
        'timestamp': datetime.now().isoformat(),
        'args': vars(args),
        'metrics': results,
        'training_output': output,
        'training_error': error
    }
    
    with open(f"{results_dir}/results.json", 'w', encoding='utf-8') as f:
        json.dump(full_results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"结果保存到: {results_dir}/results.json")

def main():
    """主函数"""
    args = parse_args()
    
    logger.info("=== 开始SMOTE增强训练 ===")
    logger.info(f"运行名称: {args.run_name}")
    logger.info(f"使用SMOTE: {args.use_smote}")
    
    # 1. 准备数据集
    if args.use_smote:
        dataset_code = create_smote_dataset(args)
        if not dataset_code:
            logger.error("SMOTE数据集创建失败，退出")
            return 1
    else:
        dataset_code = args.dataset_code
    
    # 2. 运行训练
    output, error = run_training(args, dataset_code)
    
    if output:
        # 3. 解析结果
        results = parse_training_results(output)
        
        if results:
            logger.info("=== 训练结果 ===")
            logger.info(f"Month_1 AUC: {results.get('month_1_auc', 0):.4f}")
            logger.info(f"Month_1 PR-AUC: {results.get('month_1_pr_auc', 0):.4f}")
            logger.info(f"Overall AUC: {results.get('overall_auc', 0):.4f}")
            logger.info(f"Overall PR-AUC: {results.get('overall_pr_auc', 0):.4f}")
            
            # 与基线对比
            baseline_month1_auc = 0.8906
            baseline_month1_pr_auc = 0.4808
            
            month1_auc_diff = results.get('month_1_auc', 0) - baseline_month1_auc
            month1_pr_auc_diff = results.get('month_1_pr_auc', 0) - baseline_month1_pr_auc
            
            logger.info("=== 与基线对比 ===")
            logger.info(f"Month_1 AUC差异: {month1_auc_diff:+.4f}")
            logger.info(f"Month_1 PR-AUC差异: {month1_pr_auc_diff:+.4f}")
            
            # 判断是否有效
            is_effective = month1_pr_auc_diff > 0.01 or (month1_pr_auc_diff > 0.005 and month1_auc_diff > -0.01)
            logger.info(f"优化效果: {'✅ 有效' if is_effective else '❌ 无效'}")
            
            # 4. 保存结果
            save_results(args, results, output, error)
            
            print(f"\n🎯 训练完成!")
            print(f"运行名称: {args.run_name}")
            print(f"使用SMOTE: {args.use_smote}")
            print(f"Month_1 AUC: {results.get('month_1_auc', 0):.4f} (vs基线: {month1_auc_diff:+.4f})")
            print(f"Month_1 PR-AUC: {results.get('month_1_pr_auc', 0):.4f} (vs基线: {month1_pr_auc_diff:+.4f})")
            print(f"优化效果: {'✅ 有效' if is_effective else '❌ 无效'}")
            
            return 0 if is_effective else 1
        else:
            logger.error("无法解析训练结果")
            save_results(args, {}, output, error)
            return 1
    else:
        logger.error("训练失败")
        save_results(args, {}, "", error)
        return 1

if __name__ == "__main__":
    sys.exit(main())
