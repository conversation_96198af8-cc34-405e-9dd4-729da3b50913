#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
蔚来转化率预测 - 最终训练脚本
使用模块化架构，整合所有有效优化技术
"""
import sys
import os
import json
import logging
import argparse
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.nio_loader import NioDataLoader
from models.core_models import ModelTrainer
from evaluation.evaluator import ModelEvaluator, ExperimentLogger

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='蔚来转化率预测 - 最终训练')
    parser.add_argument('--run_name', type=str, default=f'final_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    parser.add_argument('--epochs', type=int, default=12, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=512, help='批次大小')
    parser.add_argument('--patience', type=int, default=5, help='早停耐心值')
    parser.add_argument('--ensemble_size', type=int, default=3, help='集成模型数量')
    parser.add_argument('--feature_count', type=int, default=150, help='特征数量')
    parser.add_argument('--use_smote', action='store_true', default=True, help='使用SMOTE重采样')
    parser.add_argument('--smote_ratio', type=float, default=0.05, help='SMOTE采样比例')
    parser.add_argument('--test_size', type=float, default=0.2, help='测试集比例')
    return parser.parse_args()

def main():
    """主训练函数"""
    args = parse_args()
    logger.info(f"开始最终模型训练: {args.run_name}")
    
    # 1. 数据加载
    logger.info("=== 数据加载阶段 ===")
    data_loader = NioDataLoader()
    X, y, feature_names = data_loader.load_and_prepare(feature_count=args.feature_count)
    
    # 2. 数据分割
    X_train, X_test, y_train, y_test = data_loader.split_data(
        X, y, test_size=args.test_size, random_state=42
    )
    
    # 3. 模型训练
    logger.info("=== 模型训练阶段 ===")
    trainer = ModelTrainer(use_smote=args.use_smote, smote_ratio=args.smote_ratio)
    
    training_results = trainer.train_ensemble(
        X_train, y_train, X_test, y_test,
        epochs=args.epochs,
        batch_size=args.batch_size,
        patience=args.patience,
        ensemble_size=args.ensemble_size,
        verbose=1
    )
    
    # 4. 模型评估
    logger.info("=== 模型评估阶段 ===")
    evaluator = ModelEvaluator(save_dir="logs")
    
    evaluation_results = evaluator.evaluate_ensemble(
        y_test, 
        training_results['ensemble_prediction'],
        training_results['individual_predictions'],
        model_names=[f"model_{i+1}" for i in range(args.ensemble_size)]
    )
    
    # 5. 结果整合
    final_results = {
        'experiment_config': {
            'run_name': args.run_name,
            'feature_count': args.feature_count,
            'ensemble_size': args.ensemble_size,
            'use_smote': args.use_smote,
            'smote_ratio': args.smote_ratio,
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'patience': args.patience
        },
        'data_info': {
            'total_samples': len(X),
            'train_samples': len(X_train),
            'test_samples': len(X_test),
            'feature_count': len(feature_names),
            'positive_rate': float(y.mean())
        },
        'evaluation_results': evaluation_results,
        'optimization_techniques': [
            'deep_network_architecture',
            'ensemble_learning',
            'smote_resampling' if args.use_smote else 'class_weight_balancing',
            'batch_normalization',
            'early_stopping'
        ]
    }
    
    if args.use_smote:
        final_results['data_info']['smote_samples'] = len(training_results['augmented_data'][0])
    
    # 6. 保存结果
    logger.info("=== 结果保存阶段 ===")
    
    # 保存评估结果
    result_file = evaluator.save_evaluation_results(
        final_results, 
        args.run_name,
        additional_info={'model_type': 'final_optimized_ensemble'}
    )
    
    # 记录实验日志
    exp_logger = ExperimentLogger(logs_dir="logs")
    exp_logger.log_experiment(
        args.run_name,
        final_results['experiment_config'],
        final_results['evaluation_results']
    )
    
    # 7. 打印结果摘要
    evaluator.print_evaluation_summary(final_results['evaluation_results'])
    
    # 最终摘要
    ensemble_auc = final_results['evaluation_results']['ensemble_metrics']['auc']
    ensemble_acc = final_results['evaluation_results']['ensemble_metrics']['accuracy']
    
    print(f"\n🏆 最终模型训练完成!")
    print(f"🎯 集成AUC: {ensemble_auc:.4f}")
    print(f"📊 集成准确率: {ensemble_acc:.4f}")
    print(f"🔧 优化技术: {len(final_results['optimization_techniques'])}项")
    print(f"📁 结果文件: {result_file}")
    
    if args.use_smote:
        original_samples = final_results['data_info']['train_samples']
        smote_samples = final_results['data_info']['smote_samples']
        print(f"📈 SMOTE重采样: {original_samples} -> {smote_samples}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
