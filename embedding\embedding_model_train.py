import tensorflow as tf
from tensorflow import keras
import pandas as pd
import numpy as np
import json
from pathlib import Path
import logging
import glob
import pickle
import os
from datetime import datetime
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.model_selection import train_test_split
import argparse
from collections import defaultdict
from sklearn.metrics import roc_auc_score, average_precision_score, precision_score, recall_score # Added evaluation metrics

# Attempt to import data_exploration from common locations
try:
    from data_exploration import parse_comma_separated_seq, safe_parse_list # If data_exploration.py is in PYTHONPATH or same dir
except ModuleNotFoundError:
    try:
        from src.data_exploration import parse_comma_separated_seq, safe_parse_list # If in src/
    except ModuleNotFoundError:
        try:
            from utils.data_exploration import parse_comma_separated_seq, safe_parse_list # If in utils/
        except ModuleNotFoundError:
            try:
                # Assuming the script is in embedding/ and data_exploration is in ../src/ or similar relative path for running from root
                # This might require adding parent to sys.path if not run as part of a package
                # For simplicity, we rely on PYTHONPATH or direct placement for now, or user to fix if it's elsewhere
                logging.error("Module 'data_exploration.py' not found in common paths (root, src/, utils/). Ensure it is in PYTHONPATH or adjust import.")
                # As a temporary fallback to allow script to proceed, define dummy functions
                def parse_comma_separated_seq(s): return s.split(',') if isinstance(s, str) else []
                def safe_parse_list(s): 
                    if isinstance(s, list): return s
                    if isinstance(s, str): 
                        try: return json.loads(s)
                        except: return []
                    return []
                logging.warning("Using DUMMY parse_comma_separated_seq and safe_parse_list due to ModuleNotFoundError.")
            except Exception as e:
                 logging.error(f"Fallback for data_exploration also failed: {e}")
                 raise

# 导入模型构建函数
from embedding_model import build_embedding_model, build_enhanced_embedding_model
# from data_exploration import parse_comma_separated_seq, safe_parse_list # Original import commented out

# 自定义Focal Loss实现，替代tensorflow_addons依赖
def sigmoid_focal_crossentropy(y_true, y_pred, alpha=0.25, gamma=2.0):
    """自定义实现Focal Loss，避免依赖tensorflow_addons"""
    # 将标签转换为浮点型
    y_true = tf.cast(y_true, dtype=tf.float32)
    
    # 确保y_true和y_pred具有相同的形状 (rank)
    # Keras binary_crossentropy usually handles broadcasting e.g., (B,) vs (B, 1) - This was incorrect
    # Add dimension to y_true if necessary to match y_pred rank
    if len(y_true.shape) == 1 and len(y_pred.shape) == 2:
        y_true = tf.expand_dims(y_true, axis=-1)
        
    # 计算BCE损失
    bce = tf.keras.losses.binary_crossentropy(y_true, y_pred, from_logits=False)
    
    # 计算p_t
    p_t = (y_true * y_pred) + ((1 - y_true) * (1 - y_pred))
    
    # 计算alpha_t
    alpha_t = y_true * alpha + (1 - y_true) * (1 - alpha)
    
    # 应用focal项
    focal_loss = alpha_t * tf.pow(1 - p_t, gamma) * bce
    
    return focal_loss

# 添加focal loss类，便于模型编译
class SigmoidFocalCrossEntropy(tf.keras.losses.Loss):
    def __init__(self, alpha=0.25, gamma=2.0, **kwargs):
        super().__init__(**kwargs)
        self.alpha = alpha
        self.gamma = gamma
        
    def call(self, y_true, y_pred):
        return sigmoid_focal_crossentropy(y_true, y_pred, self.alpha, self.gamma)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'alpha': self.alpha,
            'gamma': self.gamma
        })
        return config

# 设置日志
def setup_logging():
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"training_{timestamp}.log"
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    logging.info(f"日志同时保存至文件: {log_file}")
    return log_file

# 配置
MODEL_CONFIG_PATH = Path("src/configs/models/sample_20250311_v7-20250311.json")
DATASET_BASE_PATH = Path("data/dataset_nio_new_car_v15")
OUTPUT_DIR = Path("embedding/training_output")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# 确保路径正确
cwd = os.getcwd()
if os.path.basename(cwd) == 'embedding':
    MODEL_CONFIG_PATH = Path("../src/configs/models/sample_20250311_v7-20250311.json")
    DATASET_BASE_PATH = Path("../data/dataset_nio_new_car_v15")
    OUTPUT_DIR = Path("training_output")
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# 数据参数
TRAIN_DATE = "20240430"  # 使用4月30日的数据进行训练
TEST_DATE = "20240531"   # 使用5月31日的数据进行测试/预测
VAL_SPLIT = 0.1
MAX_FILES_TO_LOAD = None  # 加载所有文件
LABEL_COL = "m_purchase_days_nio_new_car"
USER_ID_COL = "user_id"

# 序列特征配置
SEQUENCE_FEATURES = {
    "action_seq": "user_core_action_code_seq",
    "day_seq": "user_core_action_day_seq",
    "veh_model_seq": "veh_cgf_cfg_veh_model_seq",
    "reg_channel_seq": "reg_leads_reg_channel_seq",
    "td_type_seq": "test_drive_td_type_seq"
}

# 静态特征配置 - 根据数据字典选择关键特征
STATIC_FEATURES_NUM = [
    "user_core_action_cnt_30d", "user_core_action_cnt_60d", "user_core_action_cnt_90d",
    "fellow_follow_30d_cnt", "fellow_follow_60d_cnt", "fellow_follow_90d_cnt",
    "user_core_book_td_nio_30d_cnt", "user_core_book_td_nio_60d_cnt", "user_core_book_td_nio_90d_cnt",
    "user_core_visit_veh_cgf_nio_30d_cnt", "user_core_visit_veh_cgf_nio_60d_cnt",
    "user_core_save_veh_cgf_nio_30d_cnt", "user_core_nio_value"
]

STATIC_FEATURES_CAT = [
    "user_core_user_gender", "user_core_user_age_group", "user_core_resident_city",
    "intention_status", "intention_level", "intention_test_drive_level",
    "user_core_nio_community_identity", "user_core_pred_career_type"
]

# 模型超参数
MAXLEN = 64
EMBED_DIM = 256  # 增加嵌入维度
NUM_HEADS = 8    # 增加注意力头数
FF_DIM = 512     # 增加前馈网络维度
NUM_TRANSFORMER_BLOCKS = 3
MLP_UNITS = [256, 128, 64]
DROPOUT_RATE = 0.2
MLP_DROPOUT = 0.3
FOCAL_GAMMA = 2.0  # 调整Focal Loss参数
FOCAL_ALPHA = 0.75 # 调整Focal Loss参数
BATCH_SIZE = 128
EPOCHS = 20
LEARNING_RATE = 5e-5 # 降低学习率提高稳定性
NEG_POS_RATIO = 5    # 负样本:正样本比例
PATIENCE = 3 # 早停耐心值

# 特殊token
PAD_TOKEN = "[PAD]"
MASK_TOKEN = "[MASK]"
UNKNOWN_TOKEN = "[UNK]"

# 全局配置，会被args覆盖
USE_ENHANCED_MODEL = False
ADDITIONAL_SEQUENCES_CONFIG = {} # Populated from args later: { "key_from_cli": "actual_df_column_name" }
ALL_VOCABS = {} # Populated later: { "seq_key": (token_to_id, id_to_token) }
INCLUDE_STATIC_FEATURES = False
LOSS_TYPE = 'focal'
MIN_DF_VOCAB = 5 # Default min_df for vocab

# 定义可选的额外序列特征及其在DataFrame中的列名
ALL_AVAILABLE_ADDITIONAL_SEQUENCES = {
    "user_car_actions": "user_car_core_action_code_seq",
    "user_car_models": "user_car_core_action_veh_model_seq",
    "user_car_days": "user_car_core_action_day_seq",
    "veh_model_seq": "veh_cgf_cfg_veh_model_seq",      # Already in SEQUENCE_FEATURES
    "reg_channel_seq": "reg_leads_reg_channel_seq",  # Already in SEQUENCE_FEATURES
    "td_type_seq": "test_drive_td_type_seq"        # Already in SEQUENCE_FEATURES
}

def build_vocab(series, min_df=5, max_tokens=None):
    """从Series中构建词汇表"""
    # 与原代码相同，构建词汇表
    all_tokens = []
    for seq_str in series.dropna():
        all_tokens.extend(parse_comma_separated_seq(seq_str))

    if not all_tokens:
        logging.info("No tokens found for vocab building. Returning default special tokens.")
        return {PAD_TOKEN: 0, MASK_TOKEN: 1, UNKNOWN_TOKEN: 2}, {0: PAD_TOKEN, 1: MASK_TOKEN, 2: UNKNOWN_TOKEN}

    # 统计词频
    token_counts = {}
    for token in all_tokens:
        token_counts[token] = token_counts.get(token, 0) + 1
    
    # 过滤低频token
    filtered_tokens = [token for token, count in token_counts.items() if count >= min_df]
    
    # 限制词汇表大小
    if max_tokens and len(filtered_tokens) > max_tokens:
        filtered_tokens = sorted(filtered_tokens, key=lambda x: token_counts[x], reverse=True)[:max_tokens]
    
    # 创建映射
    token_to_id = {PAD_TOKEN: 0, MASK_TOKEN: 1, UNKNOWN_TOKEN: 2}
    for i, token in enumerate(filtered_tokens):
        token_to_id[token] = i + 3  # 预留0,1,2给特殊token
    
    id_to_token = {idx: token for token, idx in token_to_id.items()}
    logging.info(f"构建词汇表: {len(token_to_id)}个token (包含特殊token), min_df={min_df}")
    return token_to_id, id_to_token

def sequence_to_ids(seq_str, token_to_id, maxlen):
    """将序列字符串转换为ID序列"""
    if pd.isna(seq_str):
        return [token_to_id[PAD_TOKEN]] * maxlen
    
    tokens = parse_comma_separated_seq(seq_str)
    ids = [token_to_id.get(token, token_to_id[UNKNOWN_TOKEN]) for token in tokens]
    
    # 截断或填充
    ids = ids[:maxlen]
    padding_len = maxlen - len(ids)
    ids.extend([token_to_id[PAD_TOKEN]] * padding_len)
    return ids

def create_balanced_dataset(df, label_col, neg_pos_ratio=5):
    """创建平衡的数据集"""
    positive_samples = df[df[label_col] == 1]
    negative_samples = df[df[label_col] == 0]
    
    logging.info(f"原始数据：正样本={len(positive_samples)}, 负样本={len(negative_samples)}")
    
    # 如果正样本太少，可以通过过采样增加
    if len(positive_samples) < 1000:
        oversampling_size = min(1000, len(negative_samples)//neg_pos_ratio)
        if oversampling_size > len(positive_samples):
            oversampled_positives = positive_samples.sample(
                n=oversampling_size, 
                replace=True
            )
            positive_samples = pd.concat([positive_samples, oversampled_positives])
    
    # 负样本下采样
    n_neg_samples = min(len(negative_samples), len(positive_samples) * neg_pos_ratio)
    sampled_negatives = negative_samples.sample(n=n_neg_samples)
    
    # 合并正负样本
    balanced_df = pd.concat([positive_samples, sampled_negatives])
    balanced_df = balanced_df.sample(frac=1).reset_index(drop=True)  # 打乱顺序
    
    logging.info(f"平衡后数据：正样本={len(positive_samples)}, 负样本={len(sampled_negatives)}")
    return balanced_df

def preprocess_static_features(df, numerical_features, categorical_features, mode='train', preprocessors=None):
    """
    处理数值型和类别型静态特征。
    - 'train'模式: 拟合预处理器并转换数据，返回处理后的DataFrame、特征名列表和预处理器。
    - 'transform'模式: 使用提供的预处理器转换数据，返回处理后的DataFrame和特征名列表。
    """
    logging.info(f"预处理静态特征 (模式: {mode}, 数值: {len(numerical_features)}个, 类别: {len(categorical_features)}个)")
    
    df_processed = df.copy() # Work on a copy

    if preprocessors is None:
        preprocessors = {'num_means': {}, 'num_scalers': {}, 'cat_encoders': {}, 'cat_feature_names': {}}

    processed_feature_names = []
    numerical_cols_to_add = pd.DataFrame(index=df_processed.index) # For new numerical columns
    categorical_dfs_to_concat = [] # For OHE dataframes
    cols_to_drop_original = [] # Original categorical columns to drop after OHE

    # 处理数值型特征
    available_num_features = [f for f in numerical_features if f in df_processed.columns]
    if available_num_features:
        for feature in available_num_features:
            # 1. 添加缺失指示器
            missing_indicator_col = f"{feature}_is_missing"
            # df_processed[missing_indicator_col] = df_processed[feature].isnull().astype(int)
            numerical_cols_to_add[missing_indicator_col] = df_processed[feature].isnull().astype(int)
            processed_feature_names.append(missing_indicator_col)

            # Create a temporary series for processing the numerical feature
            temp_feature_series = df_processed[feature].copy()

            if mode == 'train':
                # 2. 计算并存储均值 (训练集)
                mean_val = temp_feature_series.mean()
                preprocessors['num_means'][feature] = mean_val
                # 3. 填充缺失值
                temp_feature_series = temp_feature_series.fillna(mean_val)
                # 4. 标准化 (训练集)
                scaler = StandardScaler()
                # df_processed[feature] = scaler.fit_transform(df_processed[feature].values.reshape(-1, 1)).flatten()
                numerical_cols_to_add[feature] = scaler.fit_transform(temp_feature_series.values.reshape(-1, 1)).flatten()
                preprocessors['num_scalers'][feature] = scaler
            elif mode == 'transform':
                # 2. 获取存储的均值
                mean_val = preprocessors['num_means'].get(feature, 0) # Default to 0 if somehow missing
                # 3. 填充缺失值
                temp_feature_series = temp_feature_series.fillna(mean_val)
                # 4. 标准化 (测试集/验证集)
                if feature in preprocessors['num_scalers']:
                    scaler = preprocessors['num_scalers'][feature]
                    # df_processed[feature] = scaler.transform(df_processed[feature].values.reshape(-1, 1)).flatten()
                    numerical_cols_to_add[feature] = scaler.transform(temp_feature_series.values.reshape(-1, 1)).flatten()
                else:
                    logging.warning(f"Scaler for numerical feature {feature} not found during transform. Skipping scaling.")
                    # Fallback: fill with 0 if it's still NaN (e.g. all values were NaN in train for this col)
                    # df_processed[feature] = df_processed[feature].fillna(0) 
                    numerical_cols_to_add[feature] = temp_feature_series.fillna(0)
            processed_feature_names.append(feature)

    # 处理类别型特征
    available_cat_features = [f for f in categorical_features if f in df_processed.columns]
    if available_cat_features:
        for feature in available_cat_features:
            # 1. 填充缺失值 (on a copy for the encoder)
            feature_series_for_ohe = df_processed[feature].fillna('Unknown').copy()
            cols_to_drop_original.append(feature) # Mark original categorical column for dropping
            
            if mode == 'train':
                # 2. 独热编码 (训练集)
                encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore') # sparse_output for TF2.8+
                encoded_data = encoder.fit_transform(feature_series_for_ohe.values.reshape(-1, 1))
                preprocessors['cat_encoders'][feature] = encoder
                # 获取特征名
                feature_names_onehot = [f"{feature}_{cat_val}" for cat_val in encoder.categories_[0]]
                preprocessors['cat_feature_names'][feature] = feature_names_onehot
            elif mode == 'transform':
                # 2. 独热编码 (测试集/验证集)
                if feature in preprocessors['cat_encoders']:
                    encoder = preprocessors['cat_encoders'][feature]
                    encoded_data = encoder.transform(feature_series_for_ohe.values.reshape(-1, 1))
                    feature_names_onehot = preprocessors['cat_feature_names'].get(feature, [])
                    if not feature_names_onehot: # Should not happen if trained correctly
                         logging.warning(f"Category names for OHE feature {feature} not found. Using defaults.")
                         feature_names_onehot = [f"{feature}_{i}" for i in range(encoded_data.shape[1])]
                else:
                    logging.warning(f"Encoder for categorical feature {feature} not found. Skipping OHE.")
                    encoded_data = np.zeros((len(df_processed), 0)) # Empty array
                    feature_names_onehot = []
            
            # 将独热编码结果添加到DataFrame list
            if encoded_data.shape[1] > 0:
                encoded_df = pd.DataFrame(encoded_data, columns=feature_names_onehot, index=df_processed.index)
                categorical_dfs_to_concat.append(encoded_df)
                processed_feature_names.extend(feature_names_onehot)
            # Drop original categorical column after encoding - will be done in bulk later
            # if feature in df_processed.columns and feature not in processed_feature_names:
            #      df_processed = df_processed.drop(columns=[feature])

    # Drop original numerical features that were processed and original categorical features that were OHE'd
    original_numerical_to_drop = [f for f in available_num_features if f in df_processed.columns and f in numerical_cols_to_add.columns]
    df_processed = df_processed.drop(columns=original_numerical_to_drop + list(set(cols_to_drop_original)), errors='ignore')

    # Concatenate all new features at once
    if not numerical_cols_to_add.empty:
        df_processed = pd.concat([df_processed] + [numerical_cols_to_add], axis=1)
    if categorical_dfs_to_concat:
        df_processed = pd.concat([df_processed] + categorical_dfs_to_concat, axis=1)

    if not available_num_features and not available_cat_features:
        logging.warning("没有可用的静态特征进行处理。")
    
    logging.info(f"处理后的静态特征总数: {len(processed_feature_names)}")
    
    return df_processed, processed_feature_names, preprocessors

def create_tf_dataset(df, 
                      all_sequence_feature_map, # {"action_seq": "col_name", "user_car_actions": "col_name_2"}
                      all_token_to_id_maps,     # {"action_seq": {...}, "user_car_actions": {...} }
                      processed_static_feature_names, # List of final static feature columns in df
                      target_label_col_name,    # e.g., 'target_purchase_next_30d'
                      maxlen, 
                      batch_size,
                      is_training=True, # For shuffling
                      use_enhanced_model=False, # To know which inputs to expect
                      include_static_features=False): # To know if static_features input is present
    """创建TensorFlow数据集"""
    
    inputs_dict = {} # This will hold all features for the model

    # 1. 处理序列特征
    for seq_key, feature_col_name in all_sequence_feature_map.items():
        if feature_col_name not in df.columns:
            logging.warning(f"Sequence column '{feature_col_name}' for key '{seq_key}' not found in DataFrame. Padding only.")
            ids = np.full((len(df), maxlen), ALL_VOCABS.get(seq_key, ({PAD_TOKEN:0},None))[0].get(PAD_TOKEN, 0), dtype=np.int32) # Pad with global PAD
        elif seq_key not in all_token_to_id_maps:
            logging.warning(f"Token map for sequence key '{seq_key}' not found. Padding only for '{feature_col_name}'.")
            ids = np.full((len(df), maxlen), ALL_VOCABS.get(seq_key, ({PAD_TOKEN:0},None))[0].get(PAD_TOKEN, 0), dtype=np.int32)
        else:
            current_token_to_id_map = all_token_to_id_maps[seq_key]
            ids = np.array([
                sequence_to_ids(row, current_token_to_id_map, maxlen) 
                for row in df[feature_col_name].values # Use .get for safety if column might be missing after all
            ], dtype=np.int32)

        # Determine model input name based on convention in build_embedding_model / build_enhanced_embedding_model
        if seq_key == "action_seq":
            inputs_dict["action_ids"] = ids
        elif seq_key == "day_seq":
            inputs_dict["day_ids"] = ids
        elif use_enhanced_model: # Only add other sequences if enhanced model is active
            inputs_dict[f"{seq_key}_ids"] = ids # For build_enhanced_embedding_model
        # If not enhanced model, other sequences are ignored by model, but processed here for consistency.

    # 2. 处理静态特征 (如果模型需要)
    if use_enhanced_model and include_static_features:
        if processed_static_feature_names and all(f in df.columns for f in processed_static_feature_names):
            static_array = np.array(df[processed_static_feature_names].values, dtype=np.float32)
            inputs_dict["static_features"] = static_array
        elif processed_static_feature_names: # Some features are missing in df
            logging.warning(f"Not all processed static features found in DataFrame. Will try to use available ones or pad. Expected: {len(processed_static_feature_names)}")
            # Create a zero array with expected shape and fill what's available - safer for model input shape consistency
            # This part needs careful handling of expected static_features_dim in the model
            # For now, if any are missing, we might error out or log and provide zeros.
            # Assuming static_features_dim is known and fixed for the model.
            # Let's assume preprocess_static_features ensures all columns are present in df or an error is raised earlier.
            # If not, a placeholder of zeros might be needed:
            # expected_dim = model.get_layer("static_features_input_layer_name").input_shape[-1] # Psuedocode
            # static_array = np.zeros((len(df), expected_dim), dtype=np.float32)
            # For now, assume processed_static_feature_names are all present if this branch is reached.
            static_array = np.array(df[processed_static_feature_names].values, dtype=np.float32)
            inputs_dict["static_features"] = static_array
        else: # No static features to process, or names list is empty
            logging.info("No static features to include in this dataset batch, though model expects them.")
            # This case should be handled by ensuring static_features_dim in model is 0 if no features.
            # If model expects them (static_features_dim > 0), this will cause error.
            # The calling code should ensure consistency.

    # 3. 构建标签
    if target_label_col_name not in df.columns:
        logging.error(f"Target label column '{target_label_col_name}' not found in DataFrame. Cannot create labels.")
        raise ValueError(f"Target label column '{target_label_col_name}' not found.")
    
    labels_array = np.array(df[target_label_col_name].values, dtype=np.float32)
    
    # 4. 创建TF数据集
    # Filter inputs_dict to only include keys expected by the specific model version being used
    final_model_inputs = {}
    if "action_ids" in inputs_dict: final_model_inputs["action_ids"] = inputs_dict["action_ids"]
    if "day_ids" in inputs_dict: final_model_inputs["day_ids"] = inputs_dict["day_ids"]

    if use_enhanced_model:
        for k, v in inputs_dict.items():
            if k not in ["action_ids", "day_ids", "static_features"]: # additional sequences
                 final_model_inputs[k] = v
        if include_static_features and "static_features" in inputs_dict:
            final_model_inputs["static_features"] = inputs_dict["static_features"]
        elif include_static_features and "static_features" not in inputs_dict:
            # This implies static_features_dim > 0 in model, but no static data found. Needs placeholder.
            # This logic should be in the model building part or data prep to ensure consistency.
            # For now, assuming if include_static_features is true, static_array was prepared.
            logging.warning("Enhanced model includes static features, but no static_features array in inputs_dict for tf.data.Dataset. This might be an issue.")


    dataset = tf.data.Dataset.from_tensor_slices((final_model_inputs, labels_array))
    
    if is_training:
        dataset = dataset.shuffle(buffer_size=len(df)).batch(batch_size).prefetch(tf.data.AUTOTUNE)
    else:
        dataset = dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)
        
    return dataset

def prepare_dataset(train_df, sequence_features, static_features_num, static_features_cat, label_col):
    """准备训练集，添加标签"""
    # 确保数据类型正确
    df = train_df.copy()
    
    # 检查并处理序列特征 - 确保存在
    missing_seq_features = []
    for _, feat_col in sequence_features.items():
        if feat_col not in df.columns:
            missing_seq_features.append(feat_col)
    
    if missing_seq_features:
        logging.warning(f"以下序列特征在数据集中不存在: {missing_seq_features}")
    
    # 检查标签列
    if label_col not in df.columns:
        logging.error(f"标签列 {label_col} 不在数据集中")
        return None
    
    # 解析 m_purchase_days_nio_new_car
    df[label_col + '_parsed'] = df[label_col].apply(safe_parse_list)
    valid_labels_mask = df[label_col + '_parsed'].notna()
    if not valid_labels_mask.all():
        logging.warning(f"有 {(~valid_labels_mask).sum()} 行无法解析标签，这些行将被删除")
        df = df[valid_labels_mask]
    
    # 创建二元购车标签（第一个月内购车 = 1）
    df['target_purchase_next_30d'] = df[label_col + '_parsed'].apply(
        lambda x: 1 if x and len(x) > 0 and x[0] == 1 else 0
    )
    
    # 检查静态特征
    missing_num_features = [f for f in static_features_num if f not in df.columns]
    missing_cat_features = [f for f in static_features_cat if f not in df.columns]
    
    if missing_num_features:
        logging.warning(f"以下数值型静态特征不存在: {missing_num_features}")
        static_features_num = [f for f in static_features_num if f not in missing_num_features]
    
    if missing_cat_features:
        logging.warning(f"以下类别型静态特征不存在: {missing_cat_features}")
        static_features_cat = [f for f in static_features_cat if f not in missing_cat_features]
    
    # 统计正负样本
    positive_count = df['target_purchase_next_30d'].sum()
    total_count = len(df)
    logging.info(f"正样本数量: {positive_count}, 正样本率: {positive_count/total_count:.4%}")
    
    return df, static_features_num, static_features_cat

# 设置命令行参数
def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="NIO用户嵌入模型训练脚本")
    
    parser.add_argument("--train-date", type=str, default=TRAIN_DATE,
                        help="训练数据日期，格式: YYYYMMDD")
    
    parser.add_argument("--test-date", type=str, default=TEST_DATE,
                        help="测试数据日期，格式: YYYYMMDD")
    
    parser.add_argument("--model-config", type=str,
                        help="模型配置文件路径")
    
    parser.add_argument("--output-dir", type=str,
                        help="模型输出目录")
    
    parser.add_argument("--max-files", type=int, default=MAX_FILES_TO_LOAD,
                        help="最大加载的文件数量")
    
    parser.add_argument("--batch-size", type=int, default=BATCH_SIZE,
                        help="训练批次大小")
    
    parser.add_argument("--epochs", type=int, default=EPOCHS,
                        help="训练轮数")
    
    parser.add_argument("--embed-dim", type=int, default=EMBED_DIM,
                        help="嵌入向量维度")
    
    parser.add_argument("--num-heads", type=int, default=NUM_HEADS,
                        help="注意力头数量")
    
    parser.add_argument("--num-transformer-blocks", type=int, default=NUM_TRANSFORMER_BLOCKS,
                        help="Transformer块数量")
    
    parser.add_argument("--mlp-units", type=int, nargs="+", default=MLP_UNITS,
                        help="MLP层的神经元数量列表，例如: 256 128 64")
    
    parser.add_argument("--learning-rate", type=float, default=LEARNING_RATE,
                        help="学习率")
    
    parser.add_argument("--dropout-rate", type=float, default=DROPOUT_RATE,
                        help="序列特征Dropout率")
    
    parser.add_argument("--mlp-dropout", type=float, default=MLP_DROPOUT,
                        help="MLP层Dropout率")
    
    parser.add_argument("--neg-pos-ratio", type=float, default=NEG_POS_RATIO,
                        help="负样本与正样本的比例")
    
    parser.add_argument("--focal-alpha", type=float, default=FOCAL_ALPHA,
                        help="Focal Loss的alpha参数")
    
    parser.add_argument("--focal-gamma", type=float, default=FOCAL_GAMMA,
                        help="Focal Loss的gamma参数")
    
    parser.add_argument("--maxlen", type=int, default=MAXLEN,
                        help="序列最大长度")
    
    parser.add_argument("--use-enhanced-model", action="store_true",
                        help="是否使用增强版embedding模型")
    
    parser.add_argument("--additional-sequences", type=str, nargs="+", 
                        default=[],
                        help="额外序列特征，例如: veh_model_seq reg_channel_seq")
    
    parser.add_argument("--include-static-features", action="store_true",
                        help="是否包含静态特征")
    
    parser.add_argument("--loss-type", type=str, default="focal",
                        choices=["focal", "bce", "weighted_bce"],
                        help="损失函数类型: focal, bce, weighted_bce")
    
    parser.add_argument("--patience", type=int, default=3,
                        help="早停的耐心值")
    
    parser.add_argument("--min-df-vocab", type=int, default=MIN_DF_VOCAB,
                        help="构建词汇表时的最小词频")

    parser.add_argument("--mode", type=str, default="train",
                        choices=["train", "predict", "extract_embeddings"],
                        help="Operation mode: train, predict, or extract_embeddings")

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    log_file_path = setup_logging() # Setup logging first

    # --- 0. Update Global Configs from Args ---
    TRAIN_DATE = args.train_date
    TEST_DATE = args.test_date
    MODEL_CONFIG_PATH = Path(args.model_config) if args.model_config else MODEL_CONFIG_PATH
    OUTPUT_DIR = Path(args.output_dir) if args.output_dir else OUTPUT_DIR
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    MAX_FILES_TO_LOAD = args.max_files
    BATCH_SIZE = args.batch_size
    EPOCHS = args.epochs
    EMBED_DIM = args.embed_dim
    NUM_HEADS = args.num_heads
    NUM_TRANSFORMER_BLOCKS = args.num_transformer_blocks
    MLP_UNITS = args.mlp_units
    LEARNING_RATE = args.learning_rate
    DROPOUT_RATE = args.dropout_rate
    MLP_DROPOUT = args.mlp_dropout
    NEG_POS_RATIO = args.neg_pos_ratio
    FOCAL_ALPHA = args.focal_alpha
    FOCAL_GAMMA = args.focal_gamma
    MAXLEN = args.maxlen
    PATIENCE = args.patience
    MIN_DF_VOCAB = args.min_df_vocab # New arg for vocab

    USE_ENHANCED_MODEL = args.use_enhanced_model
    INCLUDE_STATIC_FEATURES = args.include_static_features
    LOSS_TYPE = args.loss_type
    
    logging.info(f"Run arguments: {args}")
    logging.info(f"Output directory: {OUTPUT_DIR}")

    # --- 1. Define Sequence Features Configuration ---
    # Primary sequences always included if columns exist
    PRIMARY_SEQUENCE_FEATURES = {
        "action_seq": SEQUENCE_FEATURES["action_seq"], # From existing global
        "day_seq": SEQUENCE_FEATURES["day_seq"],       # From existing global
    }
    
    # Build ADDITIONAL_SEQUENCES_CONFIG from args.additional_sequences for enhanced model
    if USE_ENHANCED_MODEL and args.additional_sequences:
        for key in args.additional_sequences:
            if key in ALL_AVAILABLE_ADDITIONAL_SEQUENCES:
                ADDITIONAL_SEQUENCES_CONFIG[key] = ALL_AVAILABLE_ADDITIONAL_SEQUENCES[key]
            else:
                logging.warning(f"Unknown additional sequence key from CLI: {key}. Defined in ALL_AVAILABLE_ADDITIONAL_SEQUENCES? Skipping.")
    
    # Master map for all sequences the model might use
    MASTER_SEQUENCE_FEATURES_MAP = {**PRIMARY_SEQUENCE_FEATURES}
    if USE_ENHANCED_MODEL:
        MASTER_SEQUENCE_FEATURES_MAP.update(ADDITIONAL_SEQUENCES_CONFIG)
    
    logging.info(f"Master sequence feature map: {MASTER_SEQUENCE_FEATURES_MAP}")

    # --- 2. Load Raw Data ---
    logging.info("Loading raw data...")
    train_files = glob.glob(str(DATASET_BASE_PATH / f"datetime={TRAIN_DATE}/**/*.parquet"), recursive=True)
    if MAX_FILES_TO_LOAD:
        train_files = train_files[:MAX_FILES_TO_LOAD]
    
    if not train_files:
        logging.error(f"No training files found for date {TRAIN_DATE} at {DATASET_BASE_PATH / f'datetime={TRAIN_DATE}'}")
        exit(1)
    
    logging.info(f"Found {len(train_files)} training files for date {TRAIN_DATE}. Loading...")
    train_df_list = []
    for f_path in train_files:
        try:
            train_df_list.append(pd.read_parquet(f_path))
            logging.debug(f"Loaded {f_path}")
        except Exception as e:
            logging.error(f"Error loading training file {f_path}: {e}")
    if not train_df_list:
        logging.error("Failed to load any training dataframes.")
        exit(1)
    train_df_raw = pd.concat(train_df_list, ignore_index=True)
    logging.info(f"Loaded {len(train_df_raw)} total training samples for date {TRAIN_DATE}.")

    test_df_raw = None
    if args.mode == "extract_embeddings" or args.mode == "predict" or (args.mode == "train" and VAL_SPLIT == 0): # Load test data if in extraction/prediction mode, or if no validation split from train for testing
        test_files = glob.glob(str(DATASET_BASE_PATH / f"datetime={TEST_DATE}/**/*.parquet"), recursive=True)
        if MAX_FILES_TO_LOAD: # Apply max files to test set loading as well if specified
            test_files = test_files[:MAX_FILES_TO_LOAD]

        if test_files:
            logging.info(f"Found {len(test_files)} test files for date {TEST_DATE}. Loading...")
            test_df_list = []
            for f_path in test_files:
                try:
                    test_df_list.append(pd.read_parquet(f_path))
                    logging.debug(f"Loaded {f_path}")
                except Exception as e:
                    logging.error(f"Error loading test file {f_path}: {e}")
            if test_df_list:
                test_df_raw = pd.concat(test_df_list, ignore_index=True)
                logging.info(f"Loaded {len(test_df_raw)} total test samples for date {TEST_DATE}.")
            else:
                logging.warning(f"No test dataframes loaded for date {TEST_DATE}, though files were found.")
        else:
            logging.warning(f"No test files found for date {TEST_DATE} at {DATASET_BASE_PATH / f'datetime={TEST_DATE}'}")
    
    # --- 3. Build Vocabularies (from training data only) ---
    logging.info("Building vocabularies from training data...")
    for seq_key, col_name in MASTER_SEQUENCE_FEATURES_MAP.items():
        if col_name in train_df_raw.columns:
            token_to_id, id_to_token = build_vocab(train_df_raw[col_name], min_df=MIN_DF_VOCAB)
            ALL_VOCABS[seq_key] = (token_to_id, id_to_token)
            logging.info(f"Built vocab for '{seq_key}' (column: {col_name}), size: {len(token_to_id)}")
        else:
            logging.warning(f"Sequence column '{col_name}' for key '{seq_key}' not found in training data. Skipping vocab.")
            ALL_VOCABS[seq_key] = ({PAD_TOKEN: 0, MASK_TOKEN: 1, UNKNOWN_TOKEN: 2}, {0:PAD_TOKEN, 1:MASK_TOKEN, 2:UNKNOWN_TOKEN}) # Default empty vocab

    # --- 4. Prepare Datasets (Train, Validation) ---
    static_preprocessors_fitted = {}
    processed_static_feature_names = []

    # Split raw training data into train and validation sets
    if VAL_SPLIT > 0 and len(train_df_raw) > 10: # Ensure enough data for split
        train_df_for_processing, val_df_for_processing = train_test_split(
            train_df_raw, test_size=VAL_SPLIT, random_state=42, 
            # stratify=train_df_raw[LABEL_COL] if LABEL_COL in train_df_raw else None # Stratify can be tricky with list-like labels
        )
        logging.info(f"Split data: {len(train_df_for_processing)} for training, {len(val_df_for_processing)} for validation.")
    else:
        train_df_for_processing = train_df_raw.copy()
        val_df_for_processing = None
        logging.info(f"Using all {len(train_df_for_processing)} samples for training (no validation split).")

    # Process Training Data
    logging.info("Processing training data...")
    train_df_prepared, current_static_num, current_static_cat = prepare_dataset(
        train_df_for_processing, PRIMARY_SEQUENCE_FEATURES, STATIC_FEATURES_NUM, STATIC_FEATURES_CAT, LABEL_COL
    )
    if train_df_prepared is None: exit(1)

    if USE_ENHANCED_MODEL and INCLUDE_STATIC_FEATURES:
        train_df_final, processed_static_feature_names, static_preprocessors_fitted = preprocess_static_features(
            train_df_prepared, current_static_num, current_static_cat, mode='train', preprocessors=None
        )
        logging.info(f"Fit static preprocessors. Number of processed static features: {len(processed_static_feature_names)}")
    else:
        train_df_final = train_df_prepared # No static features to process or not using enhanced model

    # Balance training data (after all feature processing, before TF dataset)
    logging.info("Balancing training data...")
    train_df_balanced = create_balanced_dataset(train_df_final, 'target_purchase_next_30d', neg_pos_ratio=NEG_POS_RATIO)
    
    # Create Training TF Dataset
    token_to_id_maps_for_tf = {k: v[0] for k, v in ALL_VOCABS.items()}
    train_dataset = create_tf_dataset(
        train_df_balanced, MASTER_SEQUENCE_FEATURES_MAP, token_to_id_maps_for_tf,
        processed_static_feature_names, 'target_purchase_next_30d', MAXLEN, BATCH_SIZE,
        is_training=True, use_enhanced_model=USE_ENHANCED_MODEL, include_static_features=INCLUDE_STATIC_FEATURES
    )
    logging.info("Training TensorFlow dataset created.")

    # Process Validation Data (if any)
    val_dataset = None
    if val_df_for_processing is not None and len(val_df_for_processing) > 0:
        logging.info("Processing validation data...")
        val_df_prepared, current_static_num_val, current_static_cat_val = prepare_dataset(
            val_df_for_processing, PRIMARY_SEQUENCE_FEATURES, STATIC_FEATURES_NUM, STATIC_FEATURES_CAT, LABEL_COL
        )
        if val_df_prepared is None: exit(1)

        if USE_ENHANCED_MODEL and INCLUDE_STATIC_FEATURES:
            val_df_final, val_processed_static_names, _ = preprocess_static_features(
                val_df_prepared, current_static_num_val, current_static_cat_val, 
                mode='transform', preprocessors=static_preprocessors_fitted
            )
            # Sanity check: processed feature names should match
            if set(processed_static_feature_names) != set(val_processed_static_names):
                logging.warning("Mismatch in processed static feature names between train and validation!")
        else:
            val_df_final = val_df_prepared
        
        val_dataset = create_tf_dataset(
            val_df_final, MASTER_SEQUENCE_FEATURES_MAP, token_to_id_maps_for_tf,
            processed_static_feature_names, 'target_purchase_next_30d', MAXLEN, BATCH_SIZE, # Use train's processed_static_feature_names
            is_training=False, use_enhanced_model=USE_ENHANCED_MODEL, include_static_features=INCLUDE_STATIC_FEATURES
        )
        logging.info("Validation TensorFlow dataset created.")

    # --- 5. Build Model ---
    static_features_dim_for_model = len(processed_static_feature_names) if USE_ENHANCED_MODEL and INCLUDE_STATIC_FEATURES else 0
    
    action_vocab_s = len(ALL_VOCABS.get("action_seq", ({}, None))[0]) if "action_seq" in MASTER_SEQUENCE_FEATURES_MAP else 0
    day_vocab_s = len(ALL_VOCABS.get("day_seq", ({}, None))[0]) if "day_seq" in MASTER_SEQUENCE_FEATURES_MAP else 0

    if action_vocab_s == 0: logging.warning("Action vocab size is 0! Model might not train correctly.")
    if day_vocab_s == 0: logging.warning("Day vocab size is 0! Model might not train correctly.")


    model, extractor = None, None
    if USE_ENHANCED_MODEL:
        logging.info("Building ENHANCED embedding model.")
        additional_vocab_sizes_for_model = {}
        for seq_key, col_name in ADDITIONAL_SEQUENCES_CONFIG.items():
            if seq_key in ALL_VOCABS:
                additional_vocab_sizes_for_model[seq_key] = len(ALL_VOCABS[seq_key][0])
            else: # Should not happen if vocab building was complete
                 additional_vocab_sizes_for_model[seq_key] = 0 
                 logging.warning(f"Vocab for additional sequence '{seq_key}' not found for model building. Size set to 0.")
        
        model, extractor = build_enhanced_embedding_model(
            maxlen=MAXLEN,
            action_vocab_size=action_vocab_s,
            day_vocab_size=day_vocab_s,
            additional_vocab_sizes=additional_vocab_sizes_for_model,
            static_features_dim=static_features_dim_for_model,
            embed_dim=EMBED_DIM,
            num_heads=NUM_HEADS,
            ff_dim=FF_DIM,
            num_transformer_blocks=NUM_TRANSFORMER_BLOCKS,
            mlp_units=MLP_UNITS,
            dropout_rate=DROPOUT_RATE,
            mlp_dropout=MLP_DROPOUT
            # class_weight can be added here if needed
        )
    else:
        logging.info("Building STANDARD embedding model.")
        model, extractor = build_embedding_model(
            maxlen=MAXLEN,
            action_vocab_size=action_vocab_s,
            day_vocab_size=day_vocab_s, # Ensure this is passed
            embed_dim=EMBED_DIM,
            num_heads=NUM_HEADS,
            ff_dim=FF_DIM,
            num_transformer_blocks=NUM_TRANSFORMER_BLOCKS,
            mlp_units=MLP_UNITS,
            dropout_rate=DROPOUT_RATE,
            mlp_dropout=MLP_DROPOUT,
            include_mlm_head=False # Assuming no MLM for now unless explicitly configured
        )

    # Loss selection
    if LOSS_TYPE == 'focal':
        loss_fn = SigmoidFocalCrossEntropy(alpha=FOCAL_ALPHA, gamma=FOCAL_GAMMA)
    elif LOSS_TYPE == 'bce':
        loss_fn = tf.keras.losses.BinaryCrossentropy()
    # Add weighted BCE if needed
    else:
        logging.warning(f"Unknown loss type {LOSS_TYPE}. Defaulting to Focal Loss.")
        loss_fn = SigmoidFocalCrossEntropy(alpha=FOCAL_ALPHA, gamma=FOCAL_GAMMA)

    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE),
        loss=loss_fn,
        metrics=['accuracy', tf.keras.metrics.AUC(name='auc')] # Added AUC
    )
    model.summary(print_fn=logging.info)
    extractor.summary(print_fn=logging.info)

    # --- 6. Train Model ---
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            filepath=OUTPUT_DIR / "best_model.weights.h5", # Use .weights.h5 for save_weights_only
            save_best_only=True,
            monitor='val_loss' if val_dataset else 'loss', # Monitor training loss if no val data
            verbose=1,
            save_weights_only=True # Save only weights
        ),
        tf.keras.callbacks.TensorBoard(log_dir=OUTPUT_DIR / "logs"),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss' if val_dataset else 'loss', 
            patience=PATIENCE, 
            verbose=1, 
            restore_best_weights=True # Restore best weights after stopping
        )
    ]

    logging.info("Starting model training...")
    history = model.fit(
        train_dataset,
        epochs=EPOCHS,
        validation_data=val_dataset,
        callbacks=callbacks
    )

    # Save the full model structure (architecture) + final weights (best are already saved by checkpoint)
    # model.save(OUTPUT_DIR / "final_model.keras") # Saves full model in Keras v3 format
    # extractor.save(OUTPUT_DIR / "final_extractor.keras")
    
    # To save architecture and allow loading best_model.weights.h5:
    # Need to ensure model is built again exactly the same way then load weights.
    # Or, save the best model completely. Let's load best weights and save that.
    logging.info(f"Loading best weights from {OUTPUT_DIR / 'best_model.weights.h5'}")
    model.load_weights(OUTPUT_DIR / "best_model.weights.h5") # Already done by restore_best_weights typically
    
    # Save the whole model (architecture + best weights)
    model.save(OUTPUT_DIR / "trained_model.keras")
    logging.info(f"Full trained model saved to {OUTPUT_DIR / 'trained_model.keras'}")
    
    # The extractor shares layers with the main model. Saving it separately can be tricky
    # if it's not a standalone model initially.
    # Re-define extractor based on the trained model's layers if necessary, or ensure it's savable.
    # Current build_embedding_model and build_enhanced_embedding_model return it as a separate Keras model.
    extractor.save(OUTPUT_DIR / "trained_extractor.keras")
    logging.info(f"Trained extractor model saved to {OUTPUT_DIR / 'trained_extractor.keras'}")


    # --- 7. Save Preprocessing Artifacts ---
    with open(OUTPUT_DIR / "all_vocabs.pkl", "wb") as f:
        pickle.dump(ALL_VOCABS, f)
    logging.info(f"Vocabularies saved to {OUTPUT_DIR / 'all_vocabs.pkl'}")

    if USE_ENHANCED_MODEL and INCLUDE_STATIC_FEATURES:
        with open(OUTPUT_DIR / "static_preprocessors.pkl", "wb") as f:
            pickle.dump(static_preprocessors_fitted, f)
        logging.info(f"Static feature preprocessors saved to {OUTPUT_DIR / 'static_preprocessors.pkl'}")

    # Save training configuration
    training_run_config = {
        "USE_ENHANCED_MODEL": USE_ENHANCED_MODEL,
        "INCLUDE_STATIC_FEATURES": INCLUDE_STATIC_FEATURES,
        "MASTER_SEQUENCE_FEATURES_MAP": MASTER_SEQUENCE_FEATURES_MAP,
        "MAXLEN": MAXLEN
    }
    with open(OUTPUT_DIR / "training_config.json", "w") as f:
        json.dump(training_run_config, f, indent=4)
    logging.info(f"Training configuration saved to {OUTPUT_DIR / 'training_config.json'}")

    logging.info("Training finished.")
    logging.info(f"Training logs and outputs are in: {OUTPUT_DIR}")
    logging.info(f"Main log file: {log_file_path}")


    # --- Prediction / Embedding Extraction / Evaluation --- 
    # Combine prediction and extraction logic, add evaluation
    if args.mode == "predict" or args.mode == "extract_embeddings":
        logging.info(f"--- Running in {args.mode} mode --- ")
        # Ensure output dir exists from args or defaults
        eval_output_dir = OUTPUT_DIR
        if not eval_output_dir.exists():
            logging.error(f"Output directory for loading model not found: {eval_output_dir}")
            exit(1)

        # 1. Load model and artifacts
        model_path = eval_output_dir / "trained_model.keras"
        extractor_path = eval_output_dir / "trained_extractor.keras"
        vocabs_path = eval_output_dir / "all_vocabs.pkl"
        preprocessors_path = eval_output_dir / "static_preprocessors.pkl"
        static_names_path = eval_output_dir / "processed_static_feature_names.json"
        config_path = eval_output_dir / "training_config.json"

        if not model_path.exists() or not vocabs_path.exists() or not config_path.exists():
            logging.error(f"Essential model artifacts (model, vocabs, or training_config.json) not found in {eval_output_dir}. Cannot proceed.")
            exit(1)
            
        # Load training configuration
        logging.info(f"Loading training configuration from {config_path}")
        with open(config_path, "r") as f:
            loaded_training_config = json.load(f)
        
        pred_use_enhanced_model = loaded_training_config.get("USE_ENHANCED_MODEL", False)
        pred_include_static_features = loaded_training_config.get("INCLUDE_STATIC_FEATURES", False)
        pred_master_sequence_map = loaded_training_config.get("MASTER_SEQUENCE_FEATURES_MAP", {})
        pred_maxlen = loaded_training_config.get("MAXLEN", MAXLEN) # Fallback to global MAXLEN if not in config
        # pred_processed_static_names can be taken from here or from its dedicated file.
        # If static_names_path exists, it's often the direct source from preprocess_static_features.
        # The one in training_config is what was *available* at the end of training for the tf.dataset creation.
        # We will rely on static_names_path for pred_processed_static_names_from_file
        
        logging.info(f"Loaded config - Enhanced: {pred_use_enhanced_model}, Static: {pred_include_static_features}, Maxlen: {pred_maxlen}")
        logging.info(f"Loaded config - Master Seq Map: {pred_master_sequence_map}")

        # Load necessary custom objects - ensure these are defined in embedding_model.py or imported
        try:
            from embedding_model import TokenAndPositionEmbedding, TransformerBlock, AttentionPooling # Make sure these are accessible
            custom_objects = {
                "TokenAndPositionEmbedding": TokenAndPositionEmbedding, 
                "TransformerBlock": TransformerBlock,
                "AttentionPooling": AttentionPooling,
                "SigmoidFocalCrossEntropy": SigmoidFocalCrossEntropy
            }
        except ImportError as e:
             logging.error(f"Failed to import custom layers from embedding_model: {e}. Evaluation/Prediction might fail.")
             custom_objects = {"SigmoidFocalCrossEntropy": SigmoidFocalCrossEntropy} # Minimal required

        logging.info(f"Loading model from {model_path}")
        loaded_model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)
        
        loaded_extractor = None
        if args.mode == "extract_embeddings":
            if extractor_path.exists():
                logging.info(f"Loading extractor from {extractor_path}")
                loaded_extractor = tf.keras.models.load_model(extractor_path, custom_objects=custom_objects)
            else:
                 logging.warning(f"Extractor model not found at {extractor_path}. Cannot extract embeddings.")

        logging.info(f"Loading vocabs from {vocabs_path}")
        with open(vocabs_path, "rb") as f:
            loaded_vocabs = pickle.load(f)
        token_to_id_maps_for_tf_pred = {k: v[0] for k, v in loaded_vocabs.items()}

        loaded_static_preprocessors = None
        pred_processed_static_names_from_file = [] # From dedicated file
        
        # Determine if static features were used based on loaded config AND existence of artifact files
        model_trained_with_static_config = pred_include_static_features
        static_artifacts_exist = preprocessors_path.exists() and static_names_path.exists()

        if model_trained_with_static_config and static_artifacts_exist:
            logging.info("Model was trained with static features (config and artifacts confirm). Loading preprocessors.")
            with open(preprocessors_path, "rb") as f:
                loaded_static_preprocessors = pickle.load(f)
            with open(static_names_path, "r") as f: # Use the dedicated file as primary source for names
                pred_processed_static_names_from_file = json.load(f)
            # Verify consistency if desired:
            # config_static_names = loaded_training_config.get("processed_static_feature_names", [])
            # if set(pred_processed_static_names_from_file) != set(config_static_names):
            #     logging.warning("Mismatch between static feature names in training_config.json and processed_static_feature_names.json. Preferring dedicated file.")
        elif model_trained_with_static_config and not static_artifacts_exist:
            logging.warning("Config indicates static features were used, but artifact files (preprocessors.pkl or names.json) are missing. Static features may not be processed correctly.")
        else:
            logging.info("Model was trained without static features or config indicates so.")
        
        # This line determines which static feature names are used for creating the TF dataset during prediction.
        # It prioritizes names from static_names_path if static features were used and artifacts exist.
        # If not, it defaults to an empty list. The training_config.json no longer stores these names.
        pred_processed_static_feature_names_to_use = pred_processed_static_names_from_file if (model_trained_with_static_config and static_artifacts_exist) else []

        # 2. Load and preprocess test data (using TEST_DATE from args)
        # This duplicates the loading logic from training, ensure consistency
        logging.info(f"Loading test data for date: {TEST_DATE}")
        test_files = glob.glob(str(DATASET_BASE_PATH / f"datetime={TEST_DATE}/**/*.parquet"), recursive=True)
        # REMOVED --max-files logic for predict/extract modes
        # if MAX_FILES_TO_LOAD:
        #     test_files = test_files[:MAX_FILES_TO_LOAD]

        if not test_files:
            logging.error(f"No test files found for date {TEST_DATE} at {DATASET_BASE_PATH / f'datetime={TEST_DATE}'}. Cannot evaluate.")
            exit(1)
        
        logging.info(f"Found {len(test_files)} test files for date {TEST_DATE}. Loading...")
        test_df_list = []
        for f_path in test_files:
            try:
                test_df_list.append(pd.read_parquet(f_path))
                logging.debug(f"Loaded {f_path}")
            except Exception as e:
                logging.error(f"Error loading test file {f_path}: {e}")
        if not test_df_list:
            logging.error(f"Failed to load any test dataframes for date {TEST_DATE}.")
            exit(1)
        test_df_raw = pd.concat(test_df_list, ignore_index=True)
        logging.info(f"Loaded {len(test_df_raw)} total test samples for date {TEST_DATE}.")

        # Preprocess test data
        logging.info(f"Processing test data for {args.mode}...")
        # Need to know which sequence features the loaded model expects
        # This is now directly from loaded_training_config.get("MASTER_SEQUENCE_FEATURES_MAP")
        eval_master_sequence_map = pred_master_sequence_map
        if not eval_master_sequence_map: # Fallback if somehow empty in config
            logging.warning("MASTER_SEQUENCE_FEATURES_MAP from config is empty. Attempting to infer from vocabs (less reliable).")
            eval_master_sequence_map = {}
            for seq_key in loaded_vocabs.keys():
                 if seq_key in PRIMARY_SEQUENCE_FEATURES:
                     eval_master_sequence_map[seq_key] = PRIMARY_SEQUENCE_FEATURES[seq_key]
                 elif seq_key in ALL_AVAILABLE_ADDITIONAL_SEQUENCES:
                     eval_master_sequence_map[seq_key] = ALL_AVAILABLE_ADDITIONAL_SEQUENCES[seq_key]
                 else:
                      logging.warning(f"Sequence key '{seq_key}' found in vocab but not in known sequences. Cannot map to column for evaluation.")
        logging.info(f"Using sequence map for evaluation: {eval_master_sequence_map}")

        # Prepare dataset (derive target label)
        test_df_prepared, test_static_num, test_static_cat = prepare_dataset(
            test_df_raw.copy(), eval_master_sequence_map, STATIC_FEATURES_NUM, STATIC_FEATURES_CAT, LABEL_COL
        ) 
        if test_df_prepared is None:
            logging.error("Failed to prepare test dataset.") 
            exit(1)
            
        # Check if the label column exists after preparation for evaluation
        if 'target_purchase_next_30d' not in test_df_prepared.columns:
             logging.warning(f"Target label 'target_purchase_next_30d' not found in prepared test data. Evaluation metrics cannot be calculated.")
             can_evaluate = False
        else:
             can_evaluate = True

        # Preprocess static features (if model used them)
        test_df_final = None
        if model_trained_with_static_config:
            if loaded_static_preprocessors:
                logging.info("Applying static preprocessors to test data...")
                test_df_final, _, _ = preprocess_static_features(
                    test_df_prepared, test_static_num, test_static_cat,
                    mode='transform', preprocessors=loaded_static_preprocessors
                )
            else: # Model expects static features but preprocessors not found
                logging.warning("Static preprocessors not found, but model expects them. Prediction might fail or be inaccurate.")
                test_df_final = test_df_prepared # Pass unprepared static features, might cause issues
        else:
            test_df_final = test_df_prepared
            
        if test_df_final is None: # Should not happen unless error above
             logging.error("Test DataFrame is None after potential static feature processing.")
             exit(1)

        # 3. Create TF Dataset for prediction
        # Determine if loaded model is enhanced based on inputs or saved config (if available)
        # Simple check: look for static_features input key
        # is_enhanced_loaded = any('static_features' in inp.name for inp in loaded_model.inputs) # Old way
        # includes_static_loaded = 'static_features' in [inp.name for inp in loaded_model.inputs] # Old way
        
        # Use loaded configuration
        is_enhanced_loaded_from_config = pred_use_enhanced_model
        includes_static_loaded_from_config = pred_include_static_features
        current_maxlen_from_config = pred_maxlen

        logging.info(f"Using config for TF Dataset - Enhanced: {is_enhanced_loaded_from_config}, Static: {includes_static_loaded_from_config}, Maxlen: {current_maxlen_from_config}")
        
        test_dataset = create_tf_dataset(
            test_df_final,
            eval_master_sequence_map, 
            token_to_id_maps_for_tf_pred,
            pred_processed_static_feature_names_to_use, # Use the names consistent with preprocessors
            'target_purchase_next_30d' if can_evaluate else None, 
            current_maxlen_from_config, # Use maxlen from loaded config
            BATCH_SIZE, 
            is_training=False,
            use_enhanced_model=is_enhanced_loaded_from_config, # from loaded config
            include_static_features=includes_static_loaded_from_config # from loaded config
        )

        # 4. Perform Prediction or Extraction
        if args.mode == "predict":
            logging.info("Making predictions...")
            predictions_raw = loaded_model.predict(test_dataset)
            predictions = predictions_raw.flatten() # Ensure predictions are 1D array
            logging.info(f"Predictions shape: {predictions.shape}")
            
            # Save predictions with user_ids
            user_ids_test = test_df_final[USER_ID_COL].values
            pred_df = pd.DataFrame({USER_ID_COL: user_ids_test, 'prediction_score': predictions})
            pred_output_path = eval_output_dir / "predictions.csv"
            pred_df.to_csv(pred_output_path, index=False)
            logging.info(f"Predictions saved to {pred_output_path}")

            # --- Evaluation --- 
            if can_evaluate:
                logging.info("Calculating evaluation metrics...")
                true_labels = test_df_final['target_purchase_next_30d'].values
                
                # Basic Metrics
                roc_auc = roc_auc_score(true_labels, predictions)
                pr_auc = average_precision_score(true_labels, predictions)
                logging.info(f"  ROC AUC: {roc_auc:.6f}")
                logging.info(f"  PR AUC: {pr_auc:.6f}")
                
                # Precision/Recall @ K
                k = 840
                if len(pred_df) >= k:
                    # Combine predictions and labels for sorting
                    eval_df = pd.DataFrame({'score': predictions, 'label': true_labels})
                    eval_df_sorted = eval_df.sort_values('score', ascending=False)
                    
                    top_k = eval_df_sorted.head(k)
                    true_positives_at_k = top_k['label'].sum()
                    total_positives = eval_df['label'].sum()
                    
                    precision_at_k = true_positives_at_k / k if k > 0 else 0
                    recall_at_k = true_positives_at_k / total_positives if total_positives > 0 else 0
                    
                    logging.info(f"  Precision@{k}: {precision_at_k:.6f}")
                    logging.info(f"  Recall@{k}: {recall_at_k:.6f}")
                else:
                    logging.warning(f"Test set size ({len(pred_df)}) is smaller than K={k}. Cannot calculate Precision/Recall@{k}.")
                    precision_at_k = None
                    recall_at_k = None
                
                # Save Metrics
                metrics = {
                    "ROC_AUC": roc_auc,
                    "PR_AUC": pr_auc,
                    f"Precision@{k}": precision_at_k,
                    f"Recall@{k}": recall_at_k,
                    "test_data_date": TEST_DATE,
                    "test_sample_size": len(test_df_final),
                    "positive_samples_test": int(true_labels.sum()),
                }
                metrics_output_path = eval_output_dir / "evaluation_metrics.json"
                with open(metrics_output_path, 'w') as f:
                    json.dump(metrics, f, indent=4)
                logging.info(f"Evaluation metrics saved to {metrics_output_path}")
            else:
                logging.info("Skipping evaluation metric calculation as target labels were not found.")


        if args.mode == "extract_embeddings":
            if loaded_extractor:
                logging.info("Extracting embeddings...")
                embeddings = loaded_extractor.predict(test_dataset)
                logging.info(f"Embeddings shape: {embeddings.shape}")
                
                # Save embeddings with user_ids
                user_ids_test = test_df_final[USER_ID_COL].values # Ensure USER_ID_COL is in test_df_final
                
                # Create a dictionary for saving, including user_id and embeddings
                embedding_results = {
                    'user_ids': user_ids_test.tolist(),
                    'embeddings': embeddings.tolist() # Convert to list for JSON or handle as needed for Parquet/PKL
                }
                
                # Example: Save to a pickle file
                emb_output_path = eval_output_dir / "user_embeddings.pkl"
                with open(emb_output_path, "wb") as f:
                    pickle.dump(embedding_results, f)
                logging.info(f"Embeddings saved to {emb_output_path}")
            else:
                 logging.info("Skipping embedding extraction as extractor model was not loaded.")

        logging.info(f"{args.mode} mode finished.")
        # End of prediction/extraction/evaluation block

# Make sure the script can be run with --mode predict or --mode extract_embeddings
# e.g. by adding 'mode' to parse_args()
# And then args.mode will be correctly populated.
# This change to parse_args is NOT included in this diff to keep it focused on the main refactor,
# but would be needed for the predict/extract block to run via CLI.