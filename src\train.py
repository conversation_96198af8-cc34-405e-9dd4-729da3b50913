#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Main training script for conversion rate prediction models.
"""
import os
import sys
import json
import logging
import argparse
import tensorflow as tf
import numpy as np
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.data.loader import DataLoader
from src.data.preprocessor import DataPreprocessor
from src.features.builder import FeatureBuilder
from src.training.trainer import ModelTrainer
from src.evaluation.evaluator import ModelEvaluator
from src.utils.config_utils import ConfigManager
from src.features.imbalance import apply_smote, apply_smote_enn, apply_optimized_smote_enn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

logger = logging.getLogger(__name__)

def setup_logging(log_level=logging.INFO, log_file=None):
    """
    Set up logging configuration.
    
    Args:
        log_level (int): Logging level.
        log_file (str, optional): Path to log file.
    """
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Configure file handler if log file is provided
    if log_file:
        Path(os.path.dirname(log_file)).mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Silence third-party loggers
    logging.getLogger('qcloud_cos').setLevel(logging.WARNING)


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Train a conversion rate prediction model')
    
    # Model configuration
    parser.add_argument('--model_code', required=True,
                        help='Model configuration file name (without .json extension)')
    parser.add_argument('--run_name', default=datetime.now().strftime('%Y%m%d_%H%M%S'),
                        help='Name for this training run')
    parser.add_argument('--dataset_code', required=True,
                        help='Dataset code/location')
    parser.add_argument('--evaluate_file', default=None,
                        help='Evaluation dataset filename')
    
    # Training parameters
    parser.add_argument('--epochs', type=int, default=150,
                        help='Maximum training epochs')
    parser.add_argument('--batch_size', type=int, default=8192,
                        help='Training batch size')
    parser.add_argument('--patience', type=int, default=10,
                        help='Early stopping patience')
    
    # Model components
    parser.add_argument('--use_cross_layer', type=bool, default=True,
                        help='Whether to use feature cross layer')
    parser.add_argument('--use_time_attention', type=bool, default=True,
                        help='Whether to use time attention mechanism')
    parser.add_argument('--use_transformer', action='store_true', default=False,
                        help='Whether to use transformer architecture')
    
    # Data and output paths
    parser.add_argument('--log_file', default=None,
                        help='Custom log file path')
    parser.add_argument('--data_dir', default='.',
                        help='Root directory for data')
    parser.add_argument('--output_dir', default='src/evaluation',
                        help='Output directory for evaluation results')
    
    # New parameters for loss function
    parser.add_argument('--loss_type', default='focal', choices=['standard', 'weighted', 'focal', 'asymmetric_focal'],
                        help='Type of loss function: standard, weighted, focal, or asymmetric_focal')
    parser.add_argument('--pos_weight', type=float, default=20.0,
                        help='Weight multiplier for positive class (to improve recall)')
    parser.add_argument('--use_month_weights', action='store_true', default=False,
                        help='Whether to use month-specific weights in loss function')
    parser.add_argument('--use_multitask', action='store_true', default=False,
                        help='Use multitask learning for cumulative prediction')
    
    # New parameters for sampling and class balance
    parser.add_argument('--use_balanced_sampling', action='store_true', default=False,
                        help='Whether to use balanced batch sampling to control positive ratio')
    parser.add_argument('--pos_ratio', type=float, default=0.5,
                        help='Ratio of positive samples in each batch when using balanced sampling')
    parser.add_argument('--use_oversampling', action='store_true', default=False,
                        help='Whether to use oversampling for positive samples')
    parser.add_argument('--oversampling_weight', type=float, default=5.0,
                        help='Weight for positive sample oversampling')
    
    # New parameters for feature engineering
    parser.add_argument('--use_enhanced_features', action='store_true', default=False,
                        help='Whether to use enhanced features for conversion prediction')
    
    # New parameters for resampling strategy
    parser.add_argument('--resampling_strategy', type=str, default=None, 
                        choices=['smote', 'smote_enn', 'optimized_smote_enn'],
                        help='Resampling strategy')
    parser.add_argument('--smote_k', type=int, default=5,
                        help='k neighbors for SMOTE')
    parser.add_argument('--enn_k', type=int, default=3,
                        help='k neighbors for ENN')
    parser.add_argument('--smote_ratio', type=float, default=0.5,
                        help='smote ratio for optimized SMOTE-ENN')
    
    # New parameters for ESMM mode
    parser.add_argument('--time_decay_factor', type=float, default=0.05, help='Time decay factor for attention mechanism')
    parser.add_argument('--use_esmm', action='store_true', default=False,
                        help='Use ESMM model for test drive (CTR) and purchase (CTCVR) prediction')
    
    # New parameters for ESM2 mode
    parser.add_argument('--use_esm2', action='store_true', default=False,
                        help='Use ESM2 model for sequential multi-task prediction')
    parser.add_argument('--esm2_task_names', type=str, default='test_drive_output,appointment_output,purchase_output',
                        help='Comma-separated list of task names for ESM2 model (in sequence order)')
    
    return parser.parse_args()


def main():
    """Main execution function."""
    # Parse arguments
    args = parse_args()
    
    # Setup logging
    log_file = args.log_file or f"src/evaluation/{args.run_name}/training.log"
    setup_logging(log_file=log_file)
    
    logger.info(f"Starting training run: {args.run_name}")
    logger.info(f"Model code: {args.model_code}")
    logger.info(f"Dataset code: {args.dataset_code}")
    
    try:
        # Load model configuration
        config_manager = ConfigManager()
        
        # 首先尝试从configs目录读取配置文件
        model_param_path = f"src/configs/models/{args.model_code}.json"
        if not os.path.exists(model_param_path):
            # 如果找不到，尝试从项目根目录读取
            model_param_path = f"{args.model_code}.json"
            if not os.path.exists(model_param_path):
                logger.error(f"Model parameter file not found: {model_param_path}")
                return 1
                
        logger.info(f"Loading model configuration from: {model_param_path}")
        model_config = config_manager.load_config(model_param_path)
        
        # 同样方式处理数据集配置 - 指向 data/configs/ 目录
        dataset_config_path = os.path.join(args.data_dir, 'configs', f"{args.dataset_code}.json")
        if not os.path.exists(dataset_config_path):
            # 如果在 data/configs/ 下找不到，尝试旧路径 src/configs/datasets/ (兼容)
            dataset_config_path_alt1 = f"src/configs/datasets/{args.dataset_code}.json" 
            if os.path.exists(dataset_config_path_alt1):
                dataset_config_path = dataset_config_path_alt1
            else:
                # 再尝试旧路径 src/configs/ (兼容)
                dataset_config_path_alt2 = f"src/configs/{args.dataset_code}.json"
                if os.path.exists(dataset_config_path_alt2):
                    dataset_config_path = dataset_config_path_alt2
                else:
                     # 最后尝试项目根目录
                    dataset_config_path_root = f"{args.dataset_code}.json"
                    if os.path.exists(dataset_config_path_root):
                        dataset_config_path = dataset_config_path_root
                    else:
                        logger.error(f"Dataset configuration file not found in expected locations: data/configs/, src/configs/datasets/, src/configs/, or root.")
                        return 1
                
        logger.info(f"Loading dataset configuration from: {dataset_config_path}")
        
        # Comment out the incorrect premature initialization
        # # Load dataset config using ConfigManager
        # dataset_config = config_manager.load_config(dataset_config_path)
        # # Pass dataset config path to DataLoader
        # data_loader = DataLoader(dataset_path, dataset_config_path)
        
        # Override some config parameters with command line arguments
        model_config['batch_size'] = args.batch_size
        model_config['loss_type'] = args.loss_type
        model_config['pos_weight'] = args.pos_weight
        model_config['use_month_weights'] = args.use_month_weights
        model_config['use_multitask'] = args.use_multitask
        model_config['use_esmm'] = args.use_esmm
        model_config['resampling_strategy'] = args.resampling_strategy
        model_config['smote_k'] = args.smote_k
        model_config['time_decay_factor'] = args.time_decay_factor
        model_config['use_esm2'] = args.use_esm2
        model_config['esm2_task_names'] = args.esm2_task_names.split(',')
        
        # 处理--use_esm2和--use_esmm的互斥关系
        if args.use_esm2 and args.use_esmm:
            logger.warning("Both --use_esm2 and --use_esmm are enabled. Only --use_esm2 will be applied.")
            model_config['use_esmm'] = False
        
        if args.use_esm2 and args.use_multitask:
            logger.warning("Both --use_esm2 and --use_multitask are enabled. Only --use_esm2 will be applied.")
            model_config['use_multitask'] = False
        
        # Get model parameters
        network_name = model_config.get("network_name")
        predict_method = model_config.get("predict_method", "6m")
        train_dates = model_config.get("train_dates", [])
        test_dates = model_config.get("test_dates", [])
        mask_label = model_config.get("mask_label", None)
        dict_raw_features = model_config.get("RawFeature", {})
        extra_datasets = model_config.get("Extra", [])
        
        logger.info(f"Network name: {network_name}")
        logger.info(f"Prediction method: {predict_method}")
        logger.info(f"Train dates: {train_dates}")
        logger.info(f"Test dates: {test_dates}")
        
        # Prepare column lists
        list_raw_features = list(dict_raw_features.keys())
        list_raw_labels = ["purchase_days_nio_new_car_total"]
        
        logger.info(f"Feature count: {len(list_raw_features)}")
        logger.info(f"Label count: {len(list_raw_labels)}")
        
        # Determine columns to read
        read_columns = ["user_id", "datetime"] + list_raw_features + list_raw_labels
        dataset_read_columns = read_columns + ["m_purchase_days_nio_new_car"]
        
        if mask_label:
            dataset_read_columns.append(mask_label)
        
        # 1. Data Loading
        logger.info("Step 1: Loading data")
        # 构建完整的数据集路径
        dataset_path = os.path.join(args.data_dir, args.dataset_code)
        
        # Load dataset config using ConfigManager
        dataset_config = config_manager.load_config(dataset_config_path)
        
        # Initialize DataLoader with both paths
        data_loader = DataLoader(dataset_path, dataset_config_path)
        
        # Load all date partitions for train/test
        dataset_dates = sorted(list(set(train_dates + test_dates)))
        df_raw = data_loader.load_dataset(
            dates=dataset_dates,
            columns=dataset_read_columns,
            extra_datasets=extra_datasets
        )
        
        # Load evaluation dataset
        df_evaluate = data_loader.load_evaluation_data(
            evaluate_file=args.evaluate_file,
            columns=read_columns,
            extra_datasets=extra_datasets
        )
        
        # 2. Data Preprocessing
        logger.info("Step 2: Data preprocessing")
        preprocessor = DataPreprocessor(data_loader.feature_padding_dict, train_dates=train_dates)
        
        # Preprocess features based on dataset configuration
        df_raw = preprocessor.preprocess_features(df_raw)
        df_evaluate = preprocessor.preprocess_features(df_evaluate)
        
        # Preprocess features based on model configuration
        df_raw = preprocessor.preprocess_model_features(df_raw, dict_raw_features)
        df_evaluate = preprocessor.preprocess_model_features(df_evaluate, dict_raw_features)
        
        # Process labels
        df_raw = preprocessor.process_purchase_labels(df_raw)
        df_evaluate = preprocessor.process_purchase_labels(
            df_evaluate, 
            source_column="purchase_days_nio_new_car_total", 
            target_column="m_purchase_days_nio_new_car"
        )
        
        # 3. Feature Engineering
        logger.info("Step 3: Feature engineering")
        feature_builder = FeatureBuilder()
        
        # Split data into train and test sets
        df_train_m, df_test_m = feature_builder.split_train_test(df_raw, test_dates)
        
        # 应用特征工程（如果启用）
        if args.use_enhanced_features:
            logger.info("Applying enhanced feature engineering")
            df_train_m = feature_builder.engineer_conversion_features(df_train_m)
            df_test_m = feature_builder.engineer_conversion_features(df_test_m)
            df_evaluate = feature_builder.engineer_conversion_features(df_evaluate)
            
            # 更新原始特征字典以包含新特征
            new_features = ['action_density_week', 'action_density_month', 'action_acceleration', 
                            'car_specific_ratio', 'purchase_intent_score', 'conversion_path_score']
            
            for new_feature in new_features:
                if new_feature in df_train_m.columns:
                    dict_raw_features[new_feature] = {"dtype": "Bucket"}
            
            added_features = sum(1 for f in new_features if f in df_train_m.columns)
            logger.info(f"Added {added_features} enhanced features")
        
        # Generate datasets for training
        label_name = "m_purchase_days_nio_new_car_consum"
        ds_train_m = feature_builder.generate_dataset(
            df_train_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method,
            use_balanced_sampling=args.use_balanced_sampling,
            pos_ratio=args.pos_ratio,
            use_oversampling=args.use_oversampling,
            pos_weight=args.pos_weight,
            use_esmm=args.use_esmm,
            use_esm2=args.use_esm2,
            esm2_task_names=model_config['esm2_task_names']
        )
        
        ds_test_m = feature_builder.generate_dataset(
            df_test_m, 
            dict_raw_features, 
            label=label_name, 
            batch_size=args.batch_size, 
            mask_label=mask_label, 
            predict_method=predict_method,
            use_esmm=args.use_esmm,
            use_esm2=args.use_esm2,
            esm2_task_names=model_config['esm2_task_names']
        )
        
        # 应用重采样策略
        if args.resampling_strategy:
            logger.info(f"Applying resampling strategy: {args.resampling_strategy}")
            
            # 将TensorFlow Dataset转换为NumPy数组
            features_list = []
            labels_list = []
            feature_keys = []  # Store the feature keys to reconstruct the dictionary later
            feature_info = {}  # Store information about each feature column
            
            # Get the first batch to extract feature keys and shapes
            for features_batch, labels_batch in ds_train_m.take(1):
                if isinstance(features_batch, dict):
                    feature_keys = list(features_batch.keys())
                    
                    # Collect shape info for each feature
                    col_index = 0
                    for key, tensor in features_batch.items():
                        tensor_shape = tensor.shape
                        is_sequence = len(tensor_shape) > 2
                        
                        # Record info about this feature
                        feature_info[key] = {
                            'shape': tensor_shape,
                            'is_sequence': is_sequence,
                            'start_idx': col_index
                        }
                        
                        # Convert to numpy to get actual values
                        feature_np = tensor.numpy()
                        
                        # Calculate total number of elements per sample
                        if is_sequence:
                            # For sequence features, we need to flatten all dimensions except batch
                            elements_per_sample = np.prod(feature_np.shape[1:])
                        else:
                            elements_per_sample = feature_np.shape[-1] if len(feature_np.shape) > 1 else 1
                            
                        feature_info[key]['elements_per_sample'] = int(elements_per_sample)
                        feature_info[key]['end_idx'] = col_index + int(elements_per_sample)
                        col_index += int(elements_per_sample)
                        
                break
                
            # 遍历数据集中的批次
            for features, labels in ds_train_m:
                # 处理特征字典
                if isinstance(features, dict):
                    batch_features = []
                    # 展平每个特征
                    for key, tensor in features.items():
                        # 转换为NumPy并展平
                        feature_np = tensor.numpy()
                        
                        # Record shape for tracking
                        if key not in feature_info:
                            continue
                        
                        # Reshape based on whether it's a sequence or not
                        if feature_info[key]['is_sequence']:
                            # For sequence features, we need to flatten for SMOTE
                            # but remember the original shape for reconstruction
                            reshaped_feat = feature_np.reshape(feature_np.shape[0], -1)
                        else:
                            # For non-sequence features, ensure they're 2D
                            if feature_np.ndim == 1:
                                reshaped_feat = feature_np.reshape(-1, 1)
                            else:
                                reshaped_feat = feature_np
                                
                        batch_features.append(reshaped_feat)
                    
                    # 检查所有特征维度并记录
                    if logger.isEnabledFor(logging.DEBUG):
                        for i, feat in enumerate(batch_features):
                            logger.debug(f"Feature {i} shape: {feat.shape}")
                    
                    try:
                        # 水平堆叠所有特征
                        features_array = np.hstack(batch_features)
                    except ValueError as e:
                        # 如果堆叠失败，记录错误并尝试修正
                        logger.error(f"堆叠特征失败: {str(e)}")
                        logger.warning("跳过此批次")
                        continue
                else:
                    # 如果不是字典，直接使用
                    features_array = features.numpy()
                    # 确保是2D的
                    if features_array.ndim == 1:
                        features_array = features_array.reshape(-1, 1)
                
                # 确保标签是2D的
                batch_labels = labels.numpy()
                if batch_labels.ndim == 1:
                    batch_labels = batch_labels.reshape(-1, 1)
                
                # 添加到列表
                features_list.append(features_array)
                labels_list.append(batch_labels)
            
            # 组合所有批次
            if features_list:
                try:
                    # 检查所有特征数组的形状
                    logger.info(f"特征批次数量: {len(features_list)}")
                    
                    # 尝试垂直堆叠
                    X_train_np = np.vstack(features_list)
                    y_train_np = np.vstack(labels_list)
                    
                    logger.info(f"Extracted features shape: {X_train_np.shape}, labels shape: {y_train_np.shape}")
                    
                    # 确保所有特征都是数值型
                    invalid_indices = []
                    for i in range(X_train_np.shape[1]):
                        try:
                            # 尝试将列转换为浮点数
                            X_train_np[:, i] = X_train_np[:, i].astype(float)
                        except (ValueError, TypeError):
                            # 如果失败，记录这个索引
                            invalid_indices.append(i)
                    
                    if invalid_indices:
                        logger.warning(f"Found {len(invalid_indices)} non-numeric feature columns")
                        # 创建一个掩码，只保留数值型特征列
                        valid_cols = np.ones(X_train_np.shape[1], dtype=bool)
                        valid_cols[invalid_indices] = False
                        
                        # 过滤掉非数值型特征
                        X_train_np = X_train_np[:, valid_cols]
                        logger.info(f"Filtered feature shape after removing non-numeric columns: {X_train_np.shape}")
                    
                    # 应用重采样策略
                    if args.resampling_strategy == 'smote':
                        resampled_features, resampled_labels = apply_smote(
                            X_train_np, y_train_np, 
                            k_neighbors=args.smote_k, 
                            random_state=42
                        )
                    elif args.resampling_strategy == 'smote_enn':
                        resampled_features, resampled_labels = apply_smote_enn(
                            X_train_np, y_train_np, 
                            k_neighbors=args.smote_k, 
                            enn_k=args.enn_k, 
                            random_state=42
                        )
                    elif args.resampling_strategy == 'optimized_smote_enn':
                        resampled_features, resampled_labels = apply_optimized_smote_enn(
                            X_train_np, y_train_np, 
                            smote_ratio=args.smote_ratio,
                            k_neighbors=args.smote_k, 
                            enn_k=args.enn_k, 
                            random_state=42
                        )
                    
                    logger.info(f"Resampled data shapes - Features: {resampled_features.shape}, Labels: {resampled_labels.shape}")
                    
                    # 创建TensorFlow数据集
                    if feature_keys and feature_keys[0] == "feature":
                        # Use the simple format when there's only a single "feature" key
                        ds_train_m = tf.data.Dataset.from_tensor_slices(
                            ({"feature": resampled_features}, resampled_labels)
                        ).shuffle(10000).batch(args.batch_size)
                    else:
                        # Reconstruct the original feature dictionary structure that the model expects
                        logger.info("Reconstructing original feature dictionary structure with all required features")
                        
                        # Collect original feature shapes first
                        original_shapes = {}
                        for features_batch, _ in ds_train_m.take(1):
                            if isinstance(features_batch, dict):
                                for key, tensor in features_batch.items():
                                    original_shapes[key] = tensor.shape
                        
                        # Create a dictionary with the original feature structure and shapes
                        feature_dict = {}
                        batch_size = resampled_features.shape[0]
                        
                        for feature_name in feature_keys:
                            if feature_name == "feature":
                                # This is where we would put the resampled features
                                feature_dict[feature_name] = resampled_features
                            elif feature_name in feature_info:
                                # Extract the specific portion of resampled features for this feature
                                info = feature_info[feature_name]
                                
                                if info['start_idx'] < resampled_features.shape[1] and info['end_idx'] <= resampled_features.shape[1]:
                                    # Extract the relevant columns for this feature
                                    feature_slice = resampled_features[:, info['start_idx']:info['end_idx']]
                                    
                                    # Reshape back to the original shape if needed
                                    if info['is_sequence']:
                                        # Reshape to match the original sequence shape
                                        original_shape = list(info['shape'])
                                        reshape_dims = [batch_size] + original_shape[1:]
                                        try:
                                            reshaped_feature = feature_slice.reshape(reshape_dims)
                                            feature_dict[feature_name] = reshaped_feature
                                        except ValueError:
                                            # If reshape fails, use zeros
                                            logger.warning(f"Failed to reshape {feature_name}, using zeros instead")
                                            feature_dict[feature_name] = tf.zeros([batch_size] + original_shape[1:], dtype=tf.float32)
                                    else:
                                        # For non-sequence features
                                        feature_dict[feature_name] = feature_slice
                                else:
                                    # If indices are out of bounds, use zeros
                                    if info['is_sequence']:
                                        shape = [batch_size] + list(info['shape'][1:])
                                    else:
                                        shape = (batch_size, info['elements_per_sample'])
                                    
                                    feature_dict[feature_name] = tf.zeros(shape, dtype=tf.float32)
                            else:
                                # Fallback for features not in feature_info
                                feature_dict[feature_name] = tf.zeros((batch_size, 1), dtype=tf.float32)
                        
                        # Add the full feature tensor if needed
                        if "feature" not in feature_dict:
                            feature_dict["feature"] = resampled_features
                        
                        # Create dataset with the reconstructed feature dictionary
                        ds_train_m = tf.data.Dataset.from_tensor_slices(
                            (feature_dict, resampled_labels)
                        ).shuffle(10000).batch(args.batch_size)
                except ValueError as e:
                    logger.error(f"重采样处理失败: {str(e)}")
                    logger.warning("跳过重采样，使用原始数据集")
            else:
                logger.warning("Failed to extract features and labels from dataset, skipping resampling")
        
        # 4. Model Training
        logger.info("Step 4: Model training")
        # 使用评估输出目录作为模型输出目录的基础
        output_dir = os.path.join(args.output_dir, args.run_name)
        trainer = ModelTrainer(model_config, args.run_name, output_dir)
        
        # Build model
        model = trainer.build_model(
            use_cross_layer=args.use_cross_layer,
            use_time_attention=args.use_time_attention,
            use_transformer=args.use_transformer
        )
        
        # Train model
        trainer.train(
            ds_train_m,
            ds_test_m,
            epochs=args.epochs,
            early_stopping_patience=args.patience
        )
        
        # 5. Model Inference
        logger.info("Step 5: Model inference")
        # Run inference on test set
        test_predictions = trainer.inference(df_test_m, dict_raw_features)
        df_test_m["result"] = list(test_predictions)
        
        # Run inference on evaluation set
        evaluate_predictions = trainer.inference(df_evaluate, dict_raw_features)
        df_evaluate["result"] = list(evaluate_predictions)
        
        # 6. Model Evaluation
        logger.info("Step 6: Model evaluation")
        evaluator = ModelEvaluator(output_dir)
        
        evaluation_results = evaluator.evaluate_model(
            df_test_m=df_test_m,
            df_evaluate=df_evaluate,
            label_column="m_purchase_days_nio_new_car",
            pred_column="result",
            mask_label_column=mask_label,
            model_code=args.model_code
        )
        
        logger.info("Training pipeline completed successfully")
        return 0
        
    except Exception as e:
        logger.exception(f"Error in training pipeline: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 