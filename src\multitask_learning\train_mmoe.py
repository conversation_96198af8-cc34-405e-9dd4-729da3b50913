"""
训练和评估MMOE模型

使用UCI Adult数据集演示MMOE模型的训练和评估过程
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import roc_auc_score, accuracy_score, log_loss
import tensorflow as tf

from mmoe_model import MMOE, compile_mmoe_model

# 设置随机种子以确保结果可复现
np.random.seed(42)
tf.random.set_seed(42)

def load_and_process_data(data_path, test_size=0.2):
    """
    加载和预处理UCI Adult数据集
    
    Args:
        data_path: 数据集路径
        test_size: 测试集比例
        
    Returns:
        训练和测试数据集，特征配置信息
    """
    # 列名
    column_names = [
        'age', 'workclass', 'fnlwgt', 'education', 'education_num', 
        'marital_status', 'occupation', 'relationship', 'race', 'gender',
        'capital_gain', 'capital_loss', 'hours_per_week', 'native_country', 'income_50k'
    ]
    
    # 加载数据集
    df = pd.read_csv(data_path, names=column_names, sep=',\s+', engine='python', na_values='?')
    
    # 删除包含缺失值的行
    df = df.dropna()
    
    # 处理目标变量
    df['income_50k'] = (df['income_50k'] == '>50K').astype(int)
    
    # 创建一个额外的虚拟任务（将marital_status作为第二个任务）
    # 为了演示MMOE模型，我们将marital_status是否为'Married-civ-spouse'作为第二个任务的目标
    df['marital_binary'] = (df['marital_status'] == ' Married-civ-spouse').astype(int)
    
    # 为了演示ESMM模型中的级联任务关系，创建一个复合任务
    # CTCVR = CTR(income_50k) * CVR(marital_binary) 
    # 这里我们假设income_50k是CTR任务，marital_binary是CVR任务
    df['ctcvr'] = df['income_50k'] * df['marital_binary']
    
    # 特征处理
    categorical_cols = ['workclass', 'education', 'marital_status', 'occupation', 
                         'relationship', 'race', 'gender', 'native_country']
    
    numerical_cols = ['age', 'fnlwgt', 'education_num', 'capital_gain', 'capital_loss', 'hours_per_week']
    
    # 对数值特征进行标准化
    scaler = StandardScaler()
    df[numerical_cols] = scaler.fit_transform(df[numerical_cols])
    
    # 对类别特征进行编码
    encoders = {}
    for col in categorical_cols:
        encoders[col] = LabelEncoder()
        df[col] = encoders[col].fit_transform(df[col])
    
    # 准备特征和标签
    feature_cols = numerical_cols + categorical_cols
    X = df[feature_cols].values
    y_ctr = df['income_50k'].values
    y_ctcvr = df['ctcvr'].values
    
    # 划分训练集和测试集
    X_train, X_test, y_train_ctr, y_test_ctr, y_train_ctcvr, y_test_ctcvr = train_test_split(
        X, y_ctr, y_ctcvr, test_size=test_size, random_state=42
    )
    
    # 特征配置
    feature_config = {
        'numerical_cols': numerical_cols,
        'categorical_cols': categorical_cols,
        'categorical_dims': {col: len(encoders[col].classes_) for col in categorical_cols},
        'feature_cols': feature_cols
    }
    
    return (X_train, y_train_ctr, y_train_ctcvr), (X_test, y_test_ctr, y_test_ctcvr), feature_config

def prepare_input_data(X, feature_config):
    """
    准备模型输入数据
    
    Args:
        X: 特征数据
        feature_config: 特征配置信息
        
    Returns:
        模型输入数据字典
    """
    inputs = {}
    
    # 数值特征
    for i, col in enumerate(feature_config['numerical_cols']):
        inputs[f'numerical_{col}'] = X[:, i:i+1]
    
    # 类别特征
    num_numerical = len(feature_config['numerical_cols'])
    for i, col in enumerate(feature_config['categorical_cols']):
        inputs[f'categorical_{col}'] = X[:, num_numerical+i:num_numerical+i+1]
    
    return inputs

def plot_training_history(history, output_path="mmoe_history.png"):
    """
    绘制并保存训练历史
    
    Args:
        history: 训练历史对象
        output_path: 输出图像的保存路径
    """
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))
    
    # CTR任务的损失
    axs[0, 0].plot(history.history['ctr_loss'])
    axs[0, 0].plot(history.history['val_ctr_loss'])
    axs[0, 0].set_title('CTR Loss')
    axs[0, 0].set_ylabel('Loss')
    axs[0, 0].set_xlabel('Epoch')
    axs[0, 0].legend(['Train', 'Validation'], loc='upper right')
    
    # CTCVR任务的损失
    axs[0, 1].plot(history.history['ctcvr_loss'])
    axs[0, 1].plot(history.history['val_ctcvr_loss'])
    axs[0, 1].set_title('CTCVR Loss')
    axs[0, 1].set_ylabel('Loss')
    axs[0, 1].set_xlabel('Epoch')
    axs[0, 1].legend(['Train', 'Validation'], loc='upper right')
    
    # CTR任务的AUC
    axs[1, 0].plot(history.history['ctr_auc_2'])
    axs[1, 0].plot(history.history['val_ctr_auc_2'])
    axs[1, 0].set_title('CTR AUC')
    axs[1, 0].set_ylabel('AUC')
    axs[1, 0].set_xlabel('Epoch')
    axs[1, 0].legend(['Train', 'Validation'], loc='lower right')
    
    # CTCVR任务的AUC
    axs[1, 1].plot(history.history['ctcvr_auc_2'])
    axs[1, 1].plot(history.history['val_ctcvr_auc_2'])
    axs[1, 1].set_title('CTCVR AUC')
    axs[1, 1].set_ylabel('AUC')
    axs[1, 1].set_xlabel('Epoch')
    axs[1, 1].legend(['Train', 'Validation'], loc='lower right')
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def evaluate_model(model, X, y_ctr, y_ctcvr, feature_config, phase="测试集"):
    """
    评估模型性能
    
    Args:
        model: 训练好的模型
        X: 特征数据
        y_ctr: CTR任务的目标
        y_ctcvr: CTCVR任务的目标
        feature_config: 特征配置信息
        phase: 评估阶段，用于打印输出
        
    Returns:
        包含各项评估指标的字典
    """
    # 准备输入数据
    inputs = prepare_input_data(X, feature_config)
    
    # 预测
    y_pred_ctr, y_pred_ctcvr = model.predict(inputs)
    y_pred_ctr = y_pred_ctr.flatten()
    y_pred_ctcvr = y_pred_ctcvr.flatten()
    
    # 计算AUC
    ctr_auc = roc_auc_score(y_ctr, y_pred_ctr)
    ctcvr_auc = roc_auc_score(y_ctcvr, y_pred_ctcvr)
    
    # 计算准确率
    ctr_acc = accuracy_score(y_ctr, (y_pred_ctr > 0.5).astype(int))
    ctcvr_acc = accuracy_score(y_ctcvr, (y_pred_ctcvr > 0.5).astype(int))
    
    # 计算对数损失
    ctr_log_loss = log_loss(y_ctr, y_pred_ctr)
    ctcvr_log_loss = log_loss(y_ctcvr, y_pred_ctcvr)
    
    # 打印评估结果
    print(f"\n{phase}评估结果:")
    print(f"CTR AUC: {ctr_auc:.4f}, 准确率: {ctr_acc:.4f}, 对数损失: {ctr_log_loss:.4f}")
    print(f"CTCVR AUC: {ctcvr_auc:.4f}, 准确率: {ctcvr_acc:.4f}, 对数损失: {ctcvr_log_loss:.4f}")
    
    # 返回评估指标
    metrics = {
        'ctr_auc': ctr_auc,
        'ctr_acc': ctr_acc,
        'ctr_log_loss': ctr_log_loss,
        'ctcvr_auc': ctcvr_auc,
        'ctcvr_acc': ctcvr_acc,
        'ctcvr_log_loss': ctcvr_log_loss
    }
    
    return metrics

def main():
    """主函数"""
    # 设置GPU内存增长
    physical_devices = tf.config.list_physical_devices('GPU')
    if physical_devices:
        tf.config.experimental.set_memory_growth(physical_devices[0], True)
    
    # 数据路径
    data_path = "data/adult/adult.data"
    
    print("========== MMOE多任务学习模型训练和评估 ==========")
    
    print("\n1. 加载和预处理数据...")
    try:
        # 尝试加载数据
        (X_train, y_train_ctr, y_train_ctcvr), (X_test, y_test_ctr, y_test_ctcvr), feature_config = load_and_process_data(data_path)
        print(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")
        print(f"正样本比例 - CTR: {y_train_ctr.mean():.4f}, CTCVR: {y_train_ctcvr.mean():.4f}")
    except FileNotFoundError:
        print(f"无法找到数据文件: {data_path}")
        print("请从UCI Machine Learning Repository下载Adult数据集并放置在data/adult/目录下")
        print("下载链接: https://archive.ics.uci.edu/ml/datasets/adult")
        return
    
    print("\n2. 构建MMOE模型...")
    # 构建和编译模型
    model = MMOE(
        feature_config,
        num_experts=4,
        expert_units=(128, 64),
        tower_units=(32,),
        embedding_dim=16,
        expert_dropout=0.2,
        tower_dropout=0.1,
        expert_use_bn=True,
        tower_use_bn=True,
        l2_reg_embedding=1e-5,
        l2_reg_expert=1e-5,
        l2_reg_tower=1e-5,
        task_names=('ctr_output', 'ctcvr_output')
    )
    model = compile_mmoe_model(model, learning_rate=0.001, ctr_weight=1.0, ctcvr_weight=1.0)
    
    # 打印模型摘要
    model.summary()
    
    print("\n3. 准备训练数据...")
    train_inputs = prepare_input_data(X_train, feature_config)
    test_inputs = prepare_input_data(X_test, feature_config)
    
    # 早停策略
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=5,
        restore_best_weights=True
    )
    
    # 学习率衰减
    lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=3,
        min_lr=1e-5
    )
    
    # 创建保存模型的回调
    model_checkpoint = tf.keras.callbacks.ModelCheckpoint(
        filepath='mmoe_model_best.h5',
        monitor='val_loss',
        save_best_only=True,
        save_weights_only=False,
        verbose=1
    )
    
    # 创建TensorBoard回调
    log_dir = "logs/mmoe_" + pd.Timestamp.now().strftime("%Y%m%d-%H%M%S")
    tensorboard_callback = tf.keras.callbacks.TensorBoard(
        log_dir=log_dir,
        histogram_freq=1,
        write_graph=True,
        update_freq='epoch'
    )
    
    print("\n4. 开始训练模型...")
    history = model.fit(
        train_inputs,
        {
            'ctr_output': y_train_ctr,
            'ctcvr_output': y_train_ctcvr
        },
        validation_split=0.2,
        epochs=20,
        batch_size=256,
        callbacks=[early_stopping, lr_scheduler, model_checkpoint, tensorboard_callback],
        verbose=1
    )
    
    print("\n5. 绘制训练历史...")
    plot_training_history(history, output_path="mmoe_training_history.png")
    print(f"训练历史已保存到 'mmoe_training_history.png'")
    
    print("\n6. 评估模型...")
    # 评估训练集
    train_metrics = evaluate_model(model, X_train, y_train_ctr, y_train_ctcvr, feature_config, "训练集")
    
    # 评估测试集
    test_metrics = evaluate_model(model, X_test, y_test_ctr, y_test_ctcvr, feature_config, "测试集")
    
    print("\n7. 比较MMOE和ESMM性能...")
    print("MMOE模型的优势：")
    print("1. 通过门控机制实现了更灵活的任务间知识共享")
    print("2. 每个任务通过不同的门控机制组合专家网络的输出")
    print("3. 能够处理不同相关程度的多任务学习问题")
    print("4. 比简单的共享底层模型有更强的表达能力")
    
    print("\n训练完成！")

if __name__ == "__main__":
    main() 