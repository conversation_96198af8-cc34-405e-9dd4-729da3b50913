# NIO新车购买倾向预测模型代码解析

## 模型架构
核心模型采用EPMMOENet网络结构，具有以下特点：
1. 多模态特征处理 ：模型能够处理多种类型的特征输入，包括：
   - 分类特征（通过StringLookup和Embedding处理）
   - 数值特征（通过Bucket和Dense层处理）
   - 序列特征（通过GRU和时间序列注意力机制处理）
2. 时间序列注意力机制 ：模型使用TimeSeriesAttention层处理序列数据，该层实现了带有时间衰减权重的注意力机制，使得最近的行为获得更高的权重。
3. 特征交叉增强 ：通过CrossLayer实现特征交叉，增强特征间的交互作用。
4. 多任务预测 ：模型设计支持预测未来多个月份的购买可能性，采用累积概率约束。

```mermaid
flowchart TD
    subgraph "输入层"
        input["输入特征"] --> |分类特征| cat["分类特征处理"]
        input --> |数值特征| num["数值特征处理"]
        input --> |序列特征| seq["序列特征处理"]
    end
    
    subgraph "特征处理层"
        cat --> embed["Embedding层"]
        num --> dense["Dense层"]
        seq --> gru["GRU层"]
        gru --> time_att["时间序列注意力层"]
    end
    
    subgraph "特征融合层"
        embed --> concat["特征拼接"]
        dense --> concat
        time_att --> concat
        concat --> cross["特征交叉层"]
        scene["场景特征"] --> scene_weight["场景权重网络"]
        concat --> weighted["加权特征"]
        scene_weight --> weighted
        cross --> final_concat["最终特征拼接"]
        weighted --> final_concat
    end
    
    subgraph "预测层"
        final_concat --> tower["Tower网络"]
        tower --> output["输出层(6个月购买概率)"] 
    end
```

```mermaid
sequenceDiagram
    participant Input as 输入特征
    participant Embed as Embedding处理
    participant Seq as 序列处理
    participant Cross as 特征交叉
    participant Scene as 场景权重
    participant Tower as 预测网络
    participant Output as 输出层
    
    Input->>Embed: 分类特征
    Input->>Embed: 数值特征
    Input->>Seq: 序列特征
    Seq->>Seq: GRU处理
    Seq->>Seq: 时间衰减注意力
    Embed->>Cross: 特征拼接
    Seq->>Cross: 特征拼接
    Input->>Scene: 场景特征
    Scene->>Cross: 特征加权
    Cross->>Cross: 特征交叉增强
    Cross->>Tower: 最终特征
    Tower->>Tower: 多层网络处理
    Tower->>Output: 6个月购买概率
```

## 数据处理流程
1. 数据加载 ：通过DataLoader类从Parquet文件加载数据，支持按日期分区加载和合并额外数据集。
2. 特征预处理 ：DataPreprocessor类负责特征预处理，包括：
   
   - 缺失值填充和异常值处理
   - 分类特征的词表生成和低频类别聚合
   - 序列特征的填充和截断处理
3. 特征构建 ：FeatureBuilder类将预处理后的数据转换为TensorFlow数据集，为模型训练做准备。

## 训练评估流程
1. 模型训练 ：ModelTrainer类负责模型训练，支持：
   
   - 早停和模型检查点保存
   - 混合精度训练选项
   - 自定义损失函数（cumsum_loss和cumsum_mask_loss）
2. 损失函数 ：实现了两种特殊损失函数：
   
   - cumsum_loss：结合二元交叉熵和序列差异损失，确保预测的单调性
   - cumsum_mask_loss：带掩码的损失函数，处理标签不完整的情况

## 项目组织结构
项目采用模块化设计，各组件职责明确：

- src/models/networks/：模型网络结构定义
- src/models/layers/：自定义层实现（CrossLayer和TimeSeriesAttention）
- src/data/：数据加载和预处理
- src/features/：特征工程
- src/training/：训练和损失函数
- src/evaluation/：模型评估
- src/utils/：工具函数
- src/configs/：配置文件管理
这种组织结构使得代码具有良好的可维护性和可扩展性，便于后续进行模型优化和功能扩展。

```
.
├── README.md                  # 项目说明文档
├── data/                      # 数据存储目录
│   └── dataset_nio_new_car_v15/ # 数据集
│       ├── 20240531_随机采样1%.parquet
│       ├── datetime=20240430/
│       └── datetime=20240531/
│
├── src/                       # 源代码目录
│   ├── configs/               # 配置文件目录
│   │   ├── models/            # 模型配置文件
│   │   │   └── sample_20250311_v7-20250311.json
│   │   └── datasets/          # 数据集配置文件
│   │       └── dataset_nio_new_car_v15.json
│   │
│   ├── data/                  # 数据处理相关模块
│   │   ├── __init__.py
│   │   ├── loader.py          # 数据加载器
│   │   └── preprocessor.py    # 数据预处理器
│   │
│   ├── features/              # 特征工程相关模块
│   │   ├── __init__.py
│   │   └── builder.py         # 特征构建器
│   │
│   ├── models/                # 模型相关模块
│   │   ├── __init__.py
│   │   ├── networks/          # 网络模型实现
│   │   │   ├── __init__.py
│   │   │   └── EPMMOENet.py   # 主要模型实现
│   │   └── layers/            # 自定义网络层
│   │       ├── __init__.py
│   │       └── layers.py      # 层实现
│   │
│   ├── training/              # 训练相关模块
│   │   ├── __init__.py
│   │   ├── trainer.py         # 模型训练器
│   │   └── losses.py          # 损失函数实现
│   │
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   ├── config_utils.py    # 配置管理器
│   │
│   ├── evaluation/            # 评估相关模块和结果
│   │   ├── __init__.py
│   │   ├── evaluator.py       # 模型评估器
│   │   ├── 20250317_v1/       # 评估结果目录
│   │   └── 20250317_v2/ # 评估结果目录
│   │
│   └── train.py               # 主训练脚本
│
└── report/          # 报告目录
```