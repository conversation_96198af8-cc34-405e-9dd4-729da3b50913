import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers

@tf.keras.utils.register_keras_serializable()
class TokenAndPositionEmbedding(layers.Layer):
    """Adds token and position embeddings."""
    def __init__(self, maxlen, vocab_size, embed_dim, **kwargs):
        super().__init__(**kwargs)
        self.maxlen = maxlen
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.token_emb = layers.Embedding(input_dim=vocab_size, output_dim=embed_dim, mask_zero=True)
        self.pos_emb = layers.Embedding(input_dim=maxlen, output_dim=embed_dim)

    def call(self, x):
        maxlen = tf.shape(x)[-1]
        positions = tf.range(start=0, limit=maxlen, delta=1)
        positions_embedded = self.pos_emb(positions)
        tokens_embedded = self.token_emb(x)
        # Add position embedding (broadcasts along batch dimension)
        return tokens_embedded + positions_embedded

    def compute_mask(self, inputs, mask=None):
        # Ensure the mask generated by Embedding layer is passed downstream
        return self.token_emb.compute_mask(inputs, mask)

    def get_config(self):
        config = super().get_config()
        config.update({
            "maxlen": self.maxlen,
            "vocab_size": self.vocab_size,
            "embed_dim": self.embed_dim,
        })
        return config

@tf.keras.utils.register_keras_serializable()
class TransformerBlock(layers.Layer):
    """Implements a Transformer block with multi-head self-attention."""
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1, **kwargs):
        super().__init__(**kwargs)
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.rate = rate
        
        self.att = layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim//num_heads)
        self.ffn = keras.Sequential(
            [layers.Dense(ff_dim, activation="relu"), layers.Dense(embed_dim),]
        )
        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)
        self.dropout1 = layers.Dropout(rate)
        self.dropout2 = layers.Dropout(rate)

    def call(self, inputs, training=None, attention_mask=None):
        attn_output = self.att(inputs, inputs, attention_mask=attention_mask)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)
    
    def get_config(self):
        config = super().get_config().copy()
        config.update({
            'embed_dim': self.embed_dim,
            'num_heads': self.num_heads,
            'ff_dim': self.ff_dim,
            'rate': self.rate
        })
        return config

@tf.keras.utils.register_keras_serializable()
class AttentionPooling(layers.Layer):
    """Attention pooling layer to create better sequence representations."""
    def __init__(self, units=128, **kwargs):
        super().__init__(**kwargs)
        self.units = units
        self.attention_w = layers.Dense(units)
        self.attention_b = layers.Dense(1)
        
    def call(self, inputs, mask=None):
        # inputs shape: (batch_size, seq_len, embedding_dim)
        attention = tf.nn.tanh(self.attention_w(inputs))  # (batch_size, seq_len, units)
        attention_logits = self.attention_b(attention)    # (batch_size, seq_len, 1)
        
        # Apply mask if available
        if mask is not None:
            mask = tf.cast(mask, tf.float32)
            mask = tf.expand_dims(mask, axis=-1)  # (batch_size, seq_len, 1)
            attention_logits += (1.0 - mask) * -1e9  # Large negative value for masked positions
        
        # Apply softmax to get attention weights
        attention_weights = tf.nn.softmax(attention_logits, axis=1)  # (batch_size, seq_len, 1)
        
        # Weighted sum of sequence vectors
        context_vector = tf.reduce_sum(inputs * attention_weights, axis=1)  # (batch_size, embedding_dim)
        return context_vector
    
    def get_config(self):
        config = super().get_config()
        config.update({"units": self.units})
        return config

def build_embedding_model(
    maxlen: int,            # Max sequence length (e.g., 64 for core actions)
    action_vocab_size: int, # Vocabulary size for action codes
    day_vocab_size: int,    # Vocabulary size for bucketized action days
    embed_dim: int,         # Embedding dimension
    num_heads: int,         # Number of attention heads
    ff_dim: int,            # Hidden layer size in feed forward network inside transformer
    num_transformer_blocks: int,
    mlp_units: list,        # Units for the final classification MLP head
    dropout_rate: float = 0.1,
    mlp_dropout: float = 0.1,
    include_mlm_head: bool = True # Whether to include the MLM head
):
    """Builds the multi-task Transformer model."""

    # --- Input Layers ---
    action_ids = keras.Input(shape=(maxlen,), dtype="int32", name="action_ids")
    day_ids = keras.Input(shape=(maxlen,), dtype="int32", name="day_ids")
    # Mask for MLM task (indicating which tokens are masked)
    mlm_mask = keras.Input(shape=(maxlen,), dtype="int32", name="mlm_mask") if include_mlm_head else None

    # --- Embedding Layers ---
    # Action Code Embedding (with Positional)
    action_embedding_layer = TokenAndPositionEmbedding(maxlen, action_vocab_size, embed_dim, name="action_token_pos_embedding")
    action_embeddings = action_embedding_layer(action_ids)

    # Day Embedding (assuming already bucketized/discretized, no positional needed here if added above)
    # Using mask_zero=True assumes 0 is the padding value for day_ids
    day_embedding_layer = layers.Embedding(day_vocab_size, embed_dim, mask_zero=True, name="day_embedding")
    day_embeddings = day_embedding_layer(day_ids)

    # Combine Embeddings (e.g., by adding)
    # Ensure mask propagation: action_embeddings carries the mask
    combined_embeddings = action_embeddings + day_embeddings
    embeddings_dropout = layers.Dropout(dropout_rate)(combined_embeddings)

    # Create attention mask from action_ids embedding layer's mask
    # This mask is True for padding positions (value 0 in action_ids)
    # MultiHeadAttention layer accepts boolean mask directly (True means masked)
    attention_mask_bool = action_embedding_layer.compute_mask(action_ids) # Shape (B, S)
    # Expand mask dims for broadcasting: (B, S) -> (B, 1, S)
    # This prevents attending *to* masked key positions from any query position.
    expanded_attention_mask = attention_mask_bool[:, tf.newaxis, :] # Shape (B, 1, S)

    # --- Transformer Blocks ---
    x = embeddings_dropout
    for i in range(num_transformer_blocks):
        # Use GELU activation which is common in modern transformer models
        transformer_block = TransformerBlock(
            embed_dim, 
            num_heads, 
            ff_dim, 
            dropout_rate,
            name=f"transformer_block_{i}"
        )
        # Pass the expanded boolean mask
        x = transformer_block(x, attention_mask=expanded_attention_mask)

    # --- Use Attention Pooling instead of simple GlobalAveragePooling ---
    attention_pooling = AttentionPooling(units=embed_dim//2, name="attention_pooling")
    pooled_output = attention_pooling(x, mask=attention_mask_bool)
    pooled_output_dropout = layers.Dropout(mlp_dropout)(pooled_output)

    # Supervised Head (Binary Classification)
    mlp_hidden = pooled_output_dropout
    for i, units in enumerate(mlp_units):
        mlp_hidden = layers.Dense(
            units, 
            activation="gelu", 
            kernel_regularizer=keras.regularizers.l2(1e-5),
            name=f"mlp_layer_{i}"
        )(mlp_hidden)
        mlp_hidden = layers.Dropout(mlp_dropout)(mlp_hidden)

    supervised_output = layers.Dense(1, activation="sigmoid", name="supervised_output")(mlp_hidden)
    outputs = [supervised_output]

    # MLM Head (Self-Supervised)
    if include_mlm_head:
        # Add layer normalization before MLM head
        x_norm = layers.LayerNormalization(epsilon=1e-6)(x)
        # Use the sequence output 'x' before pooling for MLM
        mlm_output = layers.Dense(action_vocab_size, activation="softmax", name="mlm_output")(x_norm)
        outputs.append(mlm_output)

    # --- Define Model ---
    inputs = [action_ids, day_ids]
    if include_mlm_head:
        inputs.append(mlm_mask)

    model = keras.Model(inputs=inputs, outputs=outputs, name="transformer_embedding_model")

    # --- Separate Model for Embedding Extraction ---
    # We can define another model that outputs the pooled representation
    embedding_extractor = keras.Model(
        inputs=[action_ids, day_ids], # Only need sequence inputs
        outputs=pooled_output,        # Output the pooled representation before final dropout/MLP
        name="embedding_extractor"
    )

    return model, embedding_extractor

def build_enhanced_embedding_model(
    maxlen,
    action_vocab_size,
    day_vocab_size,
    additional_vocab_sizes=None,
    static_features_dim=0,
    embed_dim=256,
    num_heads=8,
    ff_dim=512,
    num_transformer_blocks=3,
    mlp_units=[256, 128, 64],
    dropout_rate=0.2,
    mlp_dropout=0.3,
    class_weight=None
):
    """
    构建增强版用户embedding模型，支持多序列特征和静态特征。
    
    Args:
        maxlen: 序列最大长度
        action_vocab_size: 行为词汇表大小
        day_vocab_size: 天数词汇表大小
        additional_vocab_sizes: 额外序列特征的词汇表大小 {特征名: 词汇表大小}
        static_features_dim: 静态特征维度
        embed_dim: 嵌入向量维度
        num_heads: 注意力头数
        ff_dim: 前馈网络维度
        num_transformer_blocks: Transformer块数量
        mlp_units: MLP层单元数量列表
        dropout_rate: Dropout率
        mlp_dropout: MLP层Dropout率
        class_weight: 类别权重
        
    Returns:
        model: 完整模型
        extractor: 仅包含嵌入部分的模型，用于特征提取
    """
    # 输入层
    action_input = keras.Input(shape=(maxlen,), dtype="int32", name="action_ids")
    day_input = keras.Input(shape=(maxlen,), dtype="int32", name="day_ids")
    
    # 创建输入字典
    inputs = {
        "action_ids": action_input,
        "day_ids": day_input
    }
    
    # 添加额外序列特征输入层
    additional_inputs = {}
    if additional_vocab_sizes:
        for seq_name, vocab_size in additional_vocab_sizes.items():
            input_name = f"{seq_name}_ids"
            additional_inputs[seq_name] = keras.Input(shape=(maxlen,), dtype="int32", name=input_name)
            inputs[input_name] = additional_inputs[seq_name]
    
    # 添加静态特征输入
    static_input = None
    if static_features_dim > 0:
        static_input = keras.Input(shape=(static_features_dim,), dtype="float32", name="static_features")
        inputs["static_features"] = static_input
    
    # 行为序列嵌入
    action_embedding_layer = TokenAndPositionEmbedding(
        maxlen, action_vocab_size, embed_dim
    )
    action_embedding = action_embedding_layer(action_input)
    
    # 日期序列嵌入
    day_embedding_layer = layers.Embedding(
        input_dim=day_vocab_size, output_dim=embed_dim, mask_zero=True
    )
    day_embedding = day_embedding_layer(day_input)
    
    # 合并行为和日期嵌入 (加法融合)
    x = layers.Add()([action_embedding, day_embedding])
    
    # 处理额外序列特征
    if additional_vocab_sizes:
        additional_embeddings = []
        for seq_name, vocab_size in additional_vocab_sizes.items():
            embed_layer = layers.Embedding(
                input_dim=vocab_size, output_dim=embed_dim, mask_zero=True
            )
            additional_embed = embed_layer(additional_inputs[seq_name])
            additional_embeddings.append(additional_embed)
        
        # 如果有额外序列特征，使用注意力机制融合
        if additional_embeddings:
            # 注意力加权融合
            all_embeddings = [x] + additional_embeddings
            # 添加序列特定注意力，使特征更有区分性
            attention_weights = layers.Dense(len(all_embeddings), activation="softmax")(
                layers.GlobalAveragePooling1D()(x)
            )
            # 展开权重维度以便与各个嵌入相乘
            # attention_weights = layers.Lambda(lambda t: tf.expand_dims(t, axis=1))(attention_weights)
            # attention_weights = layers.Lambda(lambda t: tf.expand_dims(t, axis=1))(attention_weights)
            
            # 将所有嵌入堆叠成一个张量 shape=(batch, seq, features, embed_dim)
            # Note: Lambda layers cannot directly stack a list of tensors passed during model definition.
            # Instead, we concatenate and then reshape, or perform the weighted sum element-wise.
            # Let's try element-wise weighted sum:
            
            # attention_weights has shape (B, num_embeddings)
            weighted_embeddings = []
            for i, embedding in enumerate(all_embeddings):
                # Select the weight for this embedding (shape: B, 1)
                # weight = layers.Lambda(lambda t: t[:, :, i:i+1, :])(attention_weights) # Shape: (B, 1, 1, 1) - Old logic
                # weight = layers.Lambda(lambda t: tf.squeeze(t, axis=2))(weight) # Shape: (B, 1, 1) - Old logic
                
                # New logic:
                weight = layers.Lambda(lambda t: t[:, i:i+1], name=f'select_weight_{i}')(attention_weights) # Shape: (B, 1)
                # Reshape weight to (B, 1, 1) for broadcasting with embedding (B, maxlen, embed_dim)
                weight_reshaped = layers.Lambda(lambda t: tf.expand_dims(t, axis=-1), name=f'reshape_weight_{i}')(weight) # Shape: (B, 1, 1)
                
                weighted_embeddings.append(layers.Multiply(name=f'apply_weight_{i}')([embedding, weight_reshaped]))

            # 应用注意力权重 (Summing the weighted embeddings)
            x = layers.Add(name='sum_weighted_embeddings')(weighted_embeddings)
    
    # 应用Transformer块
    transformer_blocks = []
    for i in range(num_transformer_blocks):
        transformer_block = TransformerBlock(
            embed_dim, num_heads, ff_dim, dropout_rate
        )
        x = transformer_block(x)
        transformer_blocks.append(transformer_block)
    
    # 全局特征表示
    x = layers.GlobalAveragePooling1D()(x)
    
    # 添加静态特征
    if static_input is not None:
        # 处理静态特征
        static_features = layers.BatchNormalization()(static_input)
        static_features = layers.Dense(embed_dim, activation="relu")(static_features)
        static_features = layers.Dropout(mlp_dropout)(static_features)
        
        # 特征融合 - 使用门控机制
        gate = layers.Dense(embed_dim, activation="sigmoid")(
            layers.Concatenate()([x, static_features])
        )
        x = gate * x + (1 - gate) * static_features
    
    # 嵌入向量
    embedding_vector = layers.Dense(embed_dim, name="embedding_vector")(x)
    
    # 创建仅包含嵌入部分的模型，用于特征提取
    extractor = keras.Model(inputs=inputs, outputs=embedding_vector, name="embedding_extractor")
    
    # 添加MLP层进行分类
    x = embedding_vector
    for dim in mlp_units:
        x = layers.Dense(dim, activation="relu")(x)
        x = layers.Dropout(mlp_dropout)(x)
    
    # 输出层
    output = layers.Dense(1, activation="sigmoid", name="purchase_prediction")(x)
    
    # 创建完整模型
    model = keras.Model(inputs=inputs, outputs=output, name="enhanced_user_embedding_model")
    
    return model, extractor

@tf.keras.utils.register_keras_serializable()
class SpatialDropout1D(layers.Dropout):
    """空间Dropout，在特征维度上进行丢弃，而不是独立丢弃每个元素"""
    
    def __init__(self, rate, **kwargs):
        super(SpatialDropout1D, self).__init__(rate, **kwargs)
        self.input_spec = layers.InputSpec(ndim=3)
    
    def _get_noise_shape(self, inputs):
        input_shape = tf.shape(inputs)
        return tf.concat([input_shape[0:1], tf.ones([1], dtype=tf.int32), input_shape[2:]], axis=0)
    
    def get_config(self):
        return super(SpatialDropout1D, self).get_config()

if __name__ == '__main__':
    # Example Usage (for testing the build function)
    MAXLEN = 64
    ACTION_VOCAB_SIZE = 1000  # Placeholder
    DAY_VOCAB_SIZE = 200      # Placeholder (e.g., 180 days + padding + OOV)
    EMBED_DIM = 128
    NUM_HEADS = 4
    FF_DIM = 128
    NUM_TRANSFORMER_BLOCKS = 2
    MLP_UNITS = [64]
    DROPOUT_RATE = 0.1
    MLP_DROPOUT = 0.1

    model, extractor = build_embedding_model(
        maxlen=MAXLEN,
        action_vocab_size=ACTION_VOCAB_SIZE,
        day_vocab_size=DAY_VOCAB_SIZE,
        embed_dim=EMBED_DIM,
        num_heads=NUM_HEADS,
        ff_dim=FF_DIM,
        num_transformer_blocks=NUM_TRANSFORMER_BLOCKS,
        mlp_units=MLP_UNITS,
        dropout_rate=DROPOUT_RATE,
        mlp_dropout=MLP_DROPOUT,
        include_mlm_head=True
    )

    model.summary()
    print("-" * 20)
    extractor.summary()

    # Test model prediction shapes
    batch_size = 4
    dummy_action_ids = tf.random.uniform((batch_size, MAXLEN), maxval=ACTION_VOCAB_SIZE, dtype=tf.int32)
    dummy_day_ids = tf.random.uniform((batch_size, MAXLEN), maxval=DAY_VOCAB_SIZE, dtype=tf.int32)
    dummy_mlm_mask = tf.random.uniform((batch_size, MAXLEN), maxval=2, dtype=tf.int32) # 0 or 1

    outputs = model([dummy_action_ids, dummy_day_ids, dummy_mlm_mask])
    print("\nModel output shapes:")
    print(f"  Supervised: {outputs[0].shape}")
    print(f"  MLM: {outputs[1].shape}")

    embedding_output = extractor([dummy_action_ids, dummy_day_ids])
    print(f"\nExtractor output shape: {embedding_output.shape}") # Should be (batch_size, embed_dim) 