#!/bin/bash

# 设置基本参数
MODEL_CODE="sample_20250311_v7-20250311"
DATASET_CODE="dataset_nio_new_car_v15"
EVALUATE_FILE="20240531_随机采样1%.parquet"
DATA_DIR="data"
EPOCHS=50
PATIENCE=5
BATCH_SIZE=4096
BASE_OUTPUT_DIR="src/evaluation"

# 定义优化运行名称
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OPTIMIZED_RUN_NAME="smote_enn_${TIMESTAMP}"

# 基线模型路径 (使用已有的模型)
BASELINE_DIR="${BASE_OUTPUT_DIR}/20250330_transformer_v1/evaluation_20250331_080342"

# 检测操作系统类型
PLATFORM=$(uname)

echo "=== 运行SMOTE-ENN优化模型 ==="
echo "模型代码: ${MODEL_CODE}"
echo "数据集: ${DATASET_CODE}"
echo "运行名称: ${OPTIMIZED_RUN_NAME}"

# 运行训练命令，直接在训练阶段使用SMOTE-ENN
python src/train.py \
  --model_code=${MODEL_CODE} \
  --run_name=${OPTIMIZED_RUN_NAME} \
  --dataset_code=${DATASET_CODE} \
  --evaluate_file=${EVALUATE_FILE} \
  --data_dir=${DATA_DIR} \
  --epochs=${EPOCHS} \
  --patience=${PATIENCE} \
  --batch_size=${BATCH_SIZE} \
  --loss_type=asymmetric_focal \
  --pos_weight=30.0 \
  --use_enhanced_features \
  --resampling_strategy=optimized_smote_enn \
  --smote_k=3 \
  --enn_k=3 \
  --smote_ratio=0.5

# 检查训练完成状态
if [ $? -ne 0 ]; then
  echo "训练失败！"
  exit 1
fi

# 查找最新的评估目录
if [[ "$PLATFORM" == "Darwin" ]]; then
  # macOS
  OPTIMIZED_DIR=$(find ${BASE_OUTPUT_DIR}/${OPTIMIZED_RUN_NAME} -type d -name "evaluation_*" | sort -r | head -n 1)
else
  # Linux
  OPTIMIZED_DIR=$(find ${BASE_OUTPUT_DIR}/${OPTIMIZED_RUN_NAME} -type d -name "evaluation_*" -printf "%T+ %p\n" | sort -r | head -n 1 | awk '{print $2}')
fi

echo "基线模型目录: ${BASELINE_DIR}"
echo "优化模型目录: ${OPTIMIZED_DIR}"

# 运行模型比较
python scripts/evaluation/compare_models.py \
  --baseline_dir=${BASELINE_DIR} \
  --improved_dir=${OPTIMIZED_DIR} \
  --output_dir="${BASE_OUTPUT_DIR}/comparisons/comparison_${OPTIMIZED_RUN_NAME}"

echo "比较结果已保存到 ${BASE_OUTPUT_DIR}/comparisons/comparison_${OPTIMIZED_RUN_NAME}/"
echo "SMOTE-ENN优化测试完成！" 