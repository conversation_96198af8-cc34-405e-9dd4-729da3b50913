#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
蔚来数据加载器模块 - 专门处理蔚来转化率预测数据
"""
import pandas as pd
import numpy as np
import logging
from typing import Tuple, List, Optional
from sklearn.model_selection import train_test_split

logger = logging.getLogger(__name__)

class NioDataLoader:
    """蔚来数据加载器 - 专门处理蔚来转化率预测数据"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.default_file = "20240531_随机采样1%.parquet"
        self.target_column = "purchase_days_nio_new_car_total"
    
    def load_data(self, file_name: Optional[str] = None) -> pd.DataFrame:
        """加载蔚来数据"""
        file_name = file_name or self.default_file
        file_path = f"{self.data_dir}/dataset_nio_new_car_v15/{file_name}"
        
        logger.info(f"加载数据文件: {file_path}")
        
        try:
            df = pd.read_parquet(file_path)
            logger.info(f"数据加载成功: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def prepare_features(self, df: pd.DataFrame, feature_count: int = 150) -> Tuple[np.ndarray, List[str]]:
        """准备特征矩阵"""
        logger.info("准备特征矩阵...")
        
        # 选择数值特征，排除ID和时间列
        numeric_cols = [col for col in df.columns 
                       if df[col].dtype in ['int64', 'float64'] 
                       and col not in ['user_id', 'datetime', self.target_column]]
        
        # 限制特征数量
        selected_features = numeric_cols[:feature_count]
        logger.info(f"选择特征数: {len(selected_features)}")
        
        # 准备特征矩阵，填充缺失值
        X = df[selected_features].fillna(0).values
        
        logger.info(f"特征矩阵形状: {X.shape}")
        return X, selected_features
    
    def prepare_labels(self, df: pd.DataFrame) -> np.ndarray:
        """准备标签"""
        logger.info("准备标签...")
        
        if self.target_column in df.columns:
            target_series = df[self.target_column]
            # 转换为数值，处理可能的字符串值
            target_numeric = pd.to_numeric(target_series, errors='coerce').fillna(0)
            y = (target_numeric > 0).astype(int).values
            
            logger.info(f"标签统计: 负样本={np.sum(y==0)}, 正样本={np.sum(y==1)}")
            logger.info(f"正样本比例: {y.mean():.4f}")
        else:
            logger.error(f"目标列 {self.target_column} 不存在")
            raise ValueError(f"Target column {self.target_column} not found")
        
        return y
    
    def load_and_prepare(self, file_name: Optional[str] = None, 
                        feature_count: int = 150) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """一站式数据加载和准备"""
        # 加载数据
        df = self.load_data(file_name)
        
        # 准备特征和标签
        X, feature_names = self.prepare_features(df, feature_count)
        y = self.prepare_labels(df)
        
        return X, y, feature_names
    
    def split_data(self, X: np.ndarray, y: np.ndarray, 
                  test_size: float = 0.2, random_state: int = 42) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """数据分割"""
        logger.info(f"数据分割: 测试集比例={test_size}")
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
        return X_train, X_test, y_train, y_test
