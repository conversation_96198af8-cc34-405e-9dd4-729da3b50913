#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化18: 基于早期EPMMOENet经验的架构优化
融合早期模型的成功经验：多模态处理、特征交互、时间建模
基线: AUC 0.8219 (SMOTE + 集成)
目标: 学习早期模型的架构设计，实现更高效简洁的版本
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler, LabelEncoder, KBinsDiscretizer
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

class EarlyInspiredFeatureEngineering:
    """基于早期模型经验的特征工程"""
    
    def __init__(self):
        self.numerical_features = []
        self.categorical_features = []
        self.bucket_features = []
        self.label_encoders = {}
        self.discretizers = {}
        
    def engineer_features(self, df):
        """基于早期模型经验的特征工程"""
        logger.info("基于早期模型经验进行特征工程...")
        
        # 1. 基础数值特征选择
        numeric_cols = [col for col in df.columns 
                       if df[col].dtype in ['int64', 'float64'] 
                       and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
        
        # 选择更多基础特征
        base_features = numeric_cols[:200]  # 增加基础特征数量
        df_features = df[base_features].fillna(0)
        
        # 2. 多时间窗口特征 (学习早期模型的1d,7d,30d,60d,90d,180d)
        time_windows = ['1d', '7d', '30d', '90d']
        
        for window in time_windows:
            window_cols = [col for col in base_features if f'{window}_cnt' in col]
            if window_cols:
                # 时间窗口聚合特征
                df_features[f'total_actions_{window}'] = df_features[window_cols].sum(axis=1)
                df_features[f'action_diversity_{window}'] = (df_features[window_cols] > 0).sum(axis=1)
                df_features[f'action_intensity_{window}'] = df_features[window_cols].max(axis=1)
                df_features[f'action_mean_{window}'] = df_features[window_cols].mean(axis=1)
                df_features[f'action_std_{window}'] = df_features[window_cols].std(axis=1).fillna(0)
        
        # 3. DSLA特征 (Days Since Last Action) - 时间衰减建模
        action_cols = [col for col in base_features if 'action' in col and 'cnt' in col]
        if action_cols:
            for col in action_cols[:15]:  # 对前15个行为特征计算DSLA
                # 模拟时间衰减权重
                df_features[f'{col}_dsla'] = np.where(
                    df_features[col] > 0,
                    np.exp(-0.05 * np.log1p(df_features[col])),  # 时间衰减
                    0.0
                )
        
        # 4. 分桶特征 (学习早期模型的bin_boundaries)
        high_value_cols = [col for col in df_features.columns 
                          if df_features[col].max() > 5 and df_features[col].std() > 0.1]
        
        logger.info(f"对{len(high_value_cols[:20])}个特征进行分桶处理...")
        
        for col in high_value_cols[:20]:  # 对前20个特征进行分桶
            try:
                # 使用分位数分桶
                discretizer = KBinsDiscretizer(n_bins=5, encode='ordinal', strategy='quantile')
                col_values = df_features[col].values.reshape(-1, 1)
                
                # 分桶特征
                df_features[f'{col}_bucket'] = discretizer.fit_transform(col_values).flatten()
                self.discretizers[col] = discretizer
                
                # 分桶指示特征
                df_features[f'{col}_is_zero'] = (df_features[col] == 0).astype(int)
                df_features[f'{col}_is_high'] = (df_features[col] > df_features[col].quantile(0.9)).astype(int)
                
            except Exception as e:
                logger.warning(f"分桶处理失败 {col}: {e}")
                continue
        
        # 5. 类别特征工程 (模拟早期模型的StringLookup特征)
        # 基于数值特征创建类别特征
        if 'total_actions_7d' in df_features.columns:
            # 用户活跃度类别
            df_features['user_activity_level'] = pd.cut(
                df_features['total_actions_7d'], 
                bins=[-1, 0, 1, 5, 20, float('inf')], 
                labels=['inactive', 'low', 'medium', 'high', 'very_high']
            ).astype(str)
            
        if 'total_actions_30d' in df_features.columns:
            # 用户参与度类别
            df_features['user_engagement_level'] = pd.cut(
                df_features['total_actions_30d'], 
                bins=[-1, 0, 2, 10, 50, float('inf')], 
                labels=['none', 'minimal', 'moderate', 'active', 'super_active']
            ).astype(str)
        
        # 6. 行为趋势特征 (时间窗口间的变化)
        if 'total_actions_1d' in df_features.columns and 'total_actions_7d' in df_features.columns:
            df_features['trend_7d_1d'] = df_features['total_actions_7d'] - df_features['total_actions_1d']
            df_features['ratio_7d_1d'] = df_features['total_actions_7d'] / (df_features['total_actions_1d'] + 1)
            df_features['momentum_7d_1d'] = df_features['trend_7d_1d'] / (df_features['total_actions_7d'] + 1)
        
        if 'total_actions_30d' in df_features.columns and 'total_actions_7d' in df_features.columns:
            df_features['trend_30d_7d'] = df_features['total_actions_30d'] - df_features['total_actions_7d']
            df_features['ratio_30d_7d'] = df_features['total_actions_30d'] / (df_features['total_actions_7d'] + 1)
        
        # 7. 核心业务特征
        core_action_cols = [col for col in base_features if 'user_core' in col and 'cnt' in col]
        if core_action_cols:
            df_features['total_core_actions'] = df_features[core_action_cols].sum(axis=1)
            df_features['core_action_diversity'] = (df_features[core_action_cols] > 0).sum(axis=1)
            df_features['core_action_concentration'] = df_features[core_action_cols].max(axis=1) / (df_features['total_core_actions'] + 1)
        
        # 8. 购车相关特征
        car_keywords = ['book_td', 'exp_td', 'lock_ncar', 'pay_ncar', 'order', 'purchase']
        car_action_cols = [col for col in base_features 
                          if any(keyword in col for keyword in car_keywords)]
        
        if car_action_cols:
            df_features['total_car_actions'] = df_features[car_action_cols].sum(axis=1)
            df_features['car_action_diversity'] = (df_features[car_action_cols] > 0).sum(axis=1)
            df_features['car_action_intensity'] = df_features[car_action_cols].max(axis=1)
        
        # 9. 交互特征 (模拟早期模型的特征交互)
        if 'total_core_actions' in df_features.columns and 'total_car_actions' in df_features.columns:
            df_features['core_car_interaction'] = df_features['total_core_actions'] * df_features['total_car_actions']
            df_features['core_car_ratio'] = df_features['total_core_actions'] / (df_features['total_car_actions'] + 1)
            df_features['purchase_intent_score'] = (
                df_features['total_car_actions'] * 2 + 
                df_features['total_core_actions'] * 1 + 
                df_features['car_action_diversity'] * 3
            )
        
        # 10. 统计特征
        numerical_cols = [col for col in df_features.columns if df_features[col].dtype in ['int64', 'float64']]
        df_features['feature_sum'] = df_features[numerical_cols].sum(axis=1)
        df_features['feature_mean'] = df_features[numerical_cols].mean(axis=1)
        df_features['feature_std'] = df_features[numerical_cols].std(axis=1).fillna(0)
        df_features['feature_max'] = df_features[numerical_cols].max(axis=1)
        df_features['nonzero_feature_count'] = (df_features[numerical_cols] > 0).sum(axis=1)
        
        # 分离数值特征和类别特征
        self.numerical_features = [col for col in df_features.columns if df_features[col].dtype in ['int64', 'float64']]
        self.categorical_features = [col for col in df_features.columns if df_features[col].dtype == 'object']
        
        logger.info(f"特征工程完成: 数值特征{len(self.numerical_features)}个, 类别特征{len(self.categorical_features)}个")
        
        return df_features
    
    def prepare_model_inputs(self, df_features):
        """准备模型输入"""
        # 处理数值特征
        X_numerical = df_features[self.numerical_features].values
        
        # 处理类别特征
        X_categorical = []
        for col in self.categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                encoded = self.label_encoders[col].fit_transform(df_features[col].fillna('unknown'))
            else:
                encoded = self.label_encoders[col].transform(df_features[col].fillna('unknown'))
            X_categorical.append(encoded)
        
        if X_categorical:
            X_categorical = np.column_stack(X_categorical)
            return X_numerical, X_categorical
        else:
            return X_numerical, None

class EarlyInspiredModel:
    """基于早期模型经验的简化架构"""
    
    def __init__(self, numerical_dim, categorical_dim=None, categorical_vocab_sizes=None):
        self.numerical_dim = numerical_dim
        self.categorical_dim = categorical_dim
        self.categorical_vocab_sizes = categorical_vocab_sizes or []
        
    def create_model(self):
        """创建基于早期模型经验的简化架构"""
        
        # 数值特征输入
        numerical_input = tf.keras.layers.Input(shape=(self.numerical_dim,), name='numerical_input')
        
        # 数值特征处理 (模拟早期模型的Dense处理)
        numerical_features = tf.keras.layers.Dense(256, activation='relu')(numerical_input)
        numerical_features = tf.keras.layers.BatchNormalization()(numerical_features)
        numerical_features = tf.keras.layers.Dropout(0.3)(numerical_features)
        numerical_features = tf.keras.layers.Dense(128, activation='relu')(numerical_features)
        numerical_features = tf.keras.layers.BatchNormalization()(numerical_features)
        numerical_features = tf.keras.layers.Dropout(0.2)(numerical_features)
        
        # 如果有类别特征，添加类别特征处理
        if self.categorical_dim and self.categorical_vocab_sizes:
            categorical_input = tf.keras.layers.Input(shape=(self.categorical_dim,), name='categorical_input')
            
            # 类别特征嵌入 (模拟早期模型的StringLookup + Embedding)
            categorical_embeddings = []
            for i, vocab_size in enumerate(self.categorical_vocab_sizes):
                embedding = tf.keras.layers.Embedding(
                    vocab_size + 1, 8, input_length=1
                )(categorical_input[:, i:i+1])
                embedding = tf.keras.layers.Flatten()(embedding)
                categorical_embeddings.append(embedding)
            
            if len(categorical_embeddings) > 1:
                categorical_features = tf.keras.layers.Concatenate()(categorical_embeddings)
            else:
                categorical_features = categorical_embeddings[0]
            
            # 合并数值和类别特征
            combined_features = tf.keras.layers.Concatenate()([numerical_features, categorical_features])
            inputs = [numerical_input, categorical_input]
        else:
            combined_features = numerical_features
            inputs = numerical_input
        
        # 特征交互层 (模拟早期模型的CrossLayer)
        cross_features = tf.keras.layers.Dense(64, activation='relu')(combined_features)
        cross_features = tf.keras.layers.BatchNormalization()(cross_features)
        cross_features = tf.keras.layers.Dropout(0.2)(cross_features)
        
        # 残差连接 (模拟早期模型的特征交互)
        enhanced_features = tf.keras.layers.Concatenate()([combined_features, cross_features])
        
        # 场景自适应权重 (简化版)
        attention_weights = tf.keras.layers.Dense(enhanced_features.shape[-1], activation='sigmoid')(enhanced_features)
        adaptive_features = tf.keras.layers.Multiply()([enhanced_features, attention_weights])
        
        # 预测塔 (模拟早期模型的Tower_layers)
        tower = tf.keras.layers.BatchNormalization()(adaptive_features)
        tower = tf.keras.layers.Dropout(0.3)(tower)
        tower = tf.keras.layers.Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))(tower)
        tower = tf.keras.layers.BatchNormalization()(tower)
        tower = tf.keras.layers.Dropout(0.3)(tower)
        tower = tf.keras.layers.Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))(tower)
        tower = tf.keras.layers.BatchNormalization()(tower)
        tower = tf.keras.layers.Dropout(0.2)(tower)
        tower = tf.keras.layers.Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))(tower)
        tower = tf.keras.layers.Dropout(0.2)(tower)
        
        # 输出层
        output = tf.keras.layers.Dense(1, activation='sigmoid')(tower)
        
        model = tf.keras.Model(inputs=inputs, outputs=output)
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['auc']
        )
        
        return model

def train_and_evaluate():
    """训练和评估基于早期模型经验的优化版本"""
    # 1. 加载数据
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 基于早期模型经验的特征工程
    feature_engineer = EarlyInspiredFeatureEngineering()
    df_features = feature_engineer.engineer_features(df)
    
    # 3. 准备模型输入
    X_numerical, X_categorical = feature_engineer.prepare_model_inputs(df_features)
    y = data_loader.prepare_labels(df)
    
    # 4. 数据分割
    if X_categorical is not None:
        X_train_num, X_test_num, X_train_cat, X_test_cat, y_train, y_test = train_test_split(
            X_numerical, X_categorical, y, test_size=0.2, random_state=42, stratify=y
        )
    else:
        X_train_num, X_test_num, y_train, y_test = train_test_split(
            X_numerical, y, test_size=0.2, random_state=42, stratify=y
        )
        X_train_cat, X_test_cat = None, None
    
    logger.info(f"训练集: {X_train_num.shape}, 测试集: {X_test_num.shape}")
    
    # 5. 特征标准化
    scaler = StandardScaler()
    X_train_num_scaled = scaler.fit_transform(X_train_num)
    X_test_num_scaled = scaler.transform(X_test_num)
    
    # 6. 应用SMOTE重采样
    logger.info("应用SMOTE重采样...")
    if X_train_cat is not None:
        X_train_combined = np.column_stack([X_train_num_scaled, X_train_cat])
        smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
        X_train_smote, y_train_smote = smote.fit_resample(X_train_combined, y_train)
        
        # 分离数值和类别特征
        X_train_num_smote = X_train_smote[:, :X_train_num_scaled.shape[1]]
        X_train_cat_smote = X_train_smote[:, X_train_num_scaled.shape[1]:].astype(int)
    else:
        smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
        X_train_num_smote, y_train_smote = smote.fit_resample(X_train_num_scaled, y_train)
        X_train_cat_smote = None
    
    logger.info(f"SMOTE重采样: {len(y_train)} -> {len(y_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
    
    # 7. 创建模型
    categorical_vocab_sizes = []
    if X_categorical is not None:
        for i in range(X_categorical.shape[1]):
            categorical_vocab_sizes.append(int(X_categorical[:, i].max()))
    
    model_creator = EarlyInspiredModel(
        numerical_dim=X_train_num_scaled.shape[1],
        categorical_dim=X_categorical.shape[1] if X_categorical is not None else None,
        categorical_vocab_sizes=categorical_vocab_sizes if categorical_vocab_sizes else None
    )
    
    model = model_creator.create_model()
    model.summary()
    
    # 8. 训练参数
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=7, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5, min_lr=1e-6)
    ]
    
    # 9. 准备训练数据
    if X_train_cat_smote is not None:
        train_inputs = [X_train_num_smote, X_train_cat_smote]
        test_inputs = [X_test_num_scaled, X_test_cat]
    else:
        train_inputs = X_train_num_smote
        test_inputs = X_test_num_scaled
    
    # 10. 训练模型
    logger.info("=== 训练基于早期模型经验的优化版本 ===")
    
    model.fit(
        train_inputs, y_train_smote,
        validation_data=(test_inputs, y_test),
        epochs=15, batch_size=512,
        callbacks=callbacks,
        verbose=1
    )
    
    # 11. 预测和评估
    pred = model.predict(test_inputs, verbose=0)
    
    # 详细评估
    auc_score = roc_auc_score(y_test, pred)
    pr_auc = calculate_pr_auc(y_test, pred)
    precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
    
    logger.info(f"=== 基于早期模型经验的优化结果 ===")
    logger.info(f"AUC: {auc_score:.4f}")
    logger.info(f"PR-AUC: {pr_auc:.4f}")
    logger.info(f"P@840: {precision_at_840:.4f}")
    logger.info(f"R@840: {recall_at_840:.4f}")
    
    # 与基线和早期模型对比
    baseline_auc = 0.8219
    early_model_auc = 0.8756
    early_model_pr_auc = 0.4227
    early_model_recall = 0.9241
    
    auc_vs_baseline = auc_score - baseline_auc
    auc_vs_early = auc_score - early_model_auc
    pr_auc_vs_early = pr_auc - early_model_pr_auc
    recall_vs_early = recall_at_840 - early_model_recall
    
    logger.info(f"相比基线: AUC {auc_vs_baseline:+.4f}")
    logger.info(f"相比早期模型: AUC {auc_vs_early:+.4f}, PR-AUC {pr_auc_vs_early:+.4f}, Recall@840 {recall_vs_early:+.4f}")
    
    return {
        'optimization': 'early_inspired_architecture',
        'auc': float(auc_score),
        'pr_auc': float(pr_auc),
        'precision_at_840': float(precision_at_840),
        'recall_at_840': float(recall_at_840),
        'numerical_features': len(feature_engineer.numerical_features),
        'categorical_features': len(feature_engineer.categorical_features),
        'total_features': len(feature_engineer.numerical_features) + len(feature_engineer.categorical_features),
        'baseline_auc': baseline_auc,
        'early_model_auc': early_model_auc,
        'early_model_pr_auc': early_model_pr_auc,
        'early_model_recall': early_model_recall,
        'auc_vs_baseline': float(auc_vs_baseline),
        'auc_vs_early': float(auc_vs_early),
        'pr_auc_vs_early': float(pr_auc_vs_early),
        'recall_vs_early': float(recall_vs_early),
        'effective': bool(auc_vs_baseline > 0.01 or (auc_vs_early > -0.02 and pr_auc > 0.1))
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_early_inspired_arch"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化18完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"AUC: {result['auc']:.4f}")
    print(f"PR-AUC: {result['pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: 数值{result['numerical_features']}个, 类别{result['categorical_features']}个, 总计{result['total_features']}个")
    print(f"相比基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"相比早期模型AUC: {result['auc_vs_early']:+.4f}")
    print(f"相比早期模型PR-AUC: {result['pr_auc_vs_early']:+.4f}")
    print(f"相比早期模型Recall@840: {result['recall_vs_early']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
