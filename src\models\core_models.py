#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
蔚来转化率预测 - 核心模型定义
整合所有有效的模型架构
"""
import tensorflow as tf
from imblearn.over_sampling import SMOTE
import numpy as np
import logging

logger = logging.getLogger(__name__)

class ModelFactory:
    """模型工厂类 - 创建各种优化模型"""
    
    @staticmethod
    def create_deep_model(input_dim, model_name="deep_model"):
        """创建深度网络模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,), name=f'{model_name}_dense1'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn1'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout1'),
            
            tf.keras.layers.Dense(128, activation='relu', name=f'{model_name}_dense2'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn2'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout2'),
            
            tf.keras.layers.Dense(64, activation='relu', name=f'{model_name}_dense3'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn3'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout3'),
            
            tf.keras.layers.Dense(32, activation='relu', name=f'{model_name}_dense4'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn4'),
            tf.keras.layers.Dropout(0.2, name=f'{model_name}_dropout4'),
            
            tf.keras.layers.Dense(16, activation='relu', name=f'{model_name}_dense5'),
            tf.keras.layers.Dropout(0.2, name=f'{model_name}_dropout5'),
            
            tf.keras.layers.Dense(1, activation='sigmoid', name=f'{model_name}_output')
        ], name=model_name)
        
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
        return model
    
    @staticmethod
    def create_wide_model(input_dim, model_name="wide_model"):
        """创建宽网络模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(512, activation='relu', input_shape=(input_dim,), name=f'{model_name}_dense1'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn1'),
            tf.keras.layers.Dropout(0.4, name=f'{model_name}_dropout1'),
            
            tf.keras.layers.Dense(256, activation='relu', name=f'{model_name}_dense2'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn2'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout2'),
            
            tf.keras.layers.Dense(64, activation='relu', name=f'{model_name}_dense3'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout3'),
            
            tf.keras.layers.Dense(1, activation='sigmoid', name=f'{model_name}_output')
        ], name=model_name)
        
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
        return model
    
    @staticmethod
    def create_balanced_model(input_dim, model_name="balanced_model"):
        """创建平衡网络模型"""
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(input_dim,), name=f'{model_name}_dense1'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn1'),
            tf.keras.layers.Dropout(0.4, name=f'{model_name}_dropout1'),
            
            tf.keras.layers.Dense(64, activation='relu', name=f'{model_name}_dense2'),
            tf.keras.layers.BatchNormalization(name=f'{model_name}_bn2'),
            tf.keras.layers.Dropout(0.4, name=f'{model_name}_dropout2'),
            
            tf.keras.layers.Dense(32, activation='relu', name=f'{model_name}_dense3'),
            tf.keras.layers.Dropout(0.3, name=f'{model_name}_dropout3'),
            
            tf.keras.layers.Dense(16, activation='relu', name=f'{model_name}_dense4'),
            tf.keras.layers.Dropout(0.2, name=f'{model_name}_dropout4'),
            
            tf.keras.layers.Dense(1, activation='sigmoid', name=f'{model_name}_output')
        ], name=model_name)
        
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['auc'])
        return model

class EnsembleModel:
    """集成模型类"""
    
    def __init__(self, input_dim, ensemble_size=3):
        self.input_dim = input_dim
        self.ensemble_size = ensemble_size
        self.models = []
        self.model_creators = [
            ModelFactory.create_deep_model,
            ModelFactory.create_wide_model,
            ModelFactory.create_balanced_model
        ]
    
    def build_models(self):
        """构建集成模型"""
        logger.info(f"构建{self.ensemble_size}个集成模型...")
        
        for i in range(self.ensemble_size):
            creator = self.model_creators[i % len(self.model_creators)]
            model = creator(self.input_dim, f"ensemble_model_{i+1}")
            self.models.append(model)
            logger.info(f"模型{i+1}构建完成: {model.name}")
    
    def train_models(self, X_train, y_train, X_val, y_val, **kwargs):
        """训练所有模型"""
        predictions = []
        
        for i, model in enumerate(self.models, 1):
            logger.info(f"训练集成模型{i}...")
            
            callbacks = [
                tf.keras.callbacks.EarlyStopping(patience=kwargs.get('patience', 5), restore_best_weights=True),
                tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5)
            ]
            
            model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=kwargs.get('epochs', 12),
                batch_size=kwargs.get('batch_size', 512),
                callbacks=callbacks,
                verbose=kwargs.get('verbose', 0)
            )
            
            # 预测
            pred = model.predict(X_val, verbose=0)
            predictions.append(pred)
            
            from sklearn.metrics import roc_auc_score
            auc = roc_auc_score(y_val, pred)
            logger.info(f"集成模型{i} AUC: {auc:.4f}")
        
        return predictions
    
    def predict_ensemble(self, X_test):
        """集成预测"""
        predictions = []
        for model in self.models:
            pred = model.predict(X_test, verbose=0)
            predictions.append(pred)
        
        # 简单平均集成
        ensemble_pred = np.mean(predictions, axis=0)
        return ensemble_pred, predictions

class DataAugmentation:
    """数据增强类"""
    
    @staticmethod
    def apply_smote(X_train, y_train, sampling_strategy=0.05, random_state=42):
        """应用SMOTE重采样"""
        logger.info("应用SMOTE重采样...")
        logger.info(f"重采样前: 负样本={np.sum(y_train==0)}, 正样本={np.sum(y_train==1)}")
        
        smote = SMOTE(
            sampling_strategy=sampling_strategy,
            random_state=random_state,
            k_neighbors=5
        )
        
        X_resampled, y_resampled = smote.fit_resample(X_train, y_train)
        
        logger.info(f"重采样后: 负样本={np.sum(y_resampled==0)}, 正样本={np.sum(y_resampled==1)}")
        logger.info(f"新的正样本比例: {y_resampled.mean():.4f}")
        
        return X_resampled, y_resampled

class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, use_smote=True, smote_ratio=0.05):
        self.use_smote = use_smote
        self.smote_ratio = smote_ratio
    
    def train_ensemble(self, X_train, y_train, X_val, y_val, **kwargs):
        """训练集成模型"""
        # 数据增强
        if self.use_smote:
            X_train_aug, y_train_aug = DataAugmentation.apply_smote(
                X_train, y_train, sampling_strategy=self.smote_ratio
            )
        else:
            X_train_aug, y_train_aug = X_train, y_train
        
        # 创建集成模型
        ensemble = EnsembleModel(X_train_aug.shape[1], ensemble_size=kwargs.get('ensemble_size', 3))
        ensemble.build_models()
        
        # 训练模型
        predictions = ensemble.train_models(X_train_aug, y_train_aug, X_val, y_val, **kwargs)
        
        # 集成预测
        ensemble_pred, individual_preds = ensemble.predict_ensemble(X_val)
        
        return {
            'ensemble_model': ensemble,
            'ensemble_prediction': ensemble_pred,
            'individual_predictions': individual_preds,
            'augmented_data': (X_train_aug, y_train_aug)
        }
