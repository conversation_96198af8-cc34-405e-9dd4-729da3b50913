#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
标准化优化迭代框架
支持自动化的模型优化和评估，遵循"有效就保留，无效就跳过"的原则
"""
import os
import sys
import json
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizationFramework:
    """优化迭代框架"""
    
    def __init__(self, base_config_path: str = "src/configs/models/sample_20250311_v7-20250311.json"):
        self.base_config_path = base_config_path
        self.results_dir = "optimization_results"
        self.baseline_results = None
        self.optimization_history = []
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 历史最佳基准
        self.historical_best = {
            'auc': 0.9146,
            'pr_auc': 0.5619,
            'recall_at_840': 0.9747
        }
        
        # EPMMOENet基线基准
        self.epmmoe_baseline = {
            'month_1_auc': 0.8906,
            'month_1_pr_auc': 0.4808,
            'overall_auc': 0.8104,
            'overall_pr_auc': 0.0099
        }
    
    def run_baseline_evaluation(self) -> Dict:
        """运行基线评估"""
        logger.info("=== 运行EPMMOENet基线评估 ===")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"baseline_{timestamp}"
        
        # 运行基线训练
        cmd = [
            "python", "src/train.py",
            "--run_name", run_name,
            "--epochs", "15",
            "--data_dir", "data"
        ]
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=3600,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                # 解析结果
                baseline_results = self._parse_training_output(result.stdout)
                baseline_results['run_name'] = run_name
                baseline_results['timestamp'] = timestamp
                baseline_results['type'] = 'baseline'
                
                # 保存基线结果
                self.baseline_results = baseline_results
                self._save_results(baseline_results, f"{self.results_dir}/baseline_{timestamp}.json")
                
                logger.info(f"基线评估完成: {baseline_results}")
                return baseline_results
            else:
                logger.error(f"基线训练失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("基线训练超时")
            return None
        except Exception as e:
            logger.error(f"基线训练异常: {e}")
            return None
    
    def run_optimization(self, optimization_config: Dict) -> Dict:
        """运行单次优化"""
        opt_name = optimization_config.get('name', 'unknown')
        logger.info(f"=== 运行优化: {opt_name} ===")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"opt_{opt_name}_{timestamp}"
        
        # 准备优化参数
        cmd = ["python", "src/train.py", "--run_name", run_name]
        
        # 添加优化参数
        for key, value in optimization_config.get('params', {}).items():
            cmd.extend([f"--{key}", str(value)])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                # 解析结果
                opt_results = self._parse_training_output(result.stdout)
                opt_results['run_name'] = run_name
                opt_results['timestamp'] = timestamp
                opt_results['type'] = 'optimization'
                opt_results['optimization_name'] = opt_name
                opt_results['config'] = optimization_config
                
                # 评估优化效果
                is_effective = self._evaluate_optimization(opt_results)
                opt_results['is_effective'] = is_effective
                
                # 保存结果
                self._save_results(opt_results, f"{self.results_dir}/opt_{opt_name}_{timestamp}.json")
                self.optimization_history.append(opt_results)
                
                logger.info(f"优化 {opt_name} {'✅ 有效' if is_effective else '❌ 无效'}")
                return opt_results
            else:
                logger.error(f"优化 {opt_name} 训练失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error(f"优化 {opt_name} 训练超时")
            return None
        except Exception as e:
            logger.error(f"优化 {opt_name} 训练异常: {e}")
            return None
    
    def _parse_training_output(self, output: str) -> Dict:
        """解析训练输出"""
        results = {}
        lines = output.split('\n')
        
        for line in lines:
            # 解析Month_1指标
            if 'Month_1:' in line and 'ROC-AUC' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'ROC-AUC' and i + 2 < len(parts):
                        try:
                            results['month_1_auc'] = float(parts[i + 2].rstrip(','))
                        except:
                            pass
                    if part == 'PR-AUC' and i + 2 < len(parts):
                        try:
                            results['month_1_pr_auc'] = float(parts[i + 2])
                        except:
                            pass
            
            # 解析Overall指标
            if 'Overall:' in line and 'ROC-AUC' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'ROC-AUC' and i + 2 < len(parts):
                        try:
                            results['overall_auc'] = float(parts[i + 2].rstrip(','))
                        except:
                            pass
                    if part == 'PR-AUC' and i + 2 < len(parts):
                        try:
                            results['overall_pr_auc'] = float(parts[i + 2])
                        except:
                            pass
        
        return results
    
    def _evaluate_optimization(self, opt_results: Dict) -> bool:
        """评估优化是否有效"""
        if not self.baseline_results:
            logger.warning("没有基线结果，无法评估优化效果")
            return False
        
        # 主要评估指标：Month_1 PR-AUC
        baseline_pr_auc = self.baseline_results.get('month_1_pr_auc', 0)
        opt_pr_auc = opt_results.get('month_1_pr_auc', 0)
        
        # 次要评估指标：Month_1 AUC
        baseline_auc = self.baseline_results.get('month_1_auc', 0)
        opt_auc = opt_results.get('month_1_auc', 0)
        
        # 评估标准：PR-AUC提升 > 1% 或 (PR-AUC提升 > 0.5% 且 AUC不下降超过1%)
        pr_auc_improvement = opt_pr_auc - baseline_pr_auc
        auc_change = opt_auc - baseline_auc
        
        if pr_auc_improvement > 0.01:  # PR-AUC提升超过1%
            return True
        elif pr_auc_improvement > 0.005 and auc_change > -0.01:  # PR-AUC提升0.5%且AUC不大幅下降
            return True
        else:
            return False
    
    def _save_results(self, results: Dict, filepath: str):
        """保存结果"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
    
    def run_optimization_sequence(self, optimizations: List[Dict]) -> List[Dict]:
        """运行优化序列"""
        logger.info("=== 开始优化序列 ===")
        
        # 1. 运行基线评估
        if not self.baseline_results:
            baseline = self.run_baseline_evaluation()
            if not baseline:
                logger.error("基线评估失败，终止优化序列")
                return []
        
        # 2. 运行优化序列
        effective_optimizations = []
        
        for i, opt_config in enumerate(optimizations, 1):
            logger.info(f"运行优化 {i}/{len(optimizations)}: {opt_config.get('name', 'unknown')}")
            
            result = self.run_optimization(opt_config)
            if result and result.get('is_effective', False):
                effective_optimizations.append(result)
                logger.info(f"✅ 优化 {opt_config.get('name')} 有效，已保留")
            else:
                logger.info(f"❌ 优化 {opt_config.get('name')} 无效，已跳过")
        
        # 3. 生成总结报告
        self._generate_summary_report(effective_optimizations)
        
        return effective_optimizations
    
    def _generate_summary_report(self, effective_optimizations: List[Dict]):
        """生成总结报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"{self.results_dir}/optimization_summary_{timestamp}.json"
        
        summary = {
            'timestamp': timestamp,
            'baseline_results': self.baseline_results,
            'total_optimizations_tested': len(self.optimization_history),
            'effective_optimizations_count': len(effective_optimizations),
            'effective_optimizations': effective_optimizations,
            'best_result': self._find_best_result(effective_optimizations),
            'historical_comparison': self._compare_with_historical_best(effective_optimizations)
        }
        
        self._save_results(summary, report_path)
        
        logger.info("=== 优化序列完成 ===")
        logger.info(f"总测试优化: {len(self.optimization_history)}")
        logger.info(f"有效优化: {len(effective_optimizations)}")
        logger.info(f"报告保存到: {report_path}")
        
        if effective_optimizations:
            best = summary['best_result']
            logger.info(f"最佳结果: {best['optimization_name']}")
            logger.info(f"  Month_1 AUC: {best.get('month_1_auc', 0):.4f}")
            logger.info(f"  Month_1 PR-AUC: {best.get('month_1_pr_auc', 0):.4f}")
    
    def _find_best_result(self, effective_optimizations: List[Dict]) -> Optional[Dict]:
        """找到最佳结果"""
        if not effective_optimizations:
            return None
        
        # 按Month_1 PR-AUC排序
        best = max(effective_optimizations, key=lambda x: x.get('month_1_pr_auc', 0))
        return best
    
    def _compare_with_historical_best(self, effective_optimizations: List[Dict]) -> Dict:
        """与历史最佳对比"""
        if not effective_optimizations:
            return {'status': 'no_effective_optimizations'}
        
        best = self._find_best_result(effective_optimizations)
        
        return {
            'best_month_1_auc': best.get('month_1_auc', 0),
            'best_month_1_pr_auc': best.get('month_1_pr_auc', 0),
            'historical_best_auc': self.historical_best['auc'],
            'historical_best_pr_auc': self.historical_best['pr_auc'],
            'auc_gap': self.historical_best['auc'] - best.get('month_1_auc', 0),
            'pr_auc_gap': self.historical_best['pr_auc'] - best.get('month_1_pr_auc', 0),
            'progress_towards_historical': {
                'auc_progress': (best.get('month_1_auc', 0) - self.epmmoe_baseline['month_1_auc']) / 
                               (self.historical_best['auc'] - self.epmmoe_baseline['month_1_auc']),
                'pr_auc_progress': (best.get('month_1_pr_auc', 0) - self.epmmoe_baseline['month_1_pr_auc']) / 
                                  (self.historical_best['pr_auc'] - self.epmmoe_baseline['month_1_pr_auc'])
            }
        }

# 预定义的优化配置
OPTIMIZATION_CONFIGS = [
    {
        'name': 'extended_epochs',
        'description': '延长训练轮数',
        'params': {
            'epochs': 25,
            'data_dir': 'data'
        }
    },
    {
        'name': 'larger_batch',
        'description': '增大批量大小',
        'params': {
            'epochs': 15,
            'batch_size': 16384,
            'data_dir': 'data'
        }
    },
    {
        'name': 'longer_patience',
        'description': '增加早停耐心',
        'params': {
            'epochs': 30,
            'patience': 15,
            'data_dir': 'data'
        }
    }
]

def main():
    """主函数"""
    framework = OptimizationFramework()
    
    # 运行优化序列
    effective_opts = framework.run_optimization_sequence(OPTIMIZATION_CONFIGS)
    
    print(f"\n🎯 优化完成！")
    print(f"有效优化数量: {len(effective_opts)}")
    
    if effective_opts:
        best = framework._find_best_result(effective_opts)
        print(f"最佳优化: {best['optimization_name']}")
        print(f"Month_1 AUC: {best.get('month_1_auc', 0):.4f}")
        print(f"Month_1 PR-AUC: {best.get('month_1_pr_auc', 0):.4f}")

if __name__ == "__main__":
    main()
