"""
ESMM模型实现 (Entire Space Multi-task Model)

参考:
    [1] <PERSON>, <PERSON>, <PERSON>, et al. Entire space multi-task model: An effective approach for estimating 
        post-click conversion rate[C]//The 41st International ACM SIGIR Conference on Research & 
        Development in Information Retrieval. 2018.(https://arxiv.org/abs/1804.07931)
    
    [2] https://github.com/busesese/MultiTaskModel
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, Embedding, Flatten, Concatenate, Multiply, Dropout, BatchNormalization
from tensorflow.keras.models import Model


class DNN(tf.keras.layers.Layer):
    """DNN层实现，用于构建深度网络"""
    
    def __init__(self, hidden_units, activation='relu', dropout_rate=0, use_bn=False, l2_reg=0, **kwargs):
        """
        初始化DNN层
        
        Args:
            hidden_units: DNN网络隐藏层单元数列表
            activation: 激活函数
            dropout_rate: dropout比率
            use_bn: 是否使用批归一化
            l2_reg: L2正则化系数
        """
        super(DNN, self).__init__(**kwargs)
        self.hidden_units = hidden_units
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.use_bn = use_bn
        self.l2_reg = l2_reg
        
        self.dense_layers = []
        self.dropout_layers = []
        self.bn_layers = []
        
        for units in hidden_units:
            self.dense_layers.append(
                Dense(units, 
                      activation=activation,
                      kernel_regularizer=tf.keras.regularizers.l2(l2_reg))
            )
            if use_bn:
                self.bn_layers.append(BatchNormalization())
            if dropout_rate > 0:
                self.dropout_layers.append(Dropout(dropout_rate))
    
    def call(self, inputs, training=None):
        """前向传播"""
        x = inputs
        for i, dense in enumerate(self.dense_layers):
            x = dense(x)
            if self.use_bn:
                x = self.bn_layers[i](x, training=training)
            if self.dropout_rate > 0:
                x = self.dropout_layers[i](x, training=training)
        return x
    
    def get_config(self):
        """获取配置"""
        config = {
            'hidden_units': self.hidden_units,
            'activation': self.activation,
            'dropout_rate': self.dropout_rate,
            'use_bn': self.use_bn,
            'l2_reg': self.l2_reg,
        }
        base_config = super(DNN, self).get_config()
        return dict(list(base_config.items()) + list(config.items()))


def ESMM(feature_config, embedding_dim=16, dnn_hidden_units=(256, 128, 64), 
         dnn_activation='relu', dnn_dropout=0, dnn_use_bn=False, l2_reg_embedding=0.00001, 
         l2_reg_dnn=0, seed=1024, task_names=('ctr_output', 'ctcvr_output')):
    """
    构建ESMM模型
    
    Args:
        feature_config: 特征配置信息，包含类别特征和数值特征的配置
        embedding_dim: 嵌入维度
        dnn_hidden_units: DNN隐藏层单元数
        dnn_activation: DNN激活函数
        dnn_dropout: DNN的dropout率
        dnn_use_bn: 是否在DNN中使用批归一化
        l2_reg_embedding: 嵌入层的L2正则化系数
        l2_reg_dnn: DNN的L2正则化系数
        seed: 随机种子
        task_names: 任务名称，默认为('ctr_output', 'ctcvr_output')
        
    Returns:
        ESMM模型
    """
    if len(task_names) != 2:
        raise ValueError("任务名称列表长度必须为2，但得到了{}".format(len(task_names)))
    
    # 设置随机种子
    tf.random.set_seed(seed)
    
    # 构建输入
    inputs = {}
    
    # 数值特征输入
    numerical_inputs = []
    for col in feature_config['numerical_cols']:
        inputs[f'numerical_{col}'] = Input(shape=(1,), name=f'numerical_{col}')
        numerical_inputs.append(inputs[f'numerical_{col}'])
    
    # 类别特征输入和嵌入
    embedding_list = []
    for col in feature_config['categorical_cols']:
        inputs[f'categorical_{col}'] = Input(shape=(1,), name=f'categorical_{col}')
        embedding = Embedding(
            input_dim=feature_config['categorical_dims'][col],
            output_dim=embedding_dim,
            embeddings_regularizer=tf.keras.regularizers.l2(l2_reg_embedding),
            name=f'embedding_{col}'
        )(inputs[f'categorical_{col}'])
        embedding = Flatten()(embedding)
        embedding_list.append(embedding)
    
    # 合并所有特征
    if numerical_inputs:
        numerical_features = Concatenate()(numerical_inputs) if len(numerical_inputs) > 1 else numerical_inputs[0]
        features = Concatenate()([numerical_features] + embedding_list) if embedding_list else numerical_features
    else:
        features = Concatenate()(embedding_list) if len(embedding_list) > 1 else embedding_list[0]
    
    # CTR任务塔
    ctr_dnn = DNN(dnn_hidden_units, dnn_activation, dnn_dropout, dnn_use_bn, l2_reg_dnn)(features)
    ctr_logit = Dense(1, use_bias=False)(ctr_dnn)
    ctr_pred = tf.keras.layers.Activation('sigmoid', name=task_names[0])(ctr_logit)
    
    # CVR任务塔
    cvr_dnn = DNN(dnn_hidden_units, dnn_activation, dnn_dropout, dnn_use_bn, l2_reg_dnn)(features)
    cvr_logit = Dense(1, use_bias=False)(cvr_dnn)
    cvr_pred = tf.keras.layers.Activation('sigmoid')(cvr_logit)
    
    # CTCVR = CTR * CVR
    ctcvr_pred = Multiply(name=task_names[1])([ctr_pred, cvr_pred])
    
    # 构建模型
    model = Model(inputs=list(inputs.values()), outputs=[ctr_pred, ctcvr_pred])
    
    return model


def compile_esmm_model(model, learning_rate=0.001, ctr_weight=1.0, ctcvr_weight=1.0):
    """
    编译ESMM模型
    
    Args:
        model: ESMM模型
        learning_rate: 学习率
        ctr_weight: CTR任务的损失权重
        ctcvr_weight: CTCVR任务的损失权重
        
    Returns:
        编译后的模型
    """
    # 获取任务名称
    output_names = [output.name for output in model.outputs]
    
    # 设置损失权重
    loss_weights = {output_names[0]: ctr_weight, output_names[1]: ctcvr_weight}
    
    # 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
        loss={name: 'binary_crossentropy' for name in output_names},
        loss_weights=loss_weights,
        metrics={name: ['AUC', 'accuracy'] for name in output_names}
    )
    
    return model 