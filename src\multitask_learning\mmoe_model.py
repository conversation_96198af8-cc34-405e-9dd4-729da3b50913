"""
MMOE模型实现 (Multi-gate Mixture-of-Experts)

参考:
    [1] <PERSON>, <PERSON><PERSON><PERSON>, et al. "Modeling task relationships in multi-task learning with 
        multi-gate mixture-of-experts." Proceedings of the 24th ACM SIGKDD International 
        Conference on Knowledge Discovery & Data Mining. 2018.
        
    [2] https://github.com/busesese/MultiTaskModel
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, Embedding, Flatten, Concatenate, Multiply, Dropout, BatchNormalization
from tensorflow.keras.models import Model


class DNN(tf.keras.layers.Layer):
    """DNN层实现，用于构建深度网络"""
    
    def __init__(self, hidden_units, activation='relu', dropout_rate=0, use_bn=False, l2_reg=0, **kwargs):
        """
        初始化DNN层
        
        Args:
            hidden_units: DNN网络隐藏层单元数列表
            activation: 激活函数
            dropout_rate: dropout比率
            use_bn: 是否使用批归一化
            l2_reg: L2正则化系数
        """
        super(DNN, self).__init__(**kwargs)
        self.hidden_units = hidden_units
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.use_bn = use_bn
        self.l2_reg = l2_reg
        
        self.dense_layers = []
        self.dropout_layers = []
        self.bn_layers = []
        
        for units in hidden_units:
            self.dense_layers.append(
                Dense(units, 
                      activation=activation,
                      kernel_regularizer=tf.keras.regularizers.l2(l2_reg))
            )
            if use_bn:
                self.bn_layers.append(BatchNormalization())
            if dropout_rate > 0:
                self.dropout_layers.append(Dropout(dropout_rate))
    
    def call(self, inputs, training=None):
        """前向传播"""
        x = inputs
        for i, dense in enumerate(self.dense_layers):
            x = dense(x)
            if self.use_bn:
                x = self.bn_layers[i](x, training=training)
            if self.dropout_rate > 0:
                x = self.dropout_layers[i](x, training=training)
        return x
    
    def get_config(self):
        """获取配置"""
        config = {
            'hidden_units': self.hidden_units,
            'activation': self.activation,
            'dropout_rate': self.dropout_rate,
            'use_bn': self.use_bn,
            'l2_reg': self.l2_reg,
        }
        base_config = super(DNN, self).get_config()
        return dict(list(base_config.items()) + list(config.items()))


class MMoE(tf.keras.layers.Layer):
    """Multi-gate Mixture-of-Experts层实现"""
    
    def __init__(self, num_experts, num_tasks, expert_units, gate_units, expert_activation='relu', 
                 gate_activation='softmax', expert_dropout=0, expert_use_bn=False, l2_reg=0, **kwargs):
        """
        初始化MMoE层
        
        Args:
            num_experts: 专家数量
            num_tasks: 任务数量
            expert_units: 专家网络的隐藏层单元数
            gate_units: 门控网络的隐藏层单元数
            expert_activation: 专家网络的激活函数
            gate_activation: 门控网络的激活函数
            expert_dropout: 专家网络的dropout率
            expert_use_bn: 专家网络是否使用批归一化
            l2_reg: L2正则化系数
        """
        super(MMoE, self).__init__(**kwargs)
        self.num_experts = num_experts
        self.num_tasks = num_tasks
        self.expert_units = expert_units
        self.gate_units = gate_units
        self.expert_activation = expert_activation
        self.gate_activation = gate_activation
        self.expert_dropout = expert_dropout
        self.expert_use_bn = expert_use_bn
        self.l2_reg = l2_reg
        
        # 专家网络
        self.experts = []
        for i in range(num_experts):
            expert = DNN(
                hidden_units=expert_units,
                activation=expert_activation,
                dropout_rate=expert_dropout,
                use_bn=expert_use_bn,
                l2_reg=l2_reg,
                name=f'expert_{i}'
            )
            self.experts.append(expert)
        
        # 任务门控网络
        self.gates = []
        for i in range(num_tasks):
            gate = Dense(
                num_experts,
                activation=gate_activation,
                kernel_regularizer=tf.keras.regularizers.l2(l2_reg),
                name=f'gate_{i}'
            )
            self.gates.append(gate)
    
    def call(self, inputs, training=None):
        """前向传播"""
        # 每个专家的输出
        expert_outputs = []
        for expert in self.experts:
            expert_outputs.append(expert(inputs, training=training))
        
        # 堆叠专家输出
        expert_outputs_stack = tf.stack(expert_outputs, axis=1)  # (batch_size, num_experts, expert_output_dim)
        
        # 各任务的门控输出
        gate_outputs = []
        for gate in self.gates:
            gate_output = gate(inputs)  # (batch_size, num_experts)
            gate_outputs.append(gate_output)
        
        # 任务特定输出
        task_outputs = []
        for i, gate_output in enumerate(gate_outputs):
            # 扩展门控输出维度
            expanded_gate_output = tf.expand_dims(gate_output, axis=-1)  # (batch_size, num_experts, 1)
            
            # 门控加权求和
            weighted_expert_output = expert_outputs_stack * expanded_gate_output  # (batch_size, num_experts, expert_output_dim)
            task_output = tf.reduce_sum(weighted_expert_output, axis=1)  # (batch_size, expert_output_dim)
            
            task_outputs.append(task_output)
        
        return task_outputs
    
    def get_config(self):
        """获取配置"""
        config = {
            'num_experts': self.num_experts,
            'num_tasks': self.num_tasks,
            'expert_units': self.expert_units,
            'gate_units': self.gate_units,
            'expert_activation': self.expert_activation,
            'gate_activation': self.gate_activation,
            'expert_dropout': self.expert_dropout,
            'expert_use_bn': self.expert_use_bn,
            'l2_reg': self.l2_reg,
        }
        base_config = super(MMoE, self).get_config()
        return dict(list(base_config.items()) + list(config.items()))


def MMOE(feature_config, num_experts=4, expert_units=(64,), tower_units=(32,), embedding_dim=16,
         expert_activation='relu', tower_activation='relu', expert_dropout=0, tower_dropout=0,
         expert_use_bn=False, tower_use_bn=False, l2_reg_embedding=0.00001, l2_reg_expert=0,
         l2_reg_tower=0, seed=1024, task_names=('ctr_output', 'ctcvr_output')):
    """
    构建MMOE模型
    
    Args:
        feature_config: 特征配置信息，包含类别特征和数值特征的配置
        num_experts: 专家数量
        expert_units: 专家网络的隐藏层单元数
        tower_units: 任务塔的隐藏层单元数
        embedding_dim: 嵌入维度
        expert_activation: 专家网络的激活函数
        tower_activation: 任务塔的激活函数
        expert_dropout: 专家网络的dropout率
        tower_dropout: 任务塔的dropout率
        expert_use_bn: 专家网络是否使用批归一化
        tower_use_bn: 任务塔是否使用批归一化
        l2_reg_embedding: 嵌入层的L2正则化系数
        l2_reg_expert: 专家网络的L2正则化系数
        l2_reg_tower: 任务塔的L2正则化系数
        seed: 随机种子
        task_names: 任务名称，默认为('ctr_output', 'ctcvr_output')
        
    Returns:
        MMOE模型
    """
    if len(task_names) != 2:
        raise ValueError("任务名称列表长度必须为2，但得到了{}".format(len(task_names)))
    
    # 设置随机种子
    tf.random.set_seed(seed)
    
    # 构建输入
    inputs = {}
    
    # 数值特征输入
    numerical_inputs = []
    for col in feature_config['numerical_cols']:
        inputs[f'numerical_{col}'] = Input(shape=(1,), name=f'numerical_{col}')
        numerical_inputs.append(inputs[f'numerical_{col}'])
    
    # 类别特征输入和嵌入
    embedding_list = []
    for col in feature_config['categorical_cols']:
        inputs[f'categorical_{col}'] = Input(shape=(1,), name=f'categorical_{col}')
        embedding = Embedding(
            input_dim=feature_config['categorical_dims'][col],
            output_dim=embedding_dim,
            embeddings_regularizer=tf.keras.regularizers.l2(l2_reg_embedding),
            name=f'embedding_{col}'
        )(inputs[f'categorical_{col}'])
        embedding = Flatten()(embedding)
        embedding_list.append(embedding)
    
    # 合并所有特征
    if numerical_inputs:
        numerical_features = Concatenate()(numerical_inputs) if len(numerical_inputs) > 1 else numerical_inputs[0]
        features = Concatenate()([numerical_features] + embedding_list) if embedding_list else numerical_features
    else:
        features = Concatenate()(embedding_list) if len(embedding_list) > 1 else embedding_list[0]
    
    # MMoE层
    mmoe_layer = MMoE(
        num_experts=num_experts,
        num_tasks=len(task_names),
        expert_units=expert_units,
        gate_units=[],  # 简化实现，不使用门控的隐藏层
        expert_activation=expert_activation,
        expert_dropout=expert_dropout,
        expert_use_bn=expert_use_bn,
        l2_reg=l2_reg_expert
    )
    
    task_outputs = mmoe_layer(features)
    
    # 任务塔
    task_specific_outputs = []
    for i, task_name in enumerate(task_names):
        tower_output = task_outputs[i]
        # 添加任务塔
        tower = DNN(
            hidden_units=tower_units,
            activation=tower_activation,
            dropout_rate=tower_dropout,
            use_bn=tower_use_bn,
            l2_reg=l2_reg_tower,
            name=f'tower_{task_name}'
        )(tower_output)
        
        # 输出层
        if i == 0:  # CTR任务
            logit = Dense(1, use_bias=False)(tower)
            pred = tf.keras.layers.Activation('sigmoid', name=task_name)(logit)
            task_specific_outputs.append(pred)
        elif i == 1:  # CVR任务
            logit = Dense(1, use_bias=False)(tower)
            cvr_pred = tf.keras.layers.Activation('sigmoid')(logit)
            ctr_pred = task_specific_outputs[0]
            # CTCVR = CTR * CVR
            ctcvr_pred = Multiply(name=task_name)([ctr_pred, cvr_pred])
            task_specific_outputs.append(ctcvr_pred)
    
    # 构建模型
    model = Model(inputs=list(inputs.values()), outputs=task_specific_outputs)
    
    return model


def compile_mmoe_model(model, learning_rate=0.001, ctr_weight=1.0, ctcvr_weight=1.0):
    """
    编译MMOE模型
    
    Args:
        model: MMOE模型
        learning_rate: 学习率
        ctr_weight: CTR任务的损失权重
        ctcvr_weight: CTCVR任务的损失权重
        
    Returns:
        编译后的模型
    """
    # 获取任务名称
    output_names = [output.name for output in model.outputs]
    
    # 设置损失权重
    loss_weights = {output_names[0]: ctr_weight, output_names[1]: ctcvr_weight}
    
    # 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
        loss={name: 'binary_crossentropy' for name in output_names},
        loss_weights=loss_weights,
        metrics={name: ['AUC', 'accuracy'] for name in output_names}
    )
    
    return model 