#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
NIO新车购买倾向数据字典分析命令行工具
"""
import os
import sys
import argparse
from pathlib import Path

# 确保能导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.data.data_dict_analysis import DataDictionaryAnalyzer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='NIO数据字典分析工具')
    
    parser.add_argument('--dict_path', type=str, 
                        default='data/full_business_dictionary/data_dictionary.json',
                        help='数据字典JSON文件路径')
    
    parser.add_argument('--output_path', type=str, 
                        default='output/dict_analysis',
                        help='分析结果输出路径')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='显示详细日志信息')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 确保输入文件存在
    if not os.path.exists(args.dict_path):
        print(f"错误: 数据字典文件不存在: {args.dict_path}")
        return 1
    
    print(f"开始分析数据字典: {args.dict_path}")
    print(f"结果将保存至: {args.output_path}")
    
    # 创建分析器
    analyzer = DataDictionaryAnalyzer(
        dict_path=args.dict_path,
        output_path=args.output_path
    )
    
    # 执行完整分析
    if analyzer.load_data_dictionary():
        analyzer.analyze_statistics()
        problem_features, summary = analyzer.identify_problem_features()
        analyzer.analyze_by_domain()
        analyzer.generate_recommendations()
        
        # 生成报告
        success = analyzer.generate_report()
        if success:
            # 输出结果摘要
            print("\n数据字典分析完成!")
            print(f"分析结果已保存至: {args.output_path}")
            print(f"HTML报告: {args.output_path}/data_dictionary_analysis_report.html")
            
            # 输出问题特征摘要
            print("\n数据问题摘要:")
            for problem_type, count in summary['problem_features'].items():
                if count > 0:
                    percent = summary['problem_percent'][problem_type]
                    print(f"- {problem_type}: {count}个特征 ({percent:.2f}%)")
            
            return 0
        else:
            print("报告生成失败")
            return 1
    else:
        print(f"数据字典加载失败: {args.dict_path}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 