#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版训练脚本 - 用于快速测试和验证基础功能
"""
import os
import sys
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_sample_data():
    """加载样本数据进行快速测试"""
    logger.info("加载样本数据...")
    
    # 加载评估数据集（1%采样）
    eval_file = "data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet"
    
    try:
        df = pd.read_parquet(eval_file)
        logger.info(f"数据加载成功: {df.shape}")
        logger.info(f"列数: {len(df.columns)}")
        
        # 检查目标列
        target_cols = [col for col in df.columns if 'purchase' in col.lower()]
        logger.info(f"购买相关列: {target_cols}")
        
        return df
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        return None

def prepare_simple_features(df):
    """准备简单的特征用于测试"""
    logger.info("准备简单特征...")

    # 选择一些数值型特征
    numeric_cols = []
    for col in df.columns:
        if df[col].dtype in ['int64', 'float64'] and col not in ['user_id', 'datetime']:
            numeric_cols.append(col)

    # 限制特征数量，避免过于复杂
    numeric_cols = numeric_cols[:50]  # 取前50个数值特征
    logger.info(f"选择的数值特征: {len(numeric_cols)}")
    logger.info(f"特征列: {numeric_cols[:10]}...")  # 显示前10个

    # 准备特征矩阵
    X = df[numeric_cols].fillna(0).values

    # 准备标签 - 寻找购买标签
    if 'purchase_days_nio_new_car_total' in df.columns:
        # 处理可能的字符串值
        target_col = df['purchase_days_nio_new_car_total']
        logger.info(f"目标列类型: {target_col.dtype}")
        logger.info(f"目标列唯一值示例: {target_col.unique()[:10]}")

        # 转换为数值，非数值设为0
        target_numeric = pd.to_numeric(target_col, errors='coerce').fillna(0)
        y = (target_numeric > 0).astype(int).values

    elif 'm_purchase_days_nio_new_car' in df.columns:
        target_col = df['m_purchase_days_nio_new_car']
        target_numeric = pd.to_numeric(target_col, errors='coerce').fillna(0)
        y = (target_numeric > 0).astype(int).values
    else:
        # 如果找不到标签，创建一个随机标签用于测试
        logger.warning("未找到购买标签，创建随机标签用于测试")
        y = np.random.randint(0, 2, size=len(df))

    logger.info(f"特征形状: {X.shape}")
    logger.info(f"标签形状: {y.shape}")
    logger.info(f"正样本比例: {y.mean():.4f}")

    return X, y, numeric_cols

def create_simple_model(input_dim):
    """创建简单的神经网络模型"""
    logger.info(f"创建简单模型，输入维度: {input_dim}")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(64, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.Dropout(0.3),
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.3),
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer='adam',
        loss='binary_crossentropy',
        metrics=['accuracy', 'auc']
    )
    
    return model

def main():
    """主函数"""
    logger.info("开始简化版训练...")
    
    # 1. 加载数据
    df = load_sample_data()
    if df is None:
        return 1
    
    # 2. 准备特征
    X, y, feature_names = prepare_simple_features(df)
    
    # 3. 数据分割
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 4. 创建模型
    model = create_simple_model(X_train.shape[1])
    model.summary()
    
    # 5. 训练模型
    logger.info("开始训练...")
    
    # 设置回调
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5)
    ]
    
    # 训练
    history = model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=20,
        batch_size=256,
        callbacks=callbacks,
        verbose=1
    )
    
    # 6. 评估模型
    logger.info("评估模型...")
    
    # 预测
    y_pred_proba = model.predict(X_test)
    y_pred = (y_pred_proba > 0.5).astype(int)
    
    # 计算指标
    from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
    
    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    logger.info(f"测试准确率: {accuracy:.4f}")
    logger.info(f"测试AUC: {auc:.4f}")
    
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    # 7. 保存结果
    results = {
        'accuracy': float(accuracy),
        'auc': float(auc),
        'feature_count': len(feature_names),
        'train_samples': len(X_train),
        'test_samples': len(X_test),
        'positive_rate': float(y.mean())
    }
    
    with open('simple_train_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("简化版训练完成！")
    logger.info(f"结果已保存到: simple_train_results.json")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
