"""
EPMMOENet model implementation for conversion rate prediction.

This model implements a multimodal architecture with time series attention
and feature cross enhancement.
"""
import tensorflow as tf
import logging
from src.models.layers.layers import CrossLayer, TimeSeriesAttention, TransformerEncoder
from tensorflow.keras.layers import Input, Embedding, Dense, GRU, BatchNormalization, Dropout, AdditiveAttention, LayerNormalization, MultiHeadAttention, Layer, Concatenate, Multiply
from tensorflow.keras.models import Model
from ...multitask_learning.esmm_model import DNN  # Import DNN from ESMM module


class EPMMOENet_Model(tf.keras.Model):
    """
    EPMMOENet model for conversion rate prediction.
    
    Features:
    - Multiple input types (categorical, numerical, sequential)
    - Optional feature crossing
    - Time series attention with time decay for sequential features
    - Mixed precision training option
    """
    
    def __init__(self, 
                model_config, 
                default_embedding_dimension=8, 
                default_gru_dimension=32,
                expert_num=8,
                use_cross_layer=True,
                use_multitask=False,
                use_esmm=False,  # Add use_esmm parameter
                use_mixed_precision=True,
                use_time_attention=True,
                time_decay_factor=0.05
                ):
        """
        Initialize the EPMMOENet model.
        
        Args:
            model_config (dict): Model configuration.
            default_embedding_dimension (int): Default embedding dimension.
            default_gru_dimension (int): Default GRU dimension.
            expert_num (int): Number of experts for MoE layers.
            use_cross_layer (bool): Whether to use feature cross layer.
            use_multitask (bool): Whether to use multitask learning.
            use_esmm (bool): Whether to use ESMM model structure.
            use_mixed_precision (bool): Whether to use mixed precision training.
            use_time_attention (bool): Whether to use time decay attention.
            time_decay_factor (float): Time decay factor for attention.
        """
        super().__init__()
        
        # Get output dimension from config
        output_dimension = model_config.get("output_dimension", 6)
        output_activation = model_config.get("output_activation", "sigmoid")
        
        # 如果使用多任务学习，并且有mask_label，则输出维度需要翻倍
        if use_multitask and model_config.get("mask_label"):
            output_dimension = output_dimension * 2
            self.logger = logging.getLogger(__name__)
            self.logger.info(f"Multitask learning with mask_label: output_dimension set to {output_dimension}")
        
        # Store configuration
        self.use_cross_layer = use_cross_layer
        self.use_time_attention = use_time_attention
        self.use_multitask = use_multitask
        self.use_esmm = use_esmm  # Store use_esmm flag
        self.time_decay_factor = time_decay_factor
        self.output_dimension = output_dimension
        self.output_activation = output_activation
        self.logger = logging.getLogger(__name__)
        
        # Enable mixed precision training if requested
        if use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            self.logger.info("Mixed precision training enabled")
        
        # Parse feature configuration
        # 1: Input layer parameters
        RawFeature = model_config.get("RawFeature", {}).copy()
        
        # 2: Feature layer parameters (general + sequence branches)
        self.InputGeneral_features = model_config.get("InputGeneral", {}).get("features", [])
        self.InputSeqSet_features = model_config.get("InputSeqSet", {}).get("Set", [])
        self.InputSeqSet_infos = model_config.get("InputSeqSet", {}).get("SetInfo", {})
        
        # 3: Scene parameters for cross layer
        self.InputScene_features = model_config.get("InputScene", {}).get("features", [])
        
        # Optimize input layer parameters to skip unused embeddings
        InputSeq_features = []
        for set_name in self.InputSeqSet_features:
            InputSeq_features.extend(self.InputSeqSet_infos[set_name]["features"])
            
        Applied_features = self.InputGeneral_features + self.InputScene_features + InputSeq_features
        self.RawFeature = {k: v for k, v in RawFeature.items() if k in Applied_features}
        
        # Initialize dimensions tracking
        self.InputGeneral_dimensions, self.InputSeqSet_dimensions = 0, 0
        
        # Initialize embedding layers dictionary
        self.Embedding_layers = {}
        
        # Create embedding layers for each feature
        for feature_name, feature_info in self.RawFeature.items():
            # Get feature data type
            feature_dtype = feature_info["dtype"]
            assert feature_dtype in ["StringLookup", "Bucket", "Dense", "Embedding"]
            
            # Create embedding layers based on feature type
            if feature_dtype == "StringLookup":
                vocabulary = feature_info.get("vocabulary", [])
                embedding_dimension = feature_info.get("embedding_dimension", default_embedding_dimension)
                
                # 确保有一个有效的词表 - 注意，这里我们依赖预处理阶段已经为空词表生成了合适的值
                if not vocabulary:
                    raise ValueError(f"Empty vocabulary for feature {feature_name}. Preprocessing should have generated a vocabulary.")
                    
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.StringLookup(
                        vocabulary=vocabulary, mask_token=None),
                    tf.keras.layers.Embedding(
                        len(vocabulary) + 1,
                        embedding_dimension,
                        embeddings_initializer=tf.keras.initializers.HeNormal()
                    )
                ])
                
            elif feature_dtype == "Bucket":
                bin_boundarie = feature_info.get("bin_boundarie")
                embedding_dimension = feature_info.get("embedding_dimension", default_embedding_dimension)
                
                # 确保bin_boundarie存在
                if bin_boundarie is None:
                    raise ValueError(f"Missing bin_boundarie for feature {feature_name}.")
                
                # 如果只有一个元素，添加另一个远大于任何可能值的元素
                if len(bin_boundarie) == 1:
                    bin_boundarie = bin_boundarie + [float('inf')]
                
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.Discretization(bin_boundarie),
                    tf.keras.layers.Embedding(
                        len(bin_boundarie) + 1,
                        embedding_dimension,
                        embeddings_initializer=tf.keras.initializers.HeNormal()
                    )
                ])
                
            elif feature_dtype == "Dense":
                embedding_dimension = feature_info.get("embedding_dimension", 128)
                self.Embedding_layers[feature_name] = tf.keras.Sequential([
                    tf.keras.layers.Dense(512, 
                                         activation="relu", 
                                         kernel_regularizer=tf.keras.regularizers.l2(0.005)),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Dropout(rate=0.2),
                    tf.keras.layers.Dense(embedding_dimension)
                ])
                
            elif feature_dtype == "Embedding":
                # Direct passthrough
                embedding_dimension = feature_info.get("embedding_dimension", 16)
                self.Embedding_layers[feature_name] = lambda x: x
                
            # Track dimensions for non-sequential branch
            if feature_name in self.InputGeneral_features:
                self.InputGeneral_dimensions += embedding_dimension
        
        # Initialize sequence layer dictionaries
        self.Sequence_layers = {}
        self.TimeAttention_layers = {}
        self.list_mask_feature_name = []
        
        # Create sequence processing layers
        for set_name, set_info in self.InputSeqSet_infos.items():
            if set_name in self.InputSeqSet_features:
                assert len(set_info) > 0
                gru_dimension = set_info.get("gru_dimension", default_gru_dimension)
                
                # Use GRU + time decay attention for sequence features
                if self.use_time_attention:
                    self.Sequence_layers[set_name] = tf.keras.layers.GRU(
                        gru_dimension, return_sequences=True)
                    self.TimeAttention_layers[set_name] = TimeSeriesAttention(
                        attention_dim=gru_dimension//2,
                        time_decay_factor=self.time_decay_factor
                    )
                else:
                    self.Sequence_layers[set_name] = tf.keras.Sequential([
                        tf.keras.layers.GRU(gru_dimension, return_sequences=False)
                    ])
                    
                self.list_mask_feature_name.append(set_info["features"][0])
                
                # Track sequence branch dimensions
                self.InputSeqSet_dimensions += gru_dimension
        
        # Initialize cross layer if enabled
        if self.use_cross_layer:
            self.cross_layer = CrossLayer(128)  # 从64增加到128，增强特征交叉能力
            
        # Initialize scene weight network
        if len(self.InputScene_features) > 0:
            self.Scene_layers = tf.keras.models.Sequential([
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),  # 从0.3降低到0.2
                tf.keras.layers.Dense(
                    256, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005)),  # 从0.01降低到0.005
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),  # 从0.3降低到0.2
                tf.keras.layers.Dense(
                    self.InputGeneral_dimensions + self.InputSeqSet_dimensions, 
                    activation="sigmoid", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005))  # 从0.01降低到0.005
            ])
            
        # Initialize prediction branch
        self.OutputTask_feature = model_config.get("OutputTask", {})
        if len(self.OutputTask_feature) > 0:
            # Multi-task model (not implemented in this version)
            self.logger.warning("Multi-task model configuration not fully implemented")
            
        # 创建共享层和任务特定层
        if self.use_multitask:
            # 共享特征提取层
            self.Shared_layers = tf.keras.models.Sequential([
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.1),  # 降低Dropout率，减少训练不稳定性
                tf.keras.layers.Dense(
                    512, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.001)),  # 降低正则化强度
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.1)
            ])
            
            # 为每个月份创建专门的预测头
            self.Task_towers = []
            
            # 判断是否有mask_label
            has_mask = output_dimension > 6
            
            for i in range(output_dimension):
                # 根据任务的重要性设置不同的网络结构
                if i == 0 or (has_mask and i == output_dimension // 2):  # Month_1（原始和累计）
                    tower = tf.keras.models.Sequential([
                        tf.keras.layers.Dense(
                            128,  # 减小网络规模，提高稳定性
                            activation="relu", 
                            kernel_regularizer=tf.keras.regularizers.l2(0.001)),
                        tf.keras.layers.BatchNormalization(),
                        tf.keras.layers.Dropout(rate=0.05),
                        tf.keras.layers.Dense(1, activation=output_activation)
                    ])
                elif i < 4 or (has_mask and i < output_dimension // 2 + 4):  # Month_2-4（原始和累计）
                    tower = tf.keras.models.Sequential([
                        tf.keras.layers.Dense(
                            64,  # 减小网络规模，提高稳定性
                            activation="relu", 
                            kernel_regularizer=tf.keras.regularizers.l2(0.001)),
                        tf.keras.layers.BatchNormalization(),
                        tf.keras.layers.Dropout(rate=0.05),
                        tf.keras.layers.Dense(1, activation=output_activation)
                    ])
                else:  # Month_5-6（原始和累计）
                    tower = tf.keras.models.Sequential([
                        tf.keras.layers.Dense(
                            32,  # 减小网络规模，提高稳定性
                            activation="relu", 
                            kernel_regularizer=tf.keras.regularizers.l2(0.001)),
                        tf.keras.layers.BatchNormalization(),
                        tf.keras.layers.Dropout(rate=0.05),
                        tf.keras.layers.Dense(1, activation=output_activation)
                    ])
                self.Task_towers.append(tower)
        else:
            # 原始单任务模型
            self.Tower_layers = tf.keras.models.Sequential([
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),
                tf.keras.layers.Dense(
                    512, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),
                tf.keras.layers.Dense(
                    256, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.1),
                tf.keras.layers.Dense(output_dimension, activation=output_activation)
            ])
        
        if self.use_multitask and self.use_esmm:
            raise ValueError("Cannot use both multitask and ESMM modes simultaneously.")

        # Define layers based on mode
        if self.use_esmm:
            # ESMM specific layers (using DNN from esmm_model)
            # Define typical DNN hidden units for ESMM towers, can be configured
            esmm_dnn_hidden_units = model_config.get("esmm_dnn_hidden_units", (128, 64))
            esmm_dnn_dropout = model_config.get("esmm_dnn_dropout", 0.1)
            esmm_dnn_use_bn = model_config.get("esmm_dnn_use_bn", False)
            l2_reg_dnn = model_config.get("l2_reg", 0.0001) # Example, adjust as needed
            
            # Shared bottom could be simpler for ESMM or reuse parts
            # For simplicity, let's define a shared DNN here
            self.ESMM_shared_DNN = DNN(hidden_units=esmm_dnn_hidden_units,
                                        dropout_rate=esmm_dnn_dropout,
                                        use_bn=esmm_dnn_use_bn,
                                        l2_reg=l2_reg_dnn,
                                        name='esmm_shared_dnn')

            self.CTR_tower = DNN(hidden_units=esmm_dnn_hidden_units,
                                 dropout_rate=esmm_dnn_dropout,
                                 use_bn=esmm_dnn_use_bn,
                                 l2_reg=l2_reg_dnn,
                                 name='ctr_tower')
            self.CVR_tower = DNN(hidden_units=esmm_dnn_hidden_units,
                                 dropout_rate=esmm_dnn_dropout,
                                 use_bn=esmm_dnn_use_bn,
                                 l2_reg=l2_reg_dnn,
                                 name='cvr_tower')
            self.CTR_logit_layer = Dense(1, use_bias=False, name='ctr_logit')
            self.CVR_logit_layer = Dense(1, use_bias=False, name='cvr_logit')

        elif self.use_multitask:
            # Multitask specific layers (existing logic)
            self.output_dimension = model_config.get("output_dimension", 6)
        else:
            # 原始单任务模型
            self.Tower_layers = tf.keras.models.Sequential([
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),
                tf.keras.layers.Dense(
                    512, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.2),
                tf.keras.layers.Dense(
                    256, 
                    activation="relu", 
                    kernel_regularizer=tf.keras.regularizers.l2(0.005)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Dropout(rate=0.1),
                tf.keras.layers.Dense(output_dimension, activation=output_activation)
            ])
        
    def call(self, inputs):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor dictionary.
            
        Returns:
            Tensor: Model predictions.
        """
        # Process general (non-sequence) inputs
        embedding_general_features = []
        for feature_name in self.InputGeneral_features:
            feature_i = inputs[feature_name]
            embedding_i = self.Embedding_layers[feature_name](feature_i)
            embedding_general_features.append(embedding_i)
            
        # Concatenate general features if they exist
        if len(embedding_general_features) > 0:
            embedding_general_all = tf.keras.layers.Concatenate(axis=-1)(embedding_general_features)
        else:
            embedding_general_all = None
            
        # Process sequence inputs
        embedding_seqset_all = []
        for set_name in self.InputSeqSet_features:
            # Get features for this sequence set
            set_info = self.InputSeqSet_infos[set_name]
            set_features = set_info["features"]
            
            # Process each feature in the sequence set
            embedding_seq_features = []
            for feature_name in set_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_seq_features.append(embedding_i)
                
            # Concatenate sequence features within this set
            embedding_seq_all = tf.keras.layers.Concatenate(axis=-1)(embedding_seq_features)
            
            # Apply sequence processing (GRU + attention or just GRU)
            if self.use_time_attention:
                gru_output = self.Sequence_layers[set_name](embedding_seq_all)
                attention_output = self.TimeAttention_layers[set_name](gru_output)
                embedding_seqset_all.append(attention_output)
            else:
                sequence_output = self.Sequence_layers[set_name](embedding_seq_all)
                embedding_seqset_all.append(sequence_output)
                
        # Concatenate all sequence outputs if they exist
        if len(embedding_seqset_all) > 0:
            embedding_seqset_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_seqset_all)
        else:
            embedding_seqset_concat = None
            
        # Process scene features for weighting if they exist
        if len(self.InputScene_features) > 0:
            # Get scene features
            embedding_scene_features = []
            for feature_name in self.InputScene_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_scene_features.append(embedding_i)
                
            # Apply scene weighting
            scene_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_scene_features)
            scene_weight = self.Scene_layers(scene_concat)
            
            # Combine general and sequence features with scene weights
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
                embedding_all = embedding_all * scene_weight
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all * scene_weight[:, :self.InputGeneral_dimensions]
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat * scene_weight[:, self.InputGeneral_dimensions:]
            else:
                raise ValueError("No valid features to process")
        else:
            # No scene weighting, just concatenate features
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat
            else:
                raise ValueError("No valid features to process")
                
        # Apply feature crossing if enabled
        if self.use_cross_layer:
            embedding_cross = self.cross_layer(embedding_all)
            embedding_final = tf.keras.layers.Concatenate(axis=-1)([embedding_all, embedding_cross])
        else:
            embedding_final = embedding_all
            
        # --- ESMM Mode --- 
        if self.use_esmm:
            shared_output = self.ESMM_shared_DNN(embedding_final)
            
            # CTR (Test Drive) Tower
            ctr_output = self.CTR_tower(shared_output)
            ctr_logit = self.CTR_logit_layer(ctr_output)
            ctr_pred = tf.keras.layers.Activation('sigmoid', name='test_drive_output')(ctr_logit) # Output name for test drive
            
            # CVR (Purchase | Test Drive) Tower
            cvr_output = self.CVR_tower(shared_output)
            cvr_logit = self.CVR_logit_layer(cvr_output)
            cvr_pred = tf.keras.layers.Activation('sigmoid', name='purchase_given_test_drive')(cvr_logit) # Intermediate CVR 
            
            # CTCVR (Purchase) Prediction
            # Ensure stability by adding a small epsilon
            epsilon = tf.keras.backend.epsilon()
            ctcvr_pred = Multiply(name='purchase_output')([ctr_pred, cvr_pred]) # Final output name for purchase
            ctcvr_pred = tf.clip_by_value(ctcvr_pred, epsilon, 1.0 - epsilon)

            # Return dictionary of outputs instead of a list
            output = {
                'test_drive_output': ctr_pred,
                'purchase_output': ctcvr_pred
            }

        # --- Original Multitask Mode (Time-based) --- 
        elif self.use_multitask:
            # 共享特征提取
            shared_features = self.Shared_layers(embedding_final)
            
            # 任务特定预测
            task_outputs = []
            
            # 判断是否有mask_label
            has_mask = self.output_dimension > 6
            
            for i, tower in enumerate(self.Task_towers):
                # 为Month_1（原始和累计）添加额外的注意力机制
                if i == 0 or (has_mask and i == self.output_dimension // 2):
                    # 为Month_1添加额外的权重，增强特征
                    month1_features = shared_features * 1.5  # 增强Month_1的特征
                    task_output = tower(month1_features)
                else:
                    task_output = tower(shared_features)
                task_outputs.append(task_output)
            
            # 合并所有任务输出
            output = tf.concat(task_outputs, axis=1)
            
            # 确保输出满足单调性约束（后面月份的概率不小于前面月份）
            # 使用更稳定的方式实现单调性约束
            if has_mask:
                # 如果输出维度大于6，说明包含了原始标签和累计标签
                # 分别处理原始标签和累计标签
                original_output = output[:, :self.output_dimension//2]
                cumsum_output = output[:, self.output_dimension//2:]
                
                # 对原始标签应用单调性约束
                original_cummax = original_output[:, 0:1]
                original_outputs_list = [original_cummax]
                for i in range(1, original_output.shape[1]):
                    original_cummax = tf.maximum(original_cummax, original_output[:, i:i+1])
                    original_outputs_list.append(original_cummax)
                
                # 对累计标签应用单调性约束
                cumsum_cummax = cumsum_output[:, 0:1]
                cumsum_outputs_list = [cumsum_cummax]
                for i in range(1, cumsum_output.shape[1]):
                    cumsum_cummax = tf.maximum(cumsum_cummax, cumsum_output[:, i:i+1])
                    cumsum_outputs_list.append(cumsum_cummax)
                
                # 重新组合输出
                output = tf.concat([
                    tf.concat(original_outputs_list, axis=1),
                    tf.concat(cumsum_outputs_list, axis=1)
                ], axis=1)
            else:
                # 标准单调性约束
                cummax = output[:, 0:1]
                outputs_list = [cummax]
                
                for i in range(1, output.shape[1]):
                    cummax = tf.maximum(cummax, output[:, i:i+1])
                    outputs_list.append(cummax)
                
                output = tf.concat(outputs_list, axis=1)

        # --- Original Single Task Mode --- 
        else:
            # 原始单任务模型
            output = self.Tower_layers(embedding_final)
        
        return output

class EPMMOENet_TransformerModel(EPMMOENet_Model):
    """
    Enhanced version of EPMMOENet model using Transformer encoders for sequence processing.
    
    This model replaces the GRU layers with Transformer encoders to better capture
    long-range dependencies in sequential features.
    """
    
    def __init__(self, 
                model_config, 
                default_embedding_dimension=8, 
                default_transformer_dimension=64,
                num_heads=4,
                ff_dim=128,
                expert_num=8,
                use_cross_layer=True,
                use_multitask=False,
                use_esmm=False,
                use_mixed_precision=True,
                use_time_attention=True,
                time_decay_factor=0.05
                ):
        """
        Initialize the Transformer-based EPMMOENet model.
        
        Args:
            model_config (dict): Model configuration.
            default_embedding_dimension (int): Default embedding dimension.
            default_transformer_dimension (int): Default transformer model dimension.
            num_heads (int): Number of attention heads in transformer.
            ff_dim (int): Feed-forward dimension in transformer.
            expert_num (int): Number of experts for MoE layers.
            use_cross_layer (bool): Whether to use feature cross layer.
            use_multitask (bool): Whether to use multitask learning.
            use_esmm (bool): Whether to use ESMM model structure.
            use_mixed_precision (bool): Whether to use mixed precision training.
            use_time_attention (bool): Whether to use time decay attention.
            time_decay_factor (float): Time decay factor for attention.
        """
        # Initialize base model
        super().__init__(
            model_config=model_config,
            default_embedding_dimension=default_embedding_dimension,
            default_gru_dimension=default_transformer_dimension,  # Not used but keeps interface consistent
            expert_num=expert_num,
            use_cross_layer=use_cross_layer,
            use_multitask=use_multitask,
            use_esmm=use_esmm,
            use_mixed_precision=use_mixed_precision,
            use_time_attention=use_time_attention,
            time_decay_factor=time_decay_factor
        )
        
        # Store Transformer-specific parameters
        self.transformer_dimension = default_transformer_dimension
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        
        # Replace sequence processing layers with Transformer encoders
        self.Sequence_layers = {}
        
        # Create transformer encoding layers for each sequence set
        for set_name, set_info in self.InputSeqSet_infos.items():
            if set_name in self.InputSeqSet_features:
                transformer_dimension = set_info.get("transformer_dimension", default_transformer_dimension)
                
                # Create transformer encoder
                self.Sequence_layers[set_name] = TransformerEncoder(
                    d_model=transformer_dimension,
                    num_heads=num_heads,
                    ff_dim=ff_dim,
                    dropout_rate=0.1,
                    time_decay_factor=time_decay_factor,
                    use_positional_encoding=True
                )
                
                # We don't need TimeAttention_layers as the transformer handles attention internally
                # But we keep track of the dimensions for compatibility
                self.InputSeqSet_dimensions = 0
                for set_name, set_info in self.InputSeqSet_infos.items():
                    if set_name in self.InputSeqSet_features:
                        transformer_dimension = set_info.get("transformer_dimension", default_transformer_dimension)
                        self.InputSeqSet_dimensions += transformer_dimension
        
        self.logger.info(f"Initialized Transformer-based model with dimension {default_transformer_dimension} and {num_heads} heads")
    
    def call(self, inputs):
        """
        Forward pass calculation with transformer-based sequence processing.
        
        Args:
            inputs: Input tensor dictionary.
            
        Returns:
            Tensor: Model predictions.
        """
        # Process general (non-sequence) inputs
        embedding_general_features = []
        for feature_name in self.InputGeneral_features:
            feature_i = inputs[feature_name]
            embedding_i = self.Embedding_layers[feature_name](feature_i)
            embedding_general_features.append(embedding_i)
            
        # Concatenate general features if they exist
        if len(embedding_general_features) > 0:
            embedding_general_all = tf.keras.layers.Concatenate(axis=-1)(embedding_general_features)
        else:
            embedding_general_all = None
            
        # Process sequence inputs with transformers
        embedding_seqset_all = []
        for set_name in self.InputSeqSet_features:
            # Get features for this sequence set
            set_info = self.InputSeqSet_infos[set_name]
            set_features = set_info["features"]
            
            # Process each feature in the sequence set
            embedding_seq_features = []
            for feature_name in set_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_seq_features.append(embedding_i)
                
            # Concatenate sequence features within this set
            embedding_seq_all = tf.keras.layers.Concatenate(axis=-1)(embedding_seq_features)
            
            # Apply transformer encoding to sequence data
            # Transformer encoder already handles the time-aware attention internally
            transformer_output = self.Sequence_layers[set_name](embedding_seq_all)
            embedding_seqset_all.append(transformer_output)
                
        # Concatenate all sequence outputs if they exist
        if len(embedding_seqset_all) > 0:
            embedding_seqset_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_seqset_all)
        else:
            embedding_seqset_concat = None
            
        # Process scene features for weighting if they exist
        if len(self.InputScene_features) > 0:
            # Get scene features
            embedding_scene_features = []
            for feature_name in self.InputScene_features:
                feature_i = inputs[feature_name]
                embedding_i = self.Embedding_layers[feature_name](feature_i)
                embedding_scene_features.append(embedding_i)
                
            # Apply scene weighting
            scene_concat = tf.keras.layers.Concatenate(axis=-1)(embedding_scene_features)
            scene_weight = self.Scene_layers(scene_concat)
            
            # Combine general and sequence features with scene weights
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
                embedding_all = embedding_all * scene_weight
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all * scene_weight[:, :self.InputGeneral_dimensions]
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat * scene_weight[:, self.InputGeneral_dimensions:]
            else:
                raise ValueError("No valid features to process")
        else:
            # No scene weighting, just concatenate features
            if embedding_general_all is not None and embedding_seqset_concat is not None:
                embedding_all = tf.keras.layers.Concatenate(axis=-1)([
                    embedding_general_all, embedding_seqset_concat
                ])
            elif embedding_general_all is not None:
                embedding_all = embedding_general_all
            elif embedding_seqset_concat is not None:
                embedding_all = embedding_seqset_concat
            else:
                raise ValueError("No valid features to process")
                
        # Apply feature crossing if enabled
        if self.use_cross_layer:
            embedding_cross = self.cross_layer(embedding_all)
            embedding_final = tf.keras.layers.Concatenate(axis=-1)([embedding_all, embedding_cross])
        else:
            embedding_final = embedding_all
            
        # 应用预测层 - 复用父类中的多任务或单任务处理逻辑
        if self.use_multitask:
            # 共享特征提取
            shared_features = self.Shared_layers(embedding_final)
            
            # 任务特定预测
            task_outputs = []
            
            # 判断是否有mask_label
            has_mask = self.output_dimension > 6
            
            for i, tower in enumerate(self.Task_towers):
                # 为Month_1（原始和累计）添加额外的注意力机制
                if i == 0 or (has_mask and i == self.output_dimension // 2):
                    # 为Month_1添加额外的权重，增强特征
                    month1_features = shared_features * 1.5  # 增强Month_1的特征
                    task_output = tower(month1_features)
                else:
                    task_output = tower(shared_features)
                task_outputs.append(task_output)
            
            # 合并所有任务输出
            output = tf.concat(task_outputs, axis=1)
            
            # 确保输出满足单调性约束（后面月份的概率不小于前面月份）
            # 使用更稳定的方式实现单调性约束
            if has_mask:
                # 如果输出维度大于6，说明包含了原始标签和累计标签
                # 分别处理原始标签和累计标签
                original_output = output[:, :self.output_dimension//2]
                cumsum_output = output[:, self.output_dimension//2:]
                
                # 对原始标签应用单调性约束
                original_cummax = original_output[:, 0:1]
                original_outputs_list = [original_cummax]
                for i in range(1, original_output.shape[1]):
                    original_cummax = tf.maximum(original_cummax, original_output[:, i:i+1])
                    original_outputs_list.append(original_cummax)
                
                # 对累计标签应用单调性约束
                cumsum_cummax = cumsum_output[:, 0:1]
                cumsum_outputs_list = [cumsum_cummax]
                for i in range(1, cumsum_output.shape[1]):
                    cumsum_cummax = tf.maximum(cumsum_cummax, cumsum_output[:, i:i+1])
                    cumsum_outputs_list.append(cumsum_cummax)
                
                # 重新组合输出
                output = tf.concat([
                    tf.concat(original_outputs_list, axis=1),
                    tf.concat(cumsum_outputs_list, axis=1)
                ], axis=1)
            else:
                # 标准单调性约束
                cummax = output[:, 0:1]
                outputs_list = [cummax]
                
                for i in range(1, output.shape[1]):
                    cummax = tf.maximum(cummax, output[:, i:i+1])
                    outputs_list.append(cummax)
                
                output = tf.concat(outputs_list, axis=1)
        else:
            # 原始单任务模型
            output = self.Tower_layers(embedding_final)
        
        return output