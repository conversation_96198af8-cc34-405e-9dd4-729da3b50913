#!/bin/bash

echo "======== NIO 新车购买倾向预测模型数据分析流程 ========"
echo "开始时间: $(date)"

# 步骤1: 生成数据字典
echo -e "\n===== 步骤1: 生成数据字典 ====="
python src/data/dict_parse.py \
  --data_path="data/dataset_nio_new_car_v15/datetime=20240430" \
  --original_dict_path="src/configs/dataset_nio_new_car_v15.json" \
  --output_path="data/dict" \
  --visualize

# 步骤2: 分析数据字典
echo -e "\n===== 步骤2: 分析数据字典 ====="
python src/data/dict_analyze.py \
  --dict_path="data/dict/json/data_dictionary.json" \
  --output_csv="logs/feature_stats.csv"

# 步骤3: 运行数据分析
echo -e "\n===== 步骤3: 运行数据分析 ====="
python src/data/data_analyze.py

echo -e "\n===== 分析流程完成 ====="
echo "结束时间: $(date)"
echo "结果目录:"
echo "- 数据字典: data/dict/"
echo "- 特征统计: logs/feature_stats.csv"
echo "- 数据分析: src/data/data_analyze/"