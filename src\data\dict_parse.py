#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Description : NIO新车购买倾向专业数据字典生成工具
               基于现有数据和配置生成更完整的特征数据字典
"""
import pandas as pd
import numpy as np
import json
import os
from pathlib import Path
import logging
from typing import Dict, List, Optional, Union, Any
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

# For macOS
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
# For Linux 
# plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans'] 
plt.rcParams['axes.unicode_minus'] = False

# 添加自定义JSON编码器以处理NumPy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        return super(NumpyEncoder, self).default(obj)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataDictionaryGenerator:
    def __init__(self, 
                 data_path: str,
                 original_dict_path: str,
                 output_path: str = "data/dictionaries",
                 model_config_path: Optional[str] = None):
        """
        初始化数据字典生成器
        
        Args:
            data_path: 数据文件路径(parquet格式)
            original_dict_path: 原始数据字典JSON路径
            output_path: 输出目录
            model_config_path: 模型配置文件路径(可选)
        """
        self.data_path = data_path
        self.original_dict_path = original_dict_path
        self.output_path = output_path
        self.model_config_path = model_config_path
        
        # 创建输出目录
        Path(output_path).mkdir(parents=True, exist_ok=True)
        
        # 存储数据字典
        self.data_dictionary = {}
        
        # 业务领域映射
        self.domain_keywords = {
            "user_core": "用户核心属性",
            "user_car": "车辆交互行为",
            "app": "应用交互行为",
            "sales": "销售交互行为",
            "action": "用户行为序列",
            "seq": "序列特征"
        }
        
        # 特征类型推断规则
        self.type_inference_rules = {
            "cnt": "计数特征",
            "ratio": "比率特征",
            "days": "时间特征",
            "duration": "时长特征",
            "age": "人口特征", 
            "gender": "人口特征",
            "city": "地理特征",
            "price": "价格特征"
        }
        
    def load_data(self):
        """加载数据"""
        logger.info(f"加载数据: {self.data_path}")
        try:
            self.data = pd.read_parquet(self.data_path)
            logger.info(f"数据加载成功: {len(self.data)}行 x {len(self.data.columns)}列")
            
            # 排除非特征列
            exclude_cols = ['label', 'dataset', 'original_partition', 'user_id', 'datetime']
            self.feature_cols = [col for col in self.data.columns if col not in exclude_cols]
            logger.info(f"识别特征列: {len(self.feature_cols)}个")
            
            return True
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            return False
            
    def load_original_dictionary(self):
        """加载原始数据字典"""
        logger.info(f"加载原始数据字典: {self.original_dict_path}")
        try:
            with open(self.original_dict_path, 'r', encoding='utf-8') as f:
                self.original_dict = json.load(f)
            logger.info(f"原始数据字典加载成功: {len(self.original_dict)}个条目")
            return True
        except Exception as e:
            logger.error(f"原始数据字典加载失败: {e}")
            return False
            
    def load_model_config(self):
        """加载模型配置"""
        if not self.model_config_path:
            logger.warning("未提供模型配置路径")
            self.model_config = None
            return False
            
        logger.info(f"加载模型配置: {self.model_config_path}")
        try:
            with open(self.model_config_path, 'r', encoding='utf-8') as f:
                self.model_config = json.load(f)
            logger.info(f"模型配置加载成功")
            return True
        except Exception as e:
            logger.error(f"模型配置加载失败: {e}")
            self.model_config = None
            return False
    
    def infer_business_domain(self, feature_name: str) -> str:
        """推断特征所属业务领域"""
        for keyword, domain in self.domain_keywords.items():
            if keyword in feature_name:
                return domain
        return "其他"
        
    def infer_feature_group(self, feature_name: str) -> str:
        """推断特征所属特征组"""
        for keyword, group in self.type_inference_rules.items():
            if keyword in feature_name:
                return group
        
        # 检查是否为序列特征
        if feature_name.endswith('_seq') or 'seq' in feature_name:
            return "序列特征"
            
        return "其他特征"
        
    def compute_feature_statistics(self, feature_name: str) -> Dict:
        """计算特征统计信息"""
        feature_series = self.data[feature_name]
        
        # 检查数据类型
        dtype = str(feature_series.dtype)
        valid_count = len(feature_series)
        if valid_count == 0: # Handle empty series
            return {
                "count": 0, "missing_count": 0, "missing_rate": 0, "data_type": dtype,
                "actual_none_count": 0, "actual_none_rate": 0,
                "string_none_count": 0, "string_none_rate": 0,
                "empty_count": 0, "empty_rate": 0,
                "total_unusable_count": 0, "total_unusable_rate": 0,
                "unique_values": 0, "top_value": "N/A (Empty Series)"
            }

        # 1. NaN values
        is_na_series = feature_series.isna()
        missing_count = is_na_series.sum()

        # 2. Actual None objects (where not pd.isna())
        is_actual_none_series = feature_series.apply(lambda x: x is None if pd.notna(x) else False)
        actual_none_count = is_actual_none_series.sum()

        # 3. String "None" (case-insensitive, where not NaN and not actual None)
        is_string_none_series = feature_series.apply(
            lambda x: isinstance(x, str) and x.strip().lower() == 'none'
            if pd.notna(x) and x is not None
            else False
        )
        string_none_count = is_string_none_series.sum()

        # 4. Empty strings (where not NaN, not actual None, and not string "None")
        is_empty_string_series = feature_series.apply(
            lambda x: isinstance(x, str) and x.strip() == ''
            if pd.notna(x) and x is not None and not (isinstance(x, str) and x.strip().lower() == 'none')
            else False
        )
        empty_count = is_empty_string_series.sum()

        # Combined unusability
        is_unusable = is_na_series | is_actual_none_series | is_string_none_series | is_empty_string_series
        total_unusable_count = is_unusable.sum()
        
        statistics = {
            "count": valid_count,
            "data_type": dtype,
            "missing_count": missing_count,
            "missing_rate": (missing_count / valid_count) if valid_count > 0 else 0,
            "actual_none_count": actual_none_count,
            "actual_none_rate": (actual_none_count / valid_count) if valid_count > 0 else 0,
            "string_none_count": string_none_count,
            "string_none_rate": (string_none_count / valid_count) if valid_count > 0 else 0,
            "empty_count": empty_count,
            "empty_rate": (empty_count / valid_count) if valid_count > 0 else 0,
            "total_unusable_count": total_unusable_count,
            "total_unusable_rate": (total_unusable_count / valid_count) if valid_count > 0 else 0,
        }
        
        usable_feature_series = feature_series[~is_unusable]

        potential_numeric_series = pd.to_numeric(usable_feature_series, errors='coerce')
        is_effectively_numeric = pd.api.types.is_numeric_dtype(potential_numeric_series) and not potential_numeric_series.isna().all()

        if is_effectively_numeric:
            numeric_values_for_stats = potential_numeric_series.dropna()
            if not numeric_values_for_stats.empty:
                statistics.update({
                    "min": float(numeric_values_for_stats.min()),
                    "max": float(numeric_values_for_stats.max()),
                    "mean": float(numeric_values_for_stats.mean()),
                    "median": float(numeric_values_for_stats.median()),
                    "std": float(numeric_values_for_stats.std()),
                    "zero_rate": (numeric_values_for_stats == 0).mean() if len(numeric_values_for_stats) > 0 else 0
                })
                for q in [0.01, 0.05, 0.25, 0.75, 0.95, 0.99]:
                    statistics[f"percentile_{int(q*100)}"] = float(numeric_values_for_stats.quantile(q))
            statistics["effective_data_type"] = str(numeric_values_for_stats.dtype)
        
        # Use usable_feature_series for categorical/string stats
        if not usable_feature_series.empty:
            value_counts = usable_feature_series.value_counts()
            if not value_counts.empty:
                statistics.update({
                    "unique_values": len(value_counts),
                    "top_value": str(value_counts.index[0]),
                    "top_value_count": int(value_counts.iloc[0]),
                    "top_value_ratio": float(value_counts.iloc[0] / len(usable_feature_series)) if len(usable_feature_series) > 0 else 0
                })
                if len(value_counts) <= 20: # For display, show top few
                    top_n = min(5, len(value_counts))
                    statistics["top_values"] = [
                        {"value": str(value_counts.index[i]), 
                         "count": int(value_counts.iloc[i]),
                         "ratio": float(value_counts.iloc[i] / len(usable_feature_series)) if len(usable_feature_series) > 0 else 0}
                        for i in range(top_n)
                    ]
            else: # usable_feature_series was not empty, but value_counts was (e.g. all unique NaNs after to_numeric, though unlikely here)
                statistics["unique_values"] = 0
                statistics["top_value"] = "N/A (No Usable Values for Freq Count)"
        else: # All values were unusable
             statistics["unique_values"] = 0
             statistics["top_value"] = "N/A (All Values Unusable)"


        if feature_name.endswith('_seq') or ('seq' in feature_name and (pd.api.types.is_string_dtype(usable_feature_series) or usable_feature_series.dtype == 'object')):
            seq_lengths = usable_feature_series.loc[usable_feature_series.apply(lambda x: isinstance(x, str))].apply(
                lambda x: len(x.split(','))
            )
            if not seq_lengths.empty:
                statistics.update({
                    "is_sequence": True,
                    "seq_min_length": int(seq_lengths.min()),
                    "seq_max_length": int(seq_lengths.max()),
                    "seq_mean_length": float(seq_lengths.mean()),
                    "seq_median_length": float(seq_lengths.median()),
                    "non_empty_sequence_rate": float((seq_lengths > 0).mean()) if len(seq_lengths) > 0 else 0
                })
            elif not usable_feature_series.empty:
                 statistics["is_sequence"] = True 
                 statistics["non_empty_sequence_rate"] = 0.0
        
        return statistics
    
    def generate_dictionary(self):
        """生成数据字典"""
        if not hasattr(self, 'data') or not hasattr(self, 'original_dict'):
            raise ValueError("请先加载数据和原始数据字典")
            
        logger.info("开始生成数据字典...")
        
        # 处理模型配置中的特征信息
        model_features_info = {}
        if hasattr(self, 'model_config') and self.model_config:
            if 'RawFeature' in self.model_config:
                for feature_name, feature_config in self.model_config['RawFeature'].items():
                    model_features_info[feature_name] = feature_config
        
        # 遍历所有特征
        for feature_name in self.feature_cols:
            # 初始化特征字典
            feature_dict = {
                "name": feature_name,
                "technical_name": feature_name,
                "business_domain": self.infer_business_domain(feature_name),
                "feature_group": self.infer_feature_group(feature_name)
            }
            
            # 添加原始字典中的信息
            if feature_name in self.original_dict:
                orig_info = self.original_dict[feature_name]
                feature_dict.update({
                    "description": orig_info.get("feature_name", ""),
                    "base_value": orig_info.get("base_value", None),
                    "data_type": orig_info.get("data_type", "unknown"),
                    "min_value": orig_info.get("min_value", None),
                    "max_value": orig_info.get("max_value", None)
                })
            else:
                feature_dict.update({
                    "description": f"特征: {feature_name}",
                    "base_value": None,
                    "data_type": "unknown"
                })
            
            # 添加模型配置中的信息
            if feature_name in model_features_info:
                model_info = model_features_info[feature_name]
                feature_dict.update({
                    "feature_type": model_info.get("type", "table"),
                    "processor": model_info.get("dtype", "Unknown"),
                    "embedding_dim": model_info.get("embedding_dim", None),
                    "vocab_size": model_info.get("vocab_size", None)
                })
                
                # 添加特征处理方法
                if "dtype" in model_info:
                    if model_info["dtype"] == "StringLookup":
                        feature_dict["processing_method"] = "类别编码"
                    elif model_info["dtype"] == "Bucket":
                        feature_dict["processing_method"] = "数值分桶"
                    elif model_info["dtype"] == "Dense":
                        feature_dict["processing_method"] = "密集向量"
                    else:
                        feature_dict["processing_method"] = model_info["dtype"]
            
            # 计算统计信息
            try:
                statistics = self.compute_feature_statistics(feature_name)
                feature_dict["statistics"] = statistics
            except Exception as e:
                logger.warning(f"计算特征 {feature_name} 的统计信息时出错: {e}")
            
            # 存储特征字典
            self.data_dictionary[feature_name] = feature_dict
            
        logger.info(f"数据字典生成完成，共{len(self.data_dictionary)}个特征")
        
        return self.data_dictionary
        
    def save_dictionary(self, formats=["json", "markdown", "html"]):
        """保存数据字典"""
        if not hasattr(self, 'data_dictionary'):
            raise ValueError("请先生成数据字典")
            
        # 确保输出目录存在
        os.makedirs(self.output_path, exist_ok=True)
        
        # 保存为JSON
        if "json" in formats:
            json_dir = os.path.join(self.output_path, "json")
            os.makedirs(json_dir, exist_ok=True)
            json_path = f"{json_dir}/data_dictionary.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.data_dictionary, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
            logger.info(f"数据字典已保存为JSON: {json_path}")
        
        # 保存为Markdown
        if "markdown" in formats:
            md_dir = os.path.join(self.output_path, "markdown")
            os.makedirs(md_dir, exist_ok=True)
            md_path = f"{md_dir}/data_dictionary.md"
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write("# NIO新车购买倾向预测模型数据字典\n\n")
                
                # 按业务领域分组
                domains = defaultdict(list)
                for feature_name, feature_info in self.data_dictionary.items():
                    domains[feature_info.get("business_domain", "其他")].append(feature_name)
                
                # 写入目录
                f.write("## 目录\n\n")
                for domain, features in domains.items():
                    f.write(f"- [{domain}](#{domain.replace(' ', '-')})\n")
                f.write("\n")
                
                # 写入各领域特征
                for domain, features in domains.items():
                    f.write(f"## {domain}\n\n")
                    
                    for feature_name in sorted(features):
                        feature_info = self.data_dictionary[feature_name]
                        f.write(f"### {feature_name}\n\n")
                        
                        # 基本信息
                        f.write("**基本信息**\n\n")
                        f.write(f"- **中文名称**: {feature_info.get('description', '')}\n")
                        f.write(f"- **业务领域**: {feature_info.get('business_domain', '其他')}\n")
                        f.write(f"- **特征分组**: {feature_info.get('feature_group', '其他')}\n")
                        f.write(f"- **数据类型**: {feature_info.get('data_type', 'unknown')}\n")
                        
                        # 处理方法
                        if "processor" in feature_info:
                            f.write(f"- **处理方法**: {feature_info.get('processing_method', feature_info.get('processor', '未知'))}\n")
                        
                        # 特征类型
                        if "feature_type" in feature_info:
                            f.write(f"- **特征类型**: {feature_info.get('feature_type', 'table')}\n")
                        
                        # 默认值
                        if feature_info.get("base_value") is not None:
                            f.write(f"- **默认值**: {feature_info.get('base_value')}\n")
                        
                        f.write("\n")
                        
                        # 统计信息
                        if "statistics" in feature_info:
                            stats = feature_info["statistics"]
                            f.write("**统计信息**\n\n")
                            
                            # 缺失值信息
                            f.write(f"- **样本数**: {stats.get('count', 0)}\n")
                            f.write(f"- **缺失率**: {stats.get('missing_rate', 0) * 100:.2f}%\n")
                            f.write(f"- **Actual None值率**: {stats.get('actual_none_rate', 0) * 100:.2f}%\n")
                            f.write(f"- **String 'None'值率**: {stats.get('string_none_rate', 0) * 100:.2f}%\n")
                            f.write(f"- **空值率**: {stats.get('empty_rate', 0) * 100:.2f}%\n")
                            f.write(f"- **总无效率**: {stats.get('total_unusable_rate', 0) * 100:.2f}%\n")
                            
                            # 对于数值型特征
                            if "mean" in stats:
                                f.write(f"- **最小值**: {stats.get('min', 0)}\n")
                                f.write(f"- **最大值**: {stats.get('max', 0)}\n")
                                f.write(f"- **均值**: {stats.get('mean', 0):.4f}\n")
                                f.write(f"- **中位数**: {stats.get('median', 0):.4f}\n")
                                f.write(f"- **标准差**: {stats.get('std', 0):.4f}\n")
                                f.write(f"- **零值率**: {stats.get('zero_rate', 0) * 100:.2f}%\n")
                            
                            # 对于类别型特征
                            if "unique_values" in stats:
                                f.write(f"- **唯一值数量**: {stats.get('unique_values', 0)}\n")
                                f.write(f"- **最常见值**: {stats.get('top_value', '')}\n")
                                f.write(f"- **最常见值比例**: {stats.get('top_value_ratio', 0) * 100:.2f}%\n")
                            
                            # 对于序列特征
                            if "is_sequence" in stats:
                                f.write(f"- **序列最小长度**: {stats.get('seq_min_length', 0)}\n")
                                f.write(f"- **序列最大长度**: {stats.get('seq_max_length', 0)}\n")
                                f.write(f"- **序列平均长度**: {stats.get('seq_mean_length', 0):.2f}\n")
                                f.write(f"- **非空序列比例**: {stats.get('non_empty_sequence_rate', 0) * 100:.2f}%\n")
                            
                            f.write("\n")
                        
                        # 分隔线
                        f.write("---\n\n")
            
            logger.info(f"数据字典已保存为Markdown: {md_path}")
        
        # 保存为HTML
        if "html" in formats:
            # 使用pandas生成HTML表格
            features_list = []
            for idx, (feature_name, feature_info) in enumerate(self.data_dictionary.items(), 1):
                feature_row = {
                    "序号": idx,
                    "特征名": feature_name,
                    "中文名称": feature_info.get("description", ""),
                    "业务领域": feature_info.get("business_domain", "其他"),
                    "特征分组": feature_info.get("feature_group", "其他"),
                    "数据类型": feature_info.get("data_type", "unknown")
                }
                
                # 添加处理方法
                if "processor" in feature_info:
                    feature_row["处理方法"] = feature_info.get("processing_method", feature_info.get("processor", "未知"))
                
                # 添加统计信息
                if "statistics" in feature_info:
                    stats = feature_info["statistics"]
                    feature_row["缺失率"] = f"{stats.get('missing_rate', 0) * 100:.2f}%"
                    feature_row["Actual None值率"] = f"{stats.get('actual_none_rate', 0) * 100:.2f}%"
                    feature_row["String 'None'值率"] = f"{stats.get('string_none_rate', 0) * 100:.2f}%"
                    feature_row["空值率"] = f"{stats.get('empty_rate', 0) * 100:.2f}%"
                    feature_row["总无效率"] = f"{stats.get('total_unusable_rate', 0) * 100:.2f}%"
                    
                    is_numeric_display = "mean" in stats or \
                                         ("effective_data_type" in stats and \
                                          any(t in stats["effective_data_type"] for t in ["float", "int"]))

                    if is_numeric_display:
                        feature_row["最小值"] = f"{stats.get('min', '-'):.4f}" if isinstance(stats.get('min'), (int, float)) else "-"
                        feature_row["最大值"] = f"{stats.get('max', '-'):.4f}" if isinstance(stats.get('max'), (int, float)) else "-"
                        feature_row["均值"] = f"{stats.get('mean', '-'):.4f}" if isinstance(stats.get('mean'), (int, float)) else "-"
                    else:
                        feature_row["最小值"] = "-"
                        feature_row["最大值"] = "-"
                        feature_row["均值"] = "-"
                    
                    feature_row["唯一值数"] = stats.get("unique_values", "-")
                    feature_row["最常见值"] = stats.get("top_value", "-")

                features_list.append(feature_row)
            
            column_order = [
                "序号", "特征名", "中文名称", "业务领域", "特征分组", "数据类型",
                "缺失率", "Actual None值率", "String 'None'值率", "空值率", "总无效率",
                "唯一值数", "最常见值", "最小值", "最大值", "均值"
            ]
            if features_list: # Check if list is not empty
                 # Dynamically add "处理方法" if it exists in the first item, as a proxy for all items
                if "处理方法" in features_list[0] and "处理方法" not in column_order:
                     column_order.insert(6, "处理方法")


            features_df = pd.DataFrame(features_list)
            # Ensure all columns in column_order exist in features_df, add if missing with default value (e.g. '-')
            for col in column_order:
                if col not in features_df.columns:
                    features_df[col] = "-" # Or appropriate default
            
            features_df = features_df[column_order] # Enforce order and selection


            html_dir = os.path.join(self.output_path, "html")
            os.makedirs(html_dir, exist_ok=True)
            html_path = f"{html_dir}/data_dictionary.html"
            
            # 生成基本HTML
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>NIO新车购买倾向预测模型数据字典</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #333366; }}
                    table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                    th {{ background-color: #f2f2f2; text-align: left; padding: 8px; position: sticky; top: 0; }}
                    td {{ border: 1px solid #ddd; padding: 8px; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    .quality-warning {{ background-color: #ffeeee; }}
                    .quality-alert {{ background-color: #ffdddd; }}
                </style>
                <script>
                    window.onload = function() {{
                        // 突出显示存在数据质量问题的行
                        const rows = document.querySelectorAll('table tr');
                        for(let i=1; i<rows.length; i++) {{
                            const cells = rows[i].querySelectorAll('td');
                            const totalUnusableCell = rows[i].querySelector('td:nth-child(11)');
                            if(totalUnusableCell) {{
                                const unusableRate = parseFloat(totalUnusableCell.textContent);
                                if(unusableRate > 80) {{
                                    rows[i].classList.add('quality-alert');
                                }} else if(unusableRate > 50) {{
                                    rows[i].classList.add('quality-warning');
                                }}
                            }}
                        }}
                    }};
                </script>
            </head>
            <body>
                <h1>NIO新车购买倾向预测模型数据字典</h1>
                <p><strong>数据质量说明：</strong></p>
                <ul>
                    <li><strong>缺失率 (Missing Rate)</strong>：数据中实际 `NaN` (Not a Number) 值所占的比例。这是 Pandas/NumPy 中标准的缺失数据表示。</li>
                    <li><strong>Actual None值率 (Actual None Rate)</strong>：数据中 Python `None` 对象（非 `NaN`）所占的比例。这些是显式赋的空值。</li>
                    <li><strong>String 'None'值率 (String 'None' Rate)</strong>：数据中字符串值为 "None" (或 "none", "NONE" 等，忽略大小写，非实际 `None` 或 `NaN`）所占的比例。这通常表示上游数据处理中 `None` 值被错误地转换成了字符串。</li>
                    <li><strong>空值率 (Empty Rate)</strong>：数据中字符串值为空字符串（例如 `""`，不包括字符串 "None"）所占的比例。</li>
                    <li><strong>总无效率 (Total Unusable Rate)</strong>：上述所有被视为无效或空的数据类型（包括 `NaN`、实际 `None` 对象、字符串 "None" 以及空字符串）的总和占特征总记录数的比例。计算方式：`(NaN数量 + Actual None数量 + String 'None'数量 + 空字符串数量) / 总记录数 * 100%`。这是衡量特征整体数据质量的关键指标。</li>
                </ul>
                <p><strong>其他关键统计指标说明：</strong></p>
                <ul>
                    <li><strong>唯一值数 (Unique Value Count)</strong>：在排除了所有上述"无效/空"数据后，特征中不同值的数量。对于类别型特征，它表示基数；对于数值型特征，极低的数量可能表示数据是离散的或已被分箱。</li>
                    <li><strong>最常见值 (Most Common Value)</strong>：在排除了所有"无效/空"数据后，出现频率最高的值。</li>
                    <li><strong>最小值 (Min Value)</strong>：仅针对可被有效解析为数值型的特征（在排除无效值后），计算其最小值。对于非数值型特征，显示为 "-"。</li>
                    <li><strong>最大值 (Max Value)</strong>：仅针对可被有效解析为数值型的特征（在排除无效值后），计算其最大值。对于非数值型特征，显示为 "-"。</li>
                    <li><strong>均值 (Mean Value)</strong>：仅针对可被有效解析为数值型的特征（在排除无效值后），计算其算术平均值。对于非数值型特征，显示为 "-"。</li>
                </ul>
                {features_df.to_html(index=False, na_rep='-')}
            </body>
            </html>
            """
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.info(f"数据字典已保存为HTML: {html_path}")
        
        return True
    
    def generate_feature_visualizations(self):
        """生成特征可视化"""
        if not hasattr(self, 'data_dictionary'):
            raise ValueError("请先生成数据字典")
            
        # 创建可视化目录
        vis_dir = f"{self.output_path}/visualizations"
        os.makedirs(vis_dir, exist_ok=True)
        
        logger.info("开始生成特征可视化...")
        
        # 选择样本数据（如果数据太多）
        sample_size = min(10000, len(self.data))
        data_sample = self.data.sample(sample_size, random_state=42)
        
        # 按特征类型分组可视化
        numeric_features = []
        categorical_features = []
        sequence_features = []
        
        for feature_name, feature_info in self.data_dictionary.items():
            # 跳过不在样本中的特征
            if feature_name not in data_sample.columns:
                continue
                
            try:
                # 分类特征
                if "statistics" in feature_info and "unique_values" in feature_info["statistics"]:
                    if feature_info["statistics"].get("unique_values", 0) <= 20:
                        categorical_features.append(feature_name)
                # 数值特征
                elif "statistics" in feature_info and "mean" in feature_info["statistics"]:
                    numeric_features.append(feature_name)
                # 序列特征
                elif "statistics" in feature_info and "is_sequence" in feature_info["statistics"]:
                    sequence_features.append(feature_name)
            except Exception as e:
                logger.warning(f"处理特征 {feature_name} 时出错: {e}")
        
        # 可视化数值特征
        for i, feature_name in enumerate(numeric_features[:20]):  # 只可视化前20个
            try:
                plt.figure(figsize=(10, 6))
                sns.histplot(data_sample[feature_name].dropna(), kde=True)
                plt.title(f"特征分布: {feature_name}")
                plt.xlabel(feature_name)
                plt.ylabel("频数")
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_hist.png")
                plt.close()
                
                # 添加箱线图
                plt.figure(figsize=(10, 6))
                sns.boxplot(x=data_sample[feature_name].dropna())
                plt.title(f"箱线图: {feature_name}")
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_box.png")
                plt.close()
                
                logger.info(f"已生成数值特征 {feature_name} 的可视化")
            except Exception as e:
                logger.warning(f"可视化数值特征 {feature_name} 时出错: {e}")
        
        # 可视化类别特征
        for i, feature_name in enumerate(categorical_features[:20]):  # 只可视化前20个
            try:
                plt.figure(figsize=(12, 6))
                value_counts = data_sample[feature_name].value_counts().sort_values(ascending=False)
                top_n = min(10, len(value_counts))
                
                # 绘制前N个最常见值的条形图
                value_counts.iloc[:top_n].plot(kind='bar')
                plt.title(f"类别分布: {feature_name}")
                plt.xlabel("类别值")
                plt.ylabel("频数")
                plt.xticks(rotation=45, ha='right')
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_bar.png")
                plt.close()
                
                # 绘制饼图
                plt.figure(figsize=(10, 8))
                if len(value_counts) > top_n:
                    # 如果类别太多，将非top_n的合并为"其他"
                    others_sum = value_counts.iloc[top_n:].sum()
                    pie_data = value_counts.iloc[:top_n].copy()
                    pie_data["其他"] = others_sum
                    pie_data.plot(kind='pie', autopct='%1.1f%%')
                else:
                    value_counts.plot(kind='pie', autopct='%1.1f%%')
                
                plt.title(f"类别占比: {feature_name}")
                plt.ylabel("")  # 移除y标签
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_pie.png")
                plt.close()
                
                logger.info(f"已生成类别特征 {feature_name} 的可视化")
            except Exception as e:
                logger.warning(f"可视化类别特征 {feature_name} 时出错: {e}")
        
        # 可视化序列特征
        for i, feature_name in enumerate(sequence_features[:10]):  # 只可视化前10个
            try:
                # 获取序列长度分布
                seq_lengths = data_sample[feature_name].dropna().apply(
                    lambda x: len(str(x).split(',')) if isinstance(x, str) else 0
                )
                
                plt.figure(figsize=(10, 6))
                sns.histplot(seq_lengths, kde=True)
                plt.title(f"序列长度分布: {feature_name}")
                plt.xlabel("序列长度")
                plt.ylabel("频数")
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_length_hist.png")
                plt.close()
                
                # 绘制非空序列比例
                plt.figure(figsize=(8, 8))
                non_empty = (seq_lengths > 0).sum()
                empty = len(seq_lengths) - non_empty
                plt.pie([non_empty, empty], labels=['非空序列', '空序列'], autopct='%1.1f%%')
                plt.title(f"序列非空比例: {feature_name}")
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/{feature_name}_empty_pie.png")
                plt.close()
                
                logger.info(f"已生成序列特征 {feature_name} 的可视化")
            except Exception as e:
                logger.warning(f"可视化序列特征 {feature_name} 时出错: {e}")
        
        # 生成特征相关性热图 (只针对数值特征)
        if len(numeric_features) >= 2:
            try:
                # 选择前20个数值特征
                corr_features = numeric_features[:20]
                corr_data = data_sample[corr_features].copy()
                
                # 计算相关性矩阵
                corr_matrix = corr_data.corr()
                
                # 绘制热图
                plt.figure(figsize=(15, 12))
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
                plt.title("特征相关性热图")
                plt.tight_layout()
                plt.savefig(f"{vis_dir}/correlation_heatmap.png")
                plt.close()
                
                logger.info("已生成特征相关性热图")
            except Exception as e:
                logger.warning(f"生成相关性热图时出错: {e}")
        
        # 生成综合报告
        try:
            with open(f"{vis_dir}/visualization_report.html", 'w', encoding='utf-8') as f:
                f.write("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>特征可视化报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1, h2 { color: #333366; }
                        .feature-section { margin-bottom: 30px; }
                        .feature-images { display: flex; flex-wrap: wrap; }
                        .feature-image { margin: 10px; }
                        img { max-width: 400px; border: 1px solid #ddd; }
                    </style>
                </head>
                <body>
                    <h1>特征可视化报告</h1>
                """)
                
                # 添加数值特征
                if numeric_features:
                    f.write("<h2>数值特征</h2>\n")
                    for i, feature_name in enumerate(numeric_features[:20]):
                        f.write(f'<div class="feature-section">\n')
                        f.write(f'<h3>{feature_name}</h3>\n')
                        f.write(f'<div class="feature-images">\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_hist.png" /><p>直方图</p></div>\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_box.png" /><p>箱线图</p></div>\n')
                        f.write(f'</div>\n</div>\n')
                
                # 添加类别特征
                if categorical_features:
                    f.write("<h2>类别特征</h2>\n")
                    for i, feature_name in enumerate(categorical_features[:20]):
                        f.write(f'<div class="feature-section">\n')
                        f.write(f'<h3>{feature_name}</h3>\n')
                        f.write(f'<div class="feature-images">\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_bar.png" /><p>条形图</p></div>\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_pie.png" /><p>饼图</p></div>\n')
                        f.write(f'</div>\n</div>\n')
                
                # 添加序列特征
                if sequence_features:
                    f.write("<h2>序列特征</h2>\n")
                    for i, feature_name in enumerate(sequence_features[:10]):
                        f.write(f'<div class="feature-section">\n')
                        f.write(f'<h3>{feature_name}</h3>\n')
                        f.write(f'<div class="feature-images">\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_length_hist.png" /><p>长度分布</p></div>\n')
                        f.write(f'<div class="feature-image"><img src="{feature_name}_empty_pie.png" /><p>非空比例</p></div>\n')
                        f.write(f'</div>\n</div>\n')
                
                # 添加相关性热图
                if len(numeric_features) >= 2:
                    f.write("<h2>特征相关性</h2>\n")
                    f.write(f'<div class="feature-section">\n')
                    f.write(f'<div class="feature-images">\n')
                    f.write(f'<div class="feature-image"><img src="correlation_heatmap.png" /><p>相关性热图</p></div>\n')
                    f.write(f'</div>\n</div>\n')
                
                f.write("</body>\n</html>")
                
            logger.info(f"可视化报告已生成: {vis_dir}/visualization_report.html")
        except Exception as e:
            logger.warning(f"生成可视化报告时出错: {e}")
            
        return True

if __name__ == "__main__":
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='NIO新车购买倾向专业数据字典生成工具')
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径(parquet格式)')
    parser.add_argument('--original_dict_path', type=str, required=True, help='原始数据字典JSON路径')
    parser.add_argument('--output_path', type=str, default="data/dictionaries", help='输出目录')
    parser.add_argument('--model_config_path', type=str, default=None, help='模型配置文件路径(可选)')
    parser.add_argument('--formats', nargs='+', default=["json", "markdown", "html"], help='输出格式')
    parser.add_argument('--visualize', action='store_true', help='是否生成可视化')
    
    args = parser.parse_args()
    
    # 创建数据字典生成器
    generator = DataDictionaryGenerator(
        data_path=args.data_path,
        original_dict_path=args.original_dict_path,
        output_path=args.output_path,
        model_config_path=args.model_config_path
    )
    
    # 加载数据和配置
    generator.load_data()
    generator.load_original_dictionary()
    if args.model_config_path:
        generator.load_model_config()
    
    # 生成数据字典
    generator.generate_dictionary()
    
    # 保存数据字典
    generator.save_dictionary(formats=args.formats)
    
    # 生成可视化
    if args.visualize:
        generator.generate_feature_visualizations()
    
    logger.info("数据字典生成完成")