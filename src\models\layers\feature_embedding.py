"""
特征嵌入处理模块
"""
import tensorflow as tf
from tensorflow.keras.layers import Embedding, Dense, Input, Flatten, Concatenate

def create_embedding_inputs(raw_features):
    """
    为特征创建输入层和嵌入输入
    
    Args:
        raw_features (dict): 原始特征配置
        
    Returns:
        tuple: (输入字典, 特征字典, 数值特征字典)
    """
    inputs = {}
    categorical_inputs = {}
    numerical_inputs = {}
    
    # 处理特征输入
    for feature_name, feature_info in raw_features.items():
        feature_type = feature_info.get("type", "table")
        feature_dtype = feature_info.get("dtype", "StringLookup")
        
        # 类别特征
        if feature_dtype in ["Embedding", "StringLookup", "IntegerLookup"]:
            # 创建输入层
            inputs[feature_name] = Input(shape=(1,), name=f"input_{feature_name}")
            categorical_inputs[feature_name] = inputs[feature_name]
        # 数值特征
        elif feature_dtype == "Dense":
            inputs[feature_name] = Input(shape=(1,), name=f"input_{feature_name}")
            numerical_inputs[feature_name] = inputs[feature_name]
            
    return inputs, categorical_inputs, numerical_inputs

def build_embedding_network(categorical_inputs, raw_features, default_embedding_dim=8):
    """
    为类别特征构建嵌入网络
    
    Args:
        categorical_inputs (dict): 类别输入字典
        raw_features (dict): 原始特征配置
        default_embedding_dim (int): 默认嵌入维度
        
    Returns:
        list: 嵌入向量列表
    """
    embedding_list = []
    
    # 为每个类别特征创建嵌入层
    for feature_name, input_tensor in categorical_inputs.items():
        feature_info = raw_features.get(feature_name, {})
        vocab_size = feature_info.get("vocab_size", 100)
        embedding_dim = feature_info.get("embedding_dim", default_embedding_dim)
        
        # 创建嵌入层
        embedding = Embedding(
            input_dim=vocab_size,
            output_dim=embedding_dim,
            name=f"embedding_{feature_name}"
        )(input_tensor)
        
        # 展平嵌入
        embedding = Flatten()(embedding)
        embedding_list.append(embedding)
        
    return embedding_list 