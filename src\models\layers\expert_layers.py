"""
专家网络层模块
"""
import tensorflow as tf
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization

def create_expert_layers(num_experts, hidden_units=[64, 32], activation='relu', dropout_rate=0.1, use_bn=True):
    """
    创建多个专家网络层
    
    Args:
        num_experts (int): 专家数量
        hidden_units (list): 每个专家的隐藏单元数列表
        activation (str): 激活函数
        dropout_rate (float): Dropout比率
        use_bn (bool): 是否使用批归一化
        
    Returns:
        list: 专家网络层列表
    """
    expert_layers = []
    
    # 创建每个专家网络
    for i in range(num_experts):
        expert = []
        
        # 创建每个专家的多层网络
        for j, units in enumerate(hidden_units):
            # 创建全连接层
            dense = Dense(
                units, 
                activation=activation, 
                name=f"expert_{i}_dense_{j}"
            )
            expert.append(dense)
            
            # 添加批归一化（如果启用）
            if use_bn:
                bn = BatchNormalization(name=f"expert_{i}_bn_{j}")
                expert.append(bn)
                
            # 添加Dropout（如果启用）
            if dropout_rate > 0:
                dropout = Dropout(dropout_rate, name=f"expert_{i}_dropout_{j}")
                expert.append(dropout)
        
        # 创建顺序模型
        expert_model = tf.keras.Sequential(expert, name=f"expert_{i}")
        expert_layers.append(expert_model)
    
    return expert_layers 