import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse

def load_metrics(model_dir, metrics_file="sample_20250311_v7-20250311_metrics.json"):
    """加载模型的评估指标"""
    metrics_path = os.path.join(model_dir, metrics_file)
    with open(metrics_path, 'r') as f:
        metrics = json.load(f)
    return metrics

def compare_models(baseline_dir, improved_dir, output_dir=None):
    """比较两个模型的性能"""
    # 加载基线模型和改进模型的指标
    baseline_metrics = load_metrics(baseline_dir)
    improved_metrics = load_metrics(improved_dir)
    
    # 创建对比结果表格
    results = []
    
    # 比较整体评估指标
    for metric in ["ROC_AUC", "PR_AUC", "Precision@840", "Recall@840"]:
        baseline_value = baseline_metrics["metrics_evaluate"][metric]
        improved_value = improved_metrics["metrics_evaluate"][metric]
        change = (improved_value - baseline_value) / baseline_value * 100
        
        results.append({
            "Metric": f"Overall {metric}",
            "Baseline": baseline_value,
            "Improved": improved_value,
            "Change (%)": change
        })
    
    # 比较月度评估指标，重点关注召回率
    for month in range(1, 7):
        month_key = f"Month_{month}"
        baseline_recall = baseline_metrics["metrics_month_evaluate"][month_key]["Recall@840"]
        improved_recall = improved_metrics["metrics_month_evaluate"][month_key]["Recall@840"]
        change = (improved_recall - baseline_recall) / baseline_recall * 100
        
        results.append({
            "Metric": f"{month_key} Recall@840",
            "Baseline": baseline_recall,
            "Improved": improved_recall,
            "Change (%)": change
        })
    
    # 转换为DataFrame并输出
    df_results = pd.DataFrame(results)
    print("\n模型性能对比:")
    print(df_results)
    
    # 绘制召回率对比图
    plt.figure(figsize=(10, 6))
    months = [f"Month_{i}" for i in range(1, 7)]
    
    baseline_recalls = [baseline_metrics["metrics_month_evaluate"][m]["Recall@840"] for m in months]
    improved_recalls = [improved_metrics["metrics_month_evaluate"][m]["Recall@840"] for m in months]
    
    x = np.arange(len(months))
    width = 0.35
    
    plt.bar(x - width/2, baseline_recalls, width, label='Baseline')
    plt.bar(x + width/2, improved_recalls, width, label='Improved')
    
    plt.xlabel('Month')
    plt.ylabel('Recall@840')
    plt.title('Recall Comparison by Month')
    plt.xticks(x, months)
    plt.legend()
    
    # 保存结果
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        df_results.to_csv(os.path.join(output_dir, "model_comparison.csv"), index=False)
        plt.savefig(os.path.join(output_dir, "recall_comparison.png"))
    
    plt.show()
    
    return df_results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Compare performance of two conversion rate prediction models")
    parser.add_argument("--baseline_dir", required=True, help="Path to baseline model evaluation directory")
    parser.add_argument("--improved_dir", required=True, help="Path to improved model evaluation directory")
    parser.add_argument("--output_dir", default="comparison_results", help="Directory to save comparison results")
    
    args = parser.parse_args()
    
    compare_models(
        baseline_dir=args.baseline_dir,
        improved_dir=args.improved_dir,
        output_dir=args.output_dir
    ) 