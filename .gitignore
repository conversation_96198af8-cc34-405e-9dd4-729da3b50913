.venv/
logs/
src/data/data_analyze/
data/dict/
embedding/enhanced_model_output_config_test/
embedding/retrieval_test_output/
embedding/training_output/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
src/evaluation/**/
!src/evaluation/__init__.py
!src/evaluation/evaluator.py