======== NIO 新车购买倾向预测模型数据分析流程 ========
开始时间: Tue May  6 20:16:59 CST 2025
-e 
===== 步骤1: 生成数据字典 =====
2025-05-06 20:17:00,710 - __main__ - INFO - 加载数据: data/dataset_nio_new_car_v15/datetime=20240430
2025-05-06 20:17:01,091 - __main__ - INFO - 数据加载成功: 56464行 x 513列
2025-05-06 20:17:01,091 - __main__ - INFO - 识别特征列: 511个
2025-05-06 20:17:01,091 - __main__ - INFO - 加载原始数据字典: src/configs/dataset_nio_new_car_v15.json
2025-05-06 20:17:01,092 - __main__ - INFO - 原始数据字典加载成功: 495个条目
2025-05-06 20:17:01,092 - __main__ - INFO - 开始生成数据字典...
2025-05-06 20:17:21,652 - __main__ - WARNING - 计算特征 mask_label 的统计信息时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-05-06 20:17:21,834 - __main__ - INFO - 数据字典生成完成，共511个特征
2025-05-06 20:17:21,851 - __main__ - INFO - 数据字典已保存为JSON: data/dict/json/data_dictionary.json
2025-05-06 20:17:21,853 - __main__ - INFO - 数据字典已保存为Markdown: data/dict/markdown/data_dictionary.md
2025-05-06 20:17:21,879 - __main__ - INFO - 数据字典已保存为HTML: data/dict/html/data_dictionary.html
2025-05-06 20:17:21,880 - __main__ - INFO - 开始生成特征可视化...
2025-05-06 20:17:22,105 - __main__ - INFO - 已生成类别特征 fellow_follow_1d_cnt 的可视化
2025-05-06 20:17:22,190 - __main__ - INFO - 已生成类别特征 fellow_follow_7d_cnt 的可视化
2025-05-06 20:17:22,259 - __main__ - INFO - 已生成类别特征 answer_sales_call_duration_s_1d_cnt 的可视化
2025-05-06 20:17:22,328 - __main__ - INFO - 已生成类别特征 user_core_user_gender 的可视化
2025-05-06 20:17:22,400 - __main__ - INFO - 已生成类别特征 user_core_user_age_group 的可视化
2025-05-06 20:17:22,462 - __main__ - INFO - 已生成类别特征 user_core_is_nio_employee 的可视化
2025-05-06 20:17:22,528 - __main__ - INFO - 已生成类别特征 user_core_pred_marriage_status 的可视化
2025-05-06 20:17:22,594 - __main__ - INFO - 已生成类别特征 user_core_pred_has_children 的可视化
2025-05-06 20:17:22,661 - __main__ - INFO - 已生成类别特征 user_core_pred_has_other_vehicle 的可视化
2025-05-06 20:17:22,730 - __main__ - INFO - 已生成类别特征 user_core_pred_other_vehicle_age_year 的可视化
2025-05-06 20:17:22,824 - __main__ - INFO - 已生成类别特征 user_core_nio_user_identity 的可视化
2025-05-06 20:17:22,886 - __main__ - INFO - 已生成类别特征 user_core_onvo_user_identity 的可视化
2025-05-06 20:17:22,957 - __main__ - INFO - 已生成类别特征 user_core_nio_owned_vehicle_cnt 的可视化
2025-05-06 20:17:23,019 - __main__ - INFO - 已生成类别特征 user_core_nio_community_identity 的可视化
2025-05-06 20:17:23,077 - __main__ - INFO - 已生成类别特征 user_core_user_app_fans_cnt 的可视化
2025-05-06 20:17:23,138 - __main__ - INFO - 已生成类别特征 user_core_nio_has_inviter 的可视化
2025-05-06 20:17:23,213 - __main__ - INFO - 已生成类别特征 fellow_follow_planned_purchase_time 的可视化
2025-05-06 20:17:23,283 - __main__ - INFO - 已生成类别特征 fellow_follow_purchase_reason 的可视化
2025-05-06 20:17:23,351 - __main__ - INFO - 已生成类别特征 fellow_follow_battery_endurance 的可视化
2025-05-06 20:17:23,422 - __main__ - INFO - 已生成类别特征 fellow_follow_brand_recognition 的可视化
2025-05-06 20:17:23,422 - __main__ - INFO - 可视化报告已生成: data/dict/visualizations/visualization_report.html
2025-05-06 20:17:23,429 - __main__ - INFO - 数据字典生成完成
-e 
===== 步骤2: 分析数据字典 =====
开始分析数据字典: data/dict/json/data_dictionary.json
数据字典加载成功: 511个特征

===== 特征类型统计 =====
numeric     :   451 (88.3%)
categorical :    59 (11.5%)

===== 业务域统计 =====
用户核心属性                        :   362 (70.8%)
其他                            :    95 (18.6%)
用户行为序列                        :    17 (3.3%)
应用交互行为                        :    10 (2.0%)
车辆交互行为                        :    10 (2.0%)
销售交互行为                        :     9 (1.8%)
序列特征                          :     8 (1.6%)

===== 缺失值统计 =====
缺失率   0%-1%:     0 (0.0%)
缺失率   1%-5%:     0 (0.0%)
缺失率   5%-10%:     0 (0.0%)
缺失率  10%-20%:     1 (0.2%)
缺失率  20%-50%:     1 (0.2%)
缺失率  50%-80%:     2 (0.4%)
缺失率  80%-95%:     1 (0.2%)
缺失率  95%-100%:     1 (0.2%)

===== 数值特征零值统计 =====
零值率   0%-1%:    28 (6.2%)
零值率   1%-5%:     5 (1.1%)
零值率   5%-10%:     4 (0.9%)
零值率  10%-20%:     9 (2.0%)
零值率  20%-50%:    17 (3.8%)
零值率  50%-80%:    24 (5.3%)
零值率  80%-95%:    75 (16.6%)
零值率  95%-100%:   267 (59.2%)

===== 问题特征识别 =====
高缺失率特征 (>80%): 2 (0.4%)
极高缺失率特征 (>95%): 1 (0.2%)
高零值率特征 (>80%): 342 (75.8% 数值特征)
极高零值率特征 (>95%): 267 (59.2% 数值特征)
低方差特征: 0 (0.0% 数值特征)
单一类别主导特征: 5 (8.5% 类别特征)

极高缺失率特征示例 (最多显示5个):
  purchase_days_nio_new_car_total (其他): 缺失率 95.7%

极高零值率特征示例 (最多显示5个):
  fellow_follow_1d_cnt (其他): 零值率 95.6%
  answer_sales_call_duration_s_1d_cnt (销售交互行为): 零值率 99.6%
  answer_sales_call_duration_s_7d_cnt (销售交互行为): 零值率 97.0%
  user_core_is_nio_employee (用户核心属性): 零值率 99.3%
  user_core_user_app_fans_cnt (用户核心属性): 零值率 100.0%

特征统计已保存至: logs/feature_stats.csv

===== 数据改进建议 =====
1. 缺失值处理:
   - 考虑移除缺失率超过95%的1个特征
   - 对其他高缺失率特征，使用合适的填充策略
2. 特征选择:
   - 考虑移除零值率极高或低方差的特征
   - 使用特征重要性评估来选择更具信息量的特征子集
-e 
===== 步骤3: 运行数据分析 =====
==== 数据分析开始 ====
训练日期分区: ['20240430']
测试日期分区: ['20240531']
特征列数量: 355
标签列数量: 1
读取日期分区 20240430 的数据...
  - 从文件 data/dataset_nio_new_car_v15/datetime=20240430 读取 56464 行数据，标记为 train
读取日期分区 20240531 的数据...
  - 从文件 data/dataset_nio_new_car_v15/datetime=20240531 读取 9800 行数据，标记为 test
读取评估集数据...
  - 从文件 data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet 读取 212611 行数据，标记为 evaluate
总数据量: 278875行, 特征维度: 361列

详细数据源统计:
train 总样本数: 56464
  - data/dataset_nio_new_car_v15/datetime=20240430: 56464 行 (100.00%)
test 总样本数: 9800
  - data/dataset_nio_new_car_v15/datetime=20240531: 9800 行 (100.00%)
evaluate 总样本数: 212611
  - data/dataset_nio_new_car_v15/20240531_随机采样1%.parquet: 212611 行 (100.00%)

数据字段概览:
['user_id', 'datetime', 'fellow_follow_decision_maker', 'fellow_follow_intention_nio_confirm', 'fellow_follow_intention_test_drive', 'user_core_first_reg_leads_nio_DSLA', 'app_search_intention_cnt_1d', 'app_search_intention_cnt_7d', 'app_search_intention_cnt_14d', 'app_search_intention_cnt_30d', 'app_search_intention_cnt_60d', 'app_search_intention_cnt_90d', 'app_search_intention_cnt_180d', 'app_search_intention_DSLA', 'user_core_unfirst_reg_leads_nio_1d_cnt', 'user_core_unfirst_reg_leads_nio_7d_cnt', 'user_core_unfirst_reg_leads_nio_30d_cnt', 'user_core_unfirst_reg_leads_nio_60d_cnt', 'user_core_unfirst_reg_leads_nio_90d_cnt', 'user_core_unfirst_reg_leads_nio_180d_cnt', 'user_core_unfirst_reg_leads_nio_DSLA', 'user_core_nio_user_identity', 'intention_stage', 'user_core_user_gender', 'user_core_user_age_group', 'user_core_resident_city', 'user_core_is_nio_employee', 'user_core_pred_career_type', 'user_core_pred_marriage_status', 'user_core_pred_has_children', 'user_core_pred_has_other_vehicle', 'user_core_pred_other_vehicle_brand', 'user_core_pred_other_vehicle_age_year', 'user_core_nio_community_identity', 'user_core_nio_has_inviter', 'intention_status', 'intention_product_type', 'intention_is_walkin', 'intention_is_first', 'user_core_action_code_seq', 'user_car_core_action_code_seq', 'user_car_core_action_veh_model_seq', 'user_core_action_day_seq', 'user_car_core_action_day_seq', 'user_core_nio_owned_vehicle_cnt', 'user_core_nio_value', 'user_core_user_curr_credit_amount', 'user_create_days', 'user_register_days', 'intention_create_time_days', 'intention_opportunity_create_days', 'intention_intention_fail_days', 'user_core_visit_nioapp_login_1d_cnt', 'user_core_visit_nioapp_login_7d_cnt', 'user_core_visit_nioapp_login_30d_cnt', 'user_core_visit_nioapp_login_60d_cnt', 'user_core_visit_nioapp_login_90d_cnt', 'user_core_visit_nioapp_login_180d_cnt', 'user_core_exp_charging_nio_1d_cnt', 'user_core_exp_charging_nio_7d_cnt', 'user_core_exp_charging_nio_30d_cnt', 'user_core_exp_charging_nio_60d_cnt', 'user_core_exp_charging_nio_90d_cnt', 'user_core_exp_charging_nio_180d_cnt', 'user_core_exp_maintenance_nio_1d_cnt', 'user_core_exp_maintenance_nio_7d_cnt', 'user_core_exp_maintenance_nio_30d_cnt', 'user_core_exp_maintenance_nio_60d_cnt', 'user_core_exp_maintenance_nio_90d_cnt', 'user_core_exp_maintenance_nio_180d_cnt', 'user_core_checkin_nioapp_1d_cnt', 'user_core_checkin_nioapp_7d_cnt', 'user_core_checkin_nioapp_30d_cnt', 'user_core_checkin_nioapp_60d_cnt', 'user_core_checkin_nioapp_90d_cnt', 'user_core_checkin_nioapp_180d_cnt', 'user_core_collect_checkin_prize_nioapp_1d_cnt', 'user_core_collect_checkin_prize_nioapp_7d_cnt', 'user_core_collect_checkin_prize_nioapp_30d_cnt', 'user_core_collect_checkin_prize_nioapp_60d_cnt', 'user_core_collect_checkin_prize_nioapp_90d_cnt', 'user_core_collect_checkin_prize_nioapp_180d_cnt', 'user_core_view_nl_hp_nioapp_1d_cnt', 'user_core_view_nl_hp_nioapp_7d_cnt', 'user_core_view_nl_hp_nioapp_30d_cnt', 'user_core_view_nl_hp_nioapp_60d_cnt', 'user_core_view_nl_hp_nioapp_90d_cnt', 'user_core_view_nl_hp_nioapp_180d_cnt', 'user_core_buy_nl_nioapp_1d_cnt', 'user_core_buy_nl_nioapp_7d_cnt', 'user_core_buy_nl_nioapp_30d_cnt', 'user_core_buy_nl_nioapp_60d_cnt', 'user_core_buy_nl_nioapp_90d_cnt', 'user_core_buy_nl_nioapp_180d_cnt', 'user_core_view_cm_hp_nioapp_1d_cnt', 'user_core_view_cm_hp_nioapp_7d_cnt', 'user_core_view_cm_hp_nioapp_30d_cnt', 'user_core_view_cm_hp_nioapp_60d_cnt', 'user_core_view_cm_hp_nioapp_90d_cnt', 'user_core_view_cm_hp_nioapp_180d_cnt', 'user_core_view_cm_dp_nioapp_1d_cnt', 'user_core_view_cm_dp_nioapp_7d_cnt', 'user_core_view_cm_dp_nioapp_30d_cnt', 'user_core_view_cm_dp_nioapp_60d_cnt', 'user_core_view_cm_dp_nioapp_90d_cnt', 'user_core_view_cm_dp_nioapp_180d_cnt', 'user_core_sign_up_comm_act_nioapp_1d_cnt', 'user_core_sign_up_comm_act_nioapp_7d_cnt', 'user_core_sign_up_comm_act_nioapp_30d_cnt', 'user_core_sign_up_comm_act_nioapp_60d_cnt', 'user_core_sign_up_comm_act_nioapp_90d_cnt', 'user_core_sign_up_comm_act_nioapp_180d_cnt', 'user_core_view_hp_rec_nioapp_1d_cnt', 'user_core_view_hp_rec_nioapp_7d_cnt', 'user_core_view_hp_rec_nioapp_30d_cnt', 'user_core_view_hp_rec_nioapp_60d_cnt', 'user_core_view_hp_rec_nioapp_90d_cnt', 'user_core_view_hp_rec_nioapp_180d_cnt', 'user_core_view_hp_news_nioapp_1d_cnt', 'user_core_view_hp_news_nioapp_7d_cnt', 'user_core_view_hp_news_nioapp_30d_cnt', 'user_core_view_hp_news_nioapp_60d_cnt', 'user_core_view_hp_news_nioapp_90d_cnt', 'user_core_view_hp_news_nioapp_180d_cnt', 'user_core_view_pgc_nioapp_1d_cnt', 'user_core_view_pgc_nioapp_7d_cnt', 'user_core_view_pgc_nioapp_30d_cnt', 'user_core_view_pgc_nioapp_60d_cnt', 'user_core_view_pgc_nioapp_90d_cnt', 'user_core_view_pgc_nioapp_180d_cnt', 'user_core_view_ugc_nioapp_1d_cnt', 'user_core_view_ugc_nioapp_7d_cnt', 'user_core_view_ugc_nioapp_30d_cnt', 'user_core_view_ugc_nioapp_60d_cnt', 'user_core_view_ugc_nioapp_90d_cnt', 'user_core_view_ugc_nioapp_180d_cnt', 'user_core_search_nioapp_1d_cnt', 'user_core_search_nioapp_7d_cnt', 'user_core_search_nioapp_30d_cnt', 'user_core_search_nioapp_60d_cnt', 'user_core_search_nioapp_90d_cnt', 'user_core_search_nioapp_180d_cnt', 'user_core_view_veh_intro_nioapp_1d_cnt', 'user_core_view_veh_intro_nioapp_7d_cnt', 'user_core_view_veh_intro_nioapp_30d_cnt', 'user_core_view_veh_intro_nioapp_60d_cnt', 'user_core_view_veh_intro_nioapp_90d_cnt', 'user_core_view_veh_intro_nioapp_180d_cnt', 'user_core_view_service_intro_nioapp_1d_cnt', 'user_core_view_service_intro_nioapp_7d_cnt', 'user_core_view_service_intro_nioapp_30d_cnt', 'user_core_view_service_intro_nioapp_60d_cnt', 'user_core_view_service_intro_nioapp_90d_cnt', 'user_core_view_service_intro_nioapp_180d_cnt', 'user_core_view_nh_intro_nioapp_1d_cnt', 'user_core_view_nh_intro_nioapp_7d_cnt', 'user_core_view_nh_intro_nioapp_30d_cnt', 'user_core_view_nh_intro_nioapp_60d_cnt', 'user_core_view_nh_intro_nioapp_90d_cnt', 'user_core_view_nh_intro_nioapp_180d_cnt', 'user_core_view_swap_intro_nioapp_1d_cnt', 'user_core_view_swap_intro_nioapp_7d_cnt', 'user_core_view_swap_intro_nioapp_30d_cnt', 'user_core_view_swap_intro_nioapp_60d_cnt', 'user_core_view_swap_intro_nioapp_90d_cnt', 'user_core_view_swap_intro_nioapp_180d_cnt', 'user_core_view_sim_swap_nioapp_1d_cnt', 'user_core_view_sim_swap_nioapp_7d_cnt', 'user_core_view_sim_swap_nioapp_30d_cnt', 'user_core_view_sim_swap_nioapp_60d_cnt', 'user_core_view_sim_swap_nioapp_90d_cnt', 'user_core_view_sim_swap_nioapp_180d_cnt', 'user_core_visit_nio_wmp_login_1d_cnt', 'user_core_visit_nio_wmp_login_7d_cnt', 'user_core_visit_nio_wmp_login_30d_cnt', 'user_core_visit_nio_wmp_login_60d_cnt', 'user_core_visit_nio_wmp_login_90d_cnt', 'user_core_visit_nio_wmp_login_180d_cnt', 'user_core_visit_veh_cgf_nio_1d_cnt', 'user_core_visit_veh_cgf_nio_7d_cnt', 'user_core_visit_veh_cgf_nio_30d_cnt', 'user_core_visit_veh_cgf_nio_60d_cnt', 'user_core_visit_veh_cgf_nio_90d_cnt', 'user_core_visit_veh_cgf_nio_180d_cnt', 'user_core_save_veh_cgf_nio_1d_cnt', 'user_core_save_veh_cgf_nio_7d_cnt', 'user_core_save_veh_cgf_nio_30d_cnt', 'user_core_save_veh_cgf_nio_60d_cnt', 'user_core_save_veh_cgf_nio_90d_cnt', 'user_core_save_veh_cgf_nio_180d_cnt', 'user_core_del_veh_cgf_nio_1d_cnt', 'user_core_del_veh_cgf_nio_7d_cnt', 'user_core_del_veh_cgf_nio_30d_cnt', 'user_core_del_veh_cgf_nio_60d_cnt', 'user_core_del_veh_cgf_nio_90d_cnt', 'user_core_del_veh_cgf_nio_180d_cnt', 'user_core_view_veh_cfg_params_nioapp_1d_cnt', 'user_core_view_veh_cfg_params_nioapp_7d_cnt', 'user_core_view_veh_cfg_params_nioapp_30d_cnt', 'user_core_view_veh_cfg_params_nioapp_60d_cnt', 'user_core_view_veh_cfg_params_nioapp_90d_cnt', 'user_core_view_veh_cfg_params_nioapp_180d_cnt', 'user_core_view_finance_calc_nioapp_1d_cnt', 'user_core_view_finance_calc_nioapp_7d_cnt', 'user_core_view_finance_calc_nioapp_30d_cnt', 'user_core_view_finance_calc_nioapp_60d_cnt', 'user_core_view_finance_calc_nioapp_90d_cnt', 'user_core_view_finance_calc_nioapp_180d_cnt', 'user_core_view_mileage_calc_nioapp_1d_cnt', 'user_core_view_mileage_calc_nioapp_7d_cnt', 'user_core_view_mileage_calc_nioapp_30d_cnt', 'user_core_view_mileage_calc_nioapp_60d_cnt', 'user_core_view_mileage_calc_nioapp_90d_cnt', 'user_core_view_mileage_calc_nioapp_180d_cnt', 'user_core_answer_sales_pc_nio_1d_cnt', 'user_core_answer_sales_pc_nio_7d_cnt', 'user_core_answer_sales_pc_nio_30d_cnt', 'user_core_answer_sales_pc_nio_60d_cnt', 'user_core_answer_sales_pc_nio_90d_cnt', 'user_core_answer_sales_pc_nio_180d_cnt', 'user_core_fl_offline_im_nio_1d_cnt', 'user_core_fl_offline_im_nio_7d_cnt', 'user_core_fl_offline_im_nio_30d_cnt', 'user_core_fl_offline_im_nio_60d_cnt', 'user_core_fl_offline_im_nio_90d_cnt', 'user_core_fl_offline_im_nio_180d_cnt', 'user_core_fl_f2f_interview_nio_1d_cnt', 'user_core_fl_f2f_interview_nio_7d_cnt', 'user_core_fl_f2f_interview_nio_30d_cnt', 'user_core_fl_f2f_interview_nio_60d_cnt', 'user_core_fl_f2f_interview_nio_90d_cnt', 'user_core_fl_f2f_interview_nio_180d_cnt', 'user_core_view_mate_materials_nio_1d_cnt', 'user_core_view_mate_materials_nio_7d_cnt', 'user_core_view_mate_materials_nio_30d_cnt', 'user_core_view_mate_materials_nio_60d_cnt', 'user_core_view_mate_materials_nio_90d_cnt', 'user_core_view_mate_materials_nio_180d_cnt', 'user_core_buy_cm_nioapp_1d_cnt', 'user_core_buy_cm_nioapp_7d_cnt', 'user_core_buy_cm_nioapp_30d_cnt', 'user_core_buy_cm_nioapp_60d_cnt', 'user_core_buy_cm_nioapp_90d_cnt', 'user_core_buy_cm_nioapp_180d_cnt', 'universe_action_cnt_1d', 'universe_action_cnt_7d', 'universe_action_cnt_14d', 'universe_action_cnt_30d', 'universe_action_cnt_60d', 'universe_action_cnt_90d', 'universe_action_cnt_180d', 'fellow_follow_1d_cnt', 'fellow_follow_7d_cnt', 'fellow_follow_30d_cnt', 'fellow_follow_60d_cnt', 'fellow_follow_90d_cnt', 'fellow_follow_180d_cnt', 'fellow_follow_180d_DSLA', 'answer_sales_call_duration_s_1d_cnt', 'answer_sales_call_duration_s_7d_cnt', 'answer_sales_call_duration_s_30d_cnt', 'answer_sales_call_duration_s_60d_cnt', 'answer_sales_call_duration_s_90d_cnt', 'answer_sales_call_duration_s_180d_cnt', 'answer_sales_call_duration_s_last', 'user_core_onvo_user_identity', 'user_core_action_cnt_1d', 'user_core_action_cnt_7d', 'user_core_action_cnt_14d', 'user_core_action_cnt_30d', 'user_core_action_cnt_60d', 'user_core_action_cnt_90d', 'user_core_action_cnt_180d', 'user_car_core_action_cnt_1d', 'user_car_core_action_cnt_7d', 'user_car_core_action_cnt_14d', 'user_car_core_action_cnt_30d', 'user_car_core_action_cnt_60d', 'user_car_core_action_cnt_90d', 'user_car_core_action_cnt_180d', 'user_core_book_td_nio_1d_cnt', 'user_core_book_td_nio_7d_cnt', 'user_core_book_td_nio_30d_cnt', 'user_core_book_td_nio_60d_cnt', 'user_core_book_td_nio_90d_cnt', 'user_core_book_td_nio_180d_cnt', 'user_core_book_td_nio_DSLA', 'user_core_exp_td_nio_1d_cnt', 'user_core_exp_td_nio_7d_cnt', 'user_core_exp_td_nio_30d_cnt', 'user_core_exp_td_nio_60d_cnt', 'user_core_exp_td_nio_90d_cnt', 'user_core_exp_td_nio_180d_cnt', 'user_core_exp_td_nio_DSLA', 'user_core_pay_ncar_intention_nio_1d_cnt', 'user_core_pay_ncar_intention_nio_7d_cnt', 'user_core_pay_ncar_intention_nio_30d_cnt', 'user_core_pay_ncar_intention_nio_60d_cnt', 'user_core_pay_ncar_intention_nio_90d_cnt', 'user_core_pay_ncar_intention_nio_180d_cnt', 'user_core_pay_ncar_intention_nio_DSLA', 'user_core_pay_ncar_dp_nio_1d_cnt', 'user_core_pay_ncar_dp_nio_7d_cnt', 'user_core_pay_ncar_dp_nio_30d_cnt', 'user_core_pay_ncar_dp_nio_60d_cnt', 'user_core_pay_ncar_dp_nio_90d_cnt', 'user_core_pay_ncar_dp_nio_180d_cnt', 'user_core_pay_ncar_dp_nio_DSLA', 'user_core_lock_ncar_nio_1d_cnt', 'user_core_lock_ncar_nio_7d_cnt', 'user_core_lock_ncar_nio_30d_cnt', 'user_core_lock_ncar_nio_60d_cnt', 'user_core_lock_ncar_nio_90d_cnt', 'user_core_lock_ncar_nio_180d_cnt', 'user_core_lock_ncar_nio_DSLA', 'user_core_cancal_ncar_nio_1d_cnt', 'user_core_cancal_ncar_nio_7d_cnt', 'user_core_cancal_ncar_nio_30d_cnt', 'user_core_cancal_ncar_nio_60d_cnt', 'user_core_cancal_ncar_nio_90d_cnt', 'user_core_cancal_ncar_nio_180d_cnt', 'user_core_cancal_ncar_nio_DSLA', 'user_core_take_ncar_dl_nio_1d_cnt', 'user_core_take_ncar_dl_nio_7d_cnt', 'user_core_take_ncar_dl_nio_30d_cnt', 'user_core_take_ncar_dl_nio_60d_cnt', 'user_core_take_ncar_dl_nio_90d_cnt', 'user_core_take_ncar_dl_nio_180d_cnt', 'user_core_take_ncar_dl_nio_DSLA', 'user_core_view_used_veh_1d_cnt', 'user_core_view_used_veh_7d_cnt', 'user_core_view_used_veh_30d_cnt', 'user_core_view_used_veh_60d_cnt', 'user_core_view_used_veh_90d_cnt', 'user_core_view_used_veh_180d_cnt', 'user_core_view_used_veh_DSLA', 'user_core_visit_nh_1d_cnt', 'user_core_visit_nh_7d_cnt', 'user_core_visit_nh_30d_cnt', 'user_core_visit_nh_60d_cnt', 'user_core_visit_nh_90d_cnt', 'user_core_visit_nh_180d_cnt', 'user_core_visit_nh_DSLA', 'user_core_visit_roadshow_1d_cnt', 'user_core_visit_roadshow_7d_cnt', 'user_core_visit_roadshow_30d_cnt', 'user_core_visit_roadshow_60d_cnt', 'user_core_visit_roadshow_90d_cnt', 'user_core_visit_roadshow_180d_cnt', 'user_core_visit_roadshow_DSLA', 'user_core_visit_autoshow_1d_cnt', 'user_core_visit_autoshow_7d_cnt', 'user_core_visit_autoshow_30d_cnt', 'user_core_visit_autoshow_60d_cnt', 'user_core_visit_autoshow_90d_cnt', 'user_core_visit_autoshow_180d_cnt', 'user_core_visit_autoshow_DSLA', 'purchase_days_nio_new_car_total', 'm_purchase_days_nio_new_car', 'mask_label', 'data_source']

数据类型与缺失值:
user_id: object, 缺失率: 0.00%
datetime: object, 缺失率: 0.00%
fellow_follow_decision_maker: object, 缺失率: 71.48%
fellow_follow_intention_nio_confirm: object, 缺失率: 54.02%
fellow_follow_intention_test_drive: object, 缺失率: 53.93%
user_core_first_reg_leads_nio_DSLA: float64, 缺失率: 60.34%
app_search_intention_cnt_1d: float64, 缺失率: 73.16%
app_search_intention_cnt_7d: float64, 缺失率: 73.16%
app_search_intention_cnt_14d: float64, 缺失率: 73.16%
app_search_intention_cnt_30d: float64, 缺失率: 73.16%
app_search_intention_cnt_60d: float64, 缺失率: 73.16%
app_search_intention_cnt_90d: float64, 缺失率: 73.16%
app_search_intention_cnt_180d: float64, 缺失率: 73.16%
app_search_intention_DSLA: float64, 缺失率: 73.16%
user_core_unfirst_reg_leads_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_unfirst_reg_leads_nio_DSLA: float64, 缺失率: 54.21%
user_core_nio_user_identity: object, 缺失率: 0.00%
intention_stage: object, 缺失率: 48.48%
user_core_user_gender: object, 缺失率: 58.78%
user_core_user_age_group: object, 缺失率: 21.45%
user_core_resident_city: object, 缺失率: 1.65%
user_core_is_nio_employee: float64, 缺失率: 0.00%
user_core_pred_career_type: object, 缺失率: 44.78%
user_core_pred_marriage_status: object, 缺失率: 49.85%
user_core_pred_has_children: object, 缺失率: 47.43%
user_core_pred_has_other_vehicle: float64, 缺失率: 18.01%
user_core_pred_other_vehicle_brand: object, 缺失率: 75.35%
user_core_pred_other_vehicle_age_year: object, 缺失率: 74.25%
user_core_nio_community_identity: object, 缺失率: 76.19%
user_core_nio_has_inviter: float64, 缺失率: 0.00%
intention_status: object, 缺失率: 48.48%
intention_product_type: object, 缺失率: 48.48%
intention_is_walkin: object, 缺失率: 48.48%
intention_is_first: object, 缺失率: 48.48%
user_core_action_code_seq: object, 缺失率: 45.67%
user_car_core_action_code_seq: object, 缺失率: 67.93%
user_car_core_action_veh_model_seq: object, 缺失率: 67.93%
user_core_action_day_seq: object, 缺失率: 45.67%
user_car_core_action_day_seq: object, 缺失率: 67.93%
user_core_nio_owned_vehicle_cnt: float64, 缺失率: 0.00%
user_core_nio_value: float64, 缺失率: 41.55%
user_core_user_curr_credit_amount: float64, 缺失率: 46.07%
user_create_days: float64, 缺失率: 0.00%
user_register_days: float64, 缺失率: 38.18%
intention_create_time_days: float64, 缺失率: 48.48%
intention_opportunity_create_days: float64, 缺失率: 65.29%
intention_intention_fail_days: float64, 缺失率: 61.18%
user_core_visit_nioapp_login_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_nioapp_login_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_nioapp_login_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_nioapp_login_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_nioapp_login_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_nioapp_login_180d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_exp_charging_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_exp_maintenance_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_checkin_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_collect_checkin_prize_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_nl_hp_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_buy_nl_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_hp_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_cm_dp_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_sign_up_comm_act_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_rec_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_hp_news_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_pgc_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_ugc_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_search_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_intro_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_service_intro_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_nh_intro_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_swap_intro_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_sim_swap_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_nio_wmp_login_180d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_veh_cgf_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_save_veh_cgf_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_del_veh_cgf_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_veh_cfg_params_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_finance_calc_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_view_mileage_calc_nioapp_180d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_answer_sales_pc_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_fl_offline_im_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_fl_f2f_interview_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_view_mate_materials_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_1d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_7d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_30d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_60d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_90d_cnt: float64, 缺失率: 37.19%
user_core_buy_cm_nioapp_180d_cnt: float64, 缺失率: 37.19%
universe_action_cnt_1d: float64, 缺失率: 37.19%
universe_action_cnt_7d: float64, 缺失率: 37.19%
universe_action_cnt_14d: float64, 缺失率: 37.19%
universe_action_cnt_30d: float64, 缺失率: 37.19%
universe_action_cnt_60d: float64, 缺失率: 37.19%
universe_action_cnt_90d: float64, 缺失率: 37.19%
universe_action_cnt_180d: float64, 缺失率: 37.19%
fellow_follow_1d_cnt: float64, 缺失率: 21.11%
fellow_follow_7d_cnt: float64, 缺失率: 21.11%
fellow_follow_30d_cnt: float64, 缺失率: 21.11%
fellow_follow_60d_cnt: float64, 缺失率: 21.11%
fellow_follow_90d_cnt: float64, 缺失率: 21.11%
fellow_follow_180d_cnt: float64, 缺失率: 21.11%
fellow_follow_180d_DSLA: float64, 缺失率: 21.11%
answer_sales_call_duration_s_1d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_7d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_30d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_60d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_90d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_180d_cnt: float64, 缺失率: 64.86%
answer_sales_call_duration_s_last: object, 缺失率: 64.86%
user_core_onvo_user_identity: object, 缺失率: 0.00%
user_core_action_cnt_1d: float64, 缺失率: 45.67%
user_core_action_cnt_7d: float64, 缺失率: 45.67%
user_core_action_cnt_14d: float64, 缺失率: 45.67%
user_core_action_cnt_30d: float64, 缺失率: 45.67%
user_core_action_cnt_60d: float64, 缺失率: 45.67%
user_core_action_cnt_90d: float64, 缺失率: 45.67%
user_core_action_cnt_180d: float64, 缺失率: 45.67%
user_car_core_action_cnt_1d: float64, 缺失率: 67.93%
user_car_core_action_cnt_7d: float64, 缺失率: 67.93%
user_car_core_action_cnt_14d: float64, 缺失率: 67.93%
user_car_core_action_cnt_30d: float64, 缺失率: 67.93%
user_car_core_action_cnt_60d: float64, 缺失率: 67.93%
user_car_core_action_cnt_90d: float64, 缺失率: 67.93%
user_car_core_action_cnt_180d: float64, 缺失率: 67.93%
user_core_book_td_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_book_td_nio_DSLA: float64, 缺失率: 72.76%
user_core_exp_td_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_exp_td_nio_DSLA: float64, 缺失率: 71.70%
user_core_pay_ncar_intention_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_intention_nio_DSLA: float64, 缺失率: 76.21%
user_core_pay_ncar_dp_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_pay_ncar_dp_nio_DSLA: float64, 缺失率: 75.85%
user_core_lock_ncar_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_lock_ncar_nio_DSLA: float64, 缺失率: 75.94%
user_core_cancal_ncar_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_cancal_ncar_nio_DSLA: float64, 缺失率: 76.11%
user_core_take_ncar_dl_nio_1d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_7d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_30d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_60d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_90d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_180d_cnt: float64, 缺失率: 37.19%
user_core_take_ncar_dl_nio_DSLA: float64, 缺失率: 75.96%
user_core_view_used_veh_1d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_7d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_30d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_60d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_90d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_180d_cnt: float64, 缺失率: 37.19%
user_core_view_used_veh_DSLA: float64, 缺失率: 74.42%
user_core_visit_nh_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_180d_cnt: float64, 缺失率: 37.19%
user_core_visit_nh_DSLA: float64, 缺失率: 72.05%
user_core_visit_roadshow_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_180d_cnt: float64, 缺失率: 37.19%
user_core_visit_roadshow_DSLA: float64, 缺失率: 75.74%
user_core_visit_autoshow_1d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_7d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_30d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_60d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_90d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_180d_cnt: float64, 缺失率: 37.19%
user_core_visit_autoshow_DSLA: float64, 缺失率: 76.10%
purchase_days_nio_new_car_total: object, 缺失率: 98.79%
m_purchase_days_nio_new_car: object, 缺失率: 76.24%
mask_label: object, 缺失率: 76.24%
data_source: object, 缺失率: 0.00%

==== 基本统计信息 ====
数据源分布:
data_source
evaluate    212611
train        56464
test          9800
Name: count, dtype: int64
各数据源正样本比例(%):
data_source
evaluate    0.244108
test        3.908163
train       4.197365
Name: purchase_days_nio_new_car_total_num, dtype: float64

==== 特征类型分析 ====
特征类型分布: {'table': 350, 'VarLen': 5}
特征类型+数据类型分布: {'table_StringLookup': 22, 'table_Bucket': 328, 'VarLen_StringLookup': 3, 'VarLen_Bucket': 2}

==== 序列特征分析 ====
序列特征数量: 5
分析序列特征: user_core_action_code_seq
分析序列特征: user_car_core_action_code_seq
分析序列特征: user_car_core_action_veh_model_seq
分析序列特征: user_core_action_day_seq
分析序列特征: user_car_core_action_day_seq

==== 类别特征分析 ====
类别特征数量: 22
分析类别特征: fellow_follow_decision_maker
分析类别特征: fellow_follow_intention_nio_confirm
分析类别特征: fellow_follow_intention_test_drive
分析类别特征: user_core_nio_user_identity
分析类别特征: intention_stage
分析类别特征: user_core_user_gender
分析类别特征: user_core_user_age_group
分析类别特征: user_core_resident_city
分析类别特征: user_core_is_nio_employee
分析类别特征: user_core_pred_career_type

==== 数值特征分析 ====
数值特征数量: 328
分析数值特征: user_core_first_reg_leads_nio_DSLA
分析数值特征: app_search_intention_cnt_1d
分析数值特征: app_search_intention_cnt_7d
分析数值特征: app_search_intention_cnt_14d
分析数值特征: app_search_intention_cnt_30d
分析数值特征: app_search_intention_cnt_60d
分析数值特征: app_search_intention_cnt_90d
分析数值特征: app_search_intention_cnt_180d
分析数值特征: app_search_intention_DSLA
分析数值特征: user_core_unfirst_reg_leads_nio_1d_cnt

==== 标签分析 ====
分析总体购买标签分布
总体正样本率: 1.17%
各数据源正样本率(%): 
data_source
evaluate    0.244108
test        3.908163
train       4.197365
Name: purchase_days_nio_new_car_total_num, dtype: float64
第1个月正样本率: 1.29%
第2个月正样本率: 0.83%
第3个月正样本率: 0.65%
第4个月正样本率: 0.67%
第5个月正样本率: 0.56%
第6个月正样本率: 0.38%
购买模式前10:
模式 000000: 63424次 (95.71%)
模式 100000: 843次 (1.27%)
模式 010000: 531次 (0.80%)
模式 000100: 421次 (0.64%)
模式 001000: 409次 (0.62%)
模式 000010: 347次 (0.52%)
模式 000001: 235次 (0.35%)
模式 000110: 7次 (0.01%)
模式 001100: 6次 (0.01%)
模式 010100: 5次 (0.01%)

==== 特征相关性分析 ====
进行相关性分析的特征数量: 20
发现高相关性特征对:
                                        特征1  ...      相关系数
6              app_search_intention_cnt_60d  ...  0.930900
10  user_core_unfirst_reg_leads_nio_60d_cnt  ...  0.883595
0               app_search_intention_cnt_7d  ...  0.869289
8              app_search_intention_cnt_90d  ...  0.862566
4              app_search_intention_cnt_30d  ...  0.860129
2              app_search_intention_cnt_14d  ...  0.854850
9   user_core_unfirst_reg_leads_nio_30d_cnt  ...  0.782185
5              app_search_intention_cnt_30d  ...  0.762893
11  user_core_unfirst_reg_leads_nio_90d_cnt  ...  0.760506
7              app_search_intention_cnt_60d  ...  0.756665
1               app_search_intention_cnt_7d  ...  0.718124
3              app_search_intention_cnt_14d  ...  0.711828

[12 rows x 3 columns]

==== 特征重要性预估 ====
特征重要性分析完成，展示前20个特征的重要性

报告已生成: src/data/data_analyze/报告-数据分析.md

==== 数据分析完成 ====
-e 
===== 分析流程完成 =====
结束时间: Tue May  6 20:17:30 CST 2025
结果目录:
- 数据字典: data/dict/
- 特征统计: logs/feature_stats.csv
- 数据分析: src/data/data_analyze/
