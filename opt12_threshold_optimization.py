#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化12: 阈值优化 - 在保持高AUC的基础上优化阈值提升召回率
基线: AUC 0.8011, Recall@840 0.0962
策略: 使用最佳模型，通过阈值调优平衡精确率和召回率
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader
from models.core_models import ModelTrainer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def find_optimal_threshold_for_recall(y_true, y_pred_proba, target_recall=0.5):
    """找到达到目标召回率的最优阈值"""
    precision, recall, thresholds = precision_recall_curve(y_true, y_pred_proba)
    
    # 找到最接近目标召回率的阈值
    recall_diff = np.abs(recall - target_recall)
    best_idx = np.argmin(recall_diff)
    
    if best_idx < len(thresholds):
        optimal_threshold = thresholds[best_idx]
        optimal_precision = precision[best_idx]
        optimal_recall = recall[best_idx]
    else:
        optimal_threshold = 0.5
        optimal_precision = precision[-1]
        optimal_recall = recall[-1]
    
    return optimal_threshold, optimal_precision, optimal_recall

def comprehensive_threshold_analysis(y_true, y_pred_proba):
    """全面的阈值分析"""
    from sklearn.metrics import precision_score, recall_score, f1_score
    
    # 测试不同阈值
    thresholds = np.arange(0.05, 0.95, 0.05)
    results = []
    
    for threshold in thresholds:
        y_pred_binary = (y_pred_proba > threshold).astype(int)
        
        precision = precision_score(y_true, y_pred_binary, zero_division=0)
        recall = recall_score(y_true, y_pred_binary, zero_division=0)
        f1 = f1_score(y_true, y_pred_binary, zero_division=0)
        
        results.append({
            'threshold': threshold,
            'precision': precision,
            'recall': recall,
            'f1': f1
        })
    
    return results

def train_and_evaluate():
    """训练最佳模型并进行阈值优化"""
    # 1. 加载数据
    data_loader = NioDataLoader()
    X, y, feature_names = data_loader.load_and_prepare(feature_count=150)
    
    # 2. 数据分割
    X_train, X_test, y_train, y_test = data_loader.split_data(X, y, test_size=0.2, random_state=42)
    
    # 3. 使用最佳配置训练模型
    logger.info("=== 训练最佳配置模型 ===")
    trainer = ModelTrainer(use_smote=True, smote_ratio=0.05)
    
    training_results = trainer.train_ensemble(
        X_train, y_train, X_test, y_test,
        epochs=12,
        batch_size=512,
        patience=5,
        ensemble_size=3,
        verbose=0
    )
    
    ensemble_pred = training_results['ensemble_prediction']
    individual_preds = training_results['individual_predictions']
    
    # 4. 基础评估
    base_auc = roc_auc_score(y_test, ensemble_pred)
    base_pr_auc = calculate_pr_auc(y_test, ensemble_pred)
    base_precision_at_840, base_recall_at_840 = calculate_precision_recall_at_k(y_test, ensemble_pred, k=840)
    
    logger.info(f"基础模型 - AUC: {base_auc:.4f}, PR-AUC: {base_pr_auc:.4f}")
    logger.info(f"基础模型 - P@840: {base_precision_at_840:.4f}, R@840: {base_recall_at_840:.4f}")
    
    # 5. 阈值优化分析
    logger.info("=== 阈值优化分析 ===")
    
    # 全面阈值分析
    threshold_results = comprehensive_threshold_analysis(y_test, ensemble_pred)
    
    # 找到不同目标的最优阈值
    optimal_thresholds = {}
    
    # 目标1: 最大化F1分数
    f1_scores = [r['f1'] for r in threshold_results]
    best_f1_idx = np.argmax(f1_scores)
    optimal_thresholds['max_f1'] = threshold_results[best_f1_idx]
    
    # 目标2: 达到50%召回率
    threshold_50_recall, precision_50_recall, recall_50_recall = find_optimal_threshold_for_recall(
        y_test, ensemble_pred, target_recall=0.5
    )
    optimal_thresholds['50_recall'] = {
        'threshold': threshold_50_recall,
        'precision': precision_50_recall,
        'recall': recall_50_recall,
        'f1': 2 * precision_50_recall * recall_50_recall / (precision_50_recall + recall_50_recall) if (precision_50_recall + recall_50_recall) > 0 else 0
    }
    
    # 目标3: 达到70%召回率
    threshold_70_recall, precision_70_recall, recall_70_recall = find_optimal_threshold_for_recall(
        y_test, ensemble_pred, target_recall=0.7
    )
    optimal_thresholds['70_recall'] = {
        'threshold': threshold_70_recall,
        'precision': precision_70_recall,
        'recall': recall_70_recall,
        'f1': 2 * precision_70_recall * recall_70_recall / (precision_70_recall + recall_70_recall) if (precision_70_recall + recall_70_recall) > 0 else 0
    }
    
    # 目标4: 平衡精确率和召回率
    precision_recall_balance = []
    for r in threshold_results:
        if r['precision'] > 0 and r['recall'] > 0:
            balance_score = 2 / (1/r['precision'] + 1/r['recall'])  # 调和平均数
            precision_recall_balance.append(balance_score)
        else:
            precision_recall_balance.append(0)
    
    best_balance_idx = np.argmax(precision_recall_balance)
    optimal_thresholds['balanced'] = threshold_results[best_balance_idx]
    
    # 6. 与基线和最老模型对比
    baseline_auc = 0.8011
    baseline_recall = 0.0962
    old_model_auc = 0.9142
    old_model_recall = 0.9747
    old_model_pr_auc = 0.5707
    
    # 记录结果
    results = {
        'optimization': 'threshold_optimization',
        'base_model': {
            'auc': float(base_auc),
            'pr_auc': float(base_pr_auc),
            'precision_at_840': float(base_precision_at_840),
            'recall_at_840': float(base_recall_at_840)
        },
        'optimal_thresholds': {
            k: {key: float(val) if isinstance(val, (int, float, np.number)) else val 
                for key, val in v.items()} 
            for k, v in optimal_thresholds.items()
        },
        'threshold_analysis': [
            {key: float(val) if isinstance(val, (int, float, np.number)) else val 
             for key, val in r.items()} 
            for r in threshold_results
        ],
        'comparison': {
            'baseline_auc': baseline_auc,
            'old_model_auc': old_model_auc,
            'old_model_recall': old_model_recall,
            'old_model_pr_auc': old_model_pr_auc,
            'auc_vs_baseline': float(base_auc - baseline_auc),
            'auc_vs_old': float(base_auc - old_model_auc),
            'pr_auc_vs_old': float(base_pr_auc - old_model_pr_auc)
        },
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in individual_preds],
        'effective': bool(base_auc >= baseline_auc and base_pr_auc > 0.1)
    }
    
    # 7. 输出详细分析
    logger.info(f"=== 阈值优化结果 ===")
    logger.info(f"基础AUC: {base_auc:.4f} (vs基线: {base_auc - baseline_auc:+.4f})")
    logger.info(f"基础PR-AUC: {base_pr_auc:.4f} (vs最老模型: {base_pr_auc - old_model_pr_auc:+.4f})")
    
    for name, opt in optimal_thresholds.items():
        logger.info(f"{name}最优阈值: {opt['threshold']:.3f} - P: {opt['precision']:.4f}, R: {opt['recall']:.4f}, F1: {opt['f1']:.4f}")
    
    return results

if __name__ == "__main__":
    result = train_and_evaluate()
    
    with open('opt12_results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化12完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"基础AUC: {result['base_model']['auc']:.4f}")
    print(f"基础PR-AUC: {result['base_model']['pr_auc']:.4f}")
    print(f"基础P@840: {result['base_model']['precision_at_840']:.4f}")
    print(f"基础R@840: {result['base_model']['recall_at_840']:.4f}")
    
    print(f"\n最优阈值配置:")
    for name, opt in result['optimal_thresholds'].items():
        print(f"{name}: 阈值={opt['threshold']:.3f}, P={opt['precision']:.4f}, R={opt['recall']:.4f}, F1={opt['f1']:.4f}")
    
    print(f"\n与最老模型对比:")
    print(f"AUC: {result['base_model']['auc']:.4f} vs 0.9142 ({result['comparison']['auc_vs_old']:+.4f})")
    print(f"PR-AUC: {result['base_model']['pr_auc']:.4f} vs 0.5707 ({result['comparison']['pr_auc_vs_old']:+.4f})")
