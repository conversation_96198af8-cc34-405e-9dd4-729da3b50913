"""
Custom layer implementations for conversion rate estimation models.
"""
import tensorflow as tf


class CrossLayer(tf.keras.layers.Layer):
    """Feature cross layer to enhance feature interactions."""
    
    def __init__(self, layer_size=64, l2_reg=0.01):
        """
        Initialize cross layer.
        
        Args:
            layer_size (int): Size of the output layer.
            l2_reg (float): L2 regularization factor.
        """
        super(<PERSON><PERSON>ay<PERSON>, self).__init__()
        self.layer_size = layer_size
        self.l2_reg = l2_reg
        
    def build(self, input_shape):
        """
        Build layer weights.
        
        Args:
            input_shape: Shape of input tensor.
        """
        self.input_dim = int(input_shape[-1])
        self.w = self.add_weight(
            name='cross_w',
            shape=(self.input_dim, self.layer_size),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(self.l2_reg),
            trainable=True
        )
        self.b = self.add_weight(
            name='cross_b',
            shape=(self.layer_size,),
            initializer='zeros',
            trainable=True
        )
        # Use tf.function for acceleration
        self.call = tf.function(self.call)
        super(CrossLayer, self).build(input_shape)
        
    def call(self, inputs):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor.
            
        Returns:
            Tensor: Cross layer output.
        """
        # More efficient implementation avoiding multiple expand_dims
        x_w = tf.matmul(inputs, self.w)  # (batch_size, layer_size)
        return x_w + self.b  # (batch_size, layer_size)


class TimeSeriesAttention(tf.keras.layers.Layer):
    """Time series attention layer with time decay weights."""
    
    def __init__(self, attention_dim=32, time_decay_factor=0.1, supports_masking=True):
        """
        Initialize time series attention layer.
        
        Args:
            attention_dim (int): Attention mechanism dimension.
            time_decay_factor (float): Factor controlling time decay.
            supports_masking (bool): Whether layer supports masking.
        """
        super(TimeSeriesAttention, self).__init__()
        self.attention_dim = attention_dim
        self.time_decay_factor = time_decay_factor
        self.supports_masking = supports_masking
        
    def build(self, input_shape):
        """
        Build layer weights.
        
        Args:
            input_shape: Shape of input tensor.
        """
        # Build attention weight matrices
        self.seq_len = input_shape[1]
        self.feature_dim = input_shape[2]
        
        self.W = self.add_weight(
            name='attention_W',
            shape=(self.feature_dim, self.attention_dim),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(0.01),
            trainable=True
        )
        self.u = self.add_weight(
            name='attention_u',
            shape=(self.attention_dim, 1),
            initializer='glorot_normal',
            regularizer=tf.keras.regularizers.l2(0.01),
            trainable=True
        )
        
        # Use tf.function for acceleration
        self.call = tf.function(self.call)
        super(TimeSeriesAttention, self).build(input_shape)
        
    def call(self, inputs, mask=None):
        """
        Forward pass calculation.
        
        Args:
            inputs: Input tensor.
            mask: Mask tensor.
            
        Returns:
            Tensor: Attention layer output.
        """
        # Get input data type
        dtype = inputs.dtype
        
        # Calculate attention scores with time decay
        uit = tf.tanh(tf.tensordot(inputs, self.W, axes=1))
        ait = tf.tensordot(uit, self.u, axes=1)
        ait = tf.squeeze(ait, -1)
        
        # Time decay weights - recent behavior gets higher weight
        time_decay_range = tf.range(self.seq_len, 0, -1, dtype=tf.float32)
        time_weights = tf.exp(-self.time_decay_factor * time_decay_range)
        # Convert to same data type as inputs
        time_weights = tf.cast(time_weights, dtype)
        
        # Apply decay weights and mask
        attention_weights = ait * time_weights
        
        if mask is not None:
            # Ensure mask has correct data type
            mask_float = tf.cast(mask, dtype)
            attention_weights += (1.0 - mask_float) * tf.cast(-1e9, dtype)
            
        # Add numerical stability measures
        attention_weights = tf.nn.softmax(attention_weights, axis=1)
        
        # Prevent NaN values
        attention_weights = tf.where(
            tf.math.is_nan(attention_weights),
            tf.zeros_like(attention_weights),
            attention_weights
        )
        
        # Weighted sum
        return tf.reduce_sum(inputs * tf.expand_dims(attention_weights, -1), axis=1) 