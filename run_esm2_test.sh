#!/bin/bash
# ESM2模型测试运行脚本
# 添加重采样策略和更多优化参数

# 设置运行名称使用时间戳
RUN_NAME="esm2_debug_run_$(date +%Y%m%d_%H%M%S)"

# 执行训练命令
python src/train.py \
  --model_code=sample_20250311_v7-20250311 \
  --run_name=$RUN_NAME \
  --dataset_code=dataset_nio_new_car_v15 \
  --evaluate_file="20240531_随机采样1%.parquet" \
  --data_dir="data" \
  --epochs=10 \
  --patience=3 \
  --batch_size=1024 \
  --use_esm2 \
  --esm2_task_names=test_drive_output,appointment_output,purchase_output \
  --use_cross_layer=True \
  --use_time_attention=True \
  --time_decay_factor=0.05 \
  --loss_type=focal \
  --pos_weight=25.0 \
  --resampling_strategy=optimized_smote_enn \
  --smote_ratio=0.6 \
  --smote_k=5 \
  --enn_k=3 \
  --use_balanced_sampling \
  --pos_ratio=0.4 \
  --use_enhanced_features

# 输出结果位置
echo "Training completed. Results are in: src/evaluation/$RUN_NAME/" 