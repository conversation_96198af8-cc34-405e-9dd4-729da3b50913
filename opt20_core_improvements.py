#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化20: 核心改进 - 基于早期模型的关键成功要素
专注于最核心的改进：分桶特征 + 时间窗口聚合 + 简洁架构
基线: AUC 0.8219 (SMOTE + 集成)
目标: 提取并应用早期模型的核心成功要素
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler, KBinsDiscretizer
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def core_feature_engineering(df):
    """核心特征工程 - 基于早期模型的关键成功要素"""
    logger.info("核心特征工程...")
    
    # 1. 基础数值特征
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
    
    # 选择前150个特征
    base_features = numeric_cols[:150]
    df_features = df[base_features].fillna(0).copy()
    
    logger.info(f"基础特征数: {len(base_features)}")
    
    # 2. 关键改进1: 多时间窗口聚合 (早期模型最重要的特征)
    time_windows = ['1d', '7d', '30d']
    
    for window in time_windows:
        window_cols = [col for col in base_features if f'{window}_cnt' in col]
        if window_cols:
            df_features[f'total_actions_{window}'] = df_features[window_cols].sum(axis=1)
            df_features[f'action_diversity_{window}'] = (df_features[window_cols] > 0).sum(axis=1)
    
    # 3. 关键改进2: 分桶特征 (早期模型的bin_boundaries效果)
    high_var_cols = [col for col in df_features.columns 
                    if df_features[col].std() > 1 and df_features[col].max() > 5]
    
    logger.info(f"对{min(5, len(high_var_cols))}个特征进行分桶...")
    
    for col in high_var_cols[:5]:
        try:
            discretizer = KBinsDiscretizer(n_bins=5, encode='ordinal', strategy='quantile')
            col_values = df_features[col].values.reshape(-1, 1)
            df_features[f'{col}_bucket'] = discretizer.fit_transform(col_values).flatten()
        except:
            continue
    
    # 4. 关键改进3: 行为趋势 (时间窗口间变化)
    if 'total_actions_1d' in df_features.columns and 'total_actions_7d' in df_features.columns:
        df_features['trend_7d_1d'] = df_features['total_actions_7d'] - df_features['total_actions_1d']
        df_features['ratio_7d_1d'] = df_features['total_actions_7d'] / (df_features['total_actions_1d'] + 1)
    
    # 5. 关键改进4: 业务特征
    car_keywords = ['book_td', 'exp_td', 'lock_ncar', 'pay_ncar']
    car_cols = [col for col in base_features if any(kw in col for kw in car_keywords)]
    if car_cols:
        df_features['total_car_actions'] = df_features[car_cols].sum(axis=1)
    
    core_cols = [col for col in base_features if 'user_core' in col and 'cnt' in col]
    if core_cols:
        df_features['total_core_actions'] = df_features[core_cols].sum(axis=1)
    
    # 6. 关键改进5: 交互特征
    if 'total_core_actions' in df_features.columns and 'total_car_actions' in df_features.columns:
        df_features['core_car_interaction'] = df_features['total_core_actions'] * df_features['total_car_actions']
    
    # 7. 基础统计特征
    df_features['feature_sum'] = df_features.sum(axis=1)
    df_features['nonzero_count'] = (df_features > 0).sum(axis=1)
    
    logger.info(f"核心特征工程完成: {df_features.shape[1]}个特征")
    
    return df_features.values, list(df_features.columns)

def create_core_model(input_dim):
    """创建核心模型 - 简洁有效的架构"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def train_and_evaluate():
    """训练和评估核心改进模型"""
    # 1. 加载数据
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 核心特征工程
    X, feature_names = core_feature_engineering(df)
    y = data_loader.prepare_labels(df)
    
    # 3. 特征标准化
    logger.info("特征标准化...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 4. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. 应用SMOTE重采样
    logger.info("应用SMOTE重采样...")
    smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    logger.info(f"SMOTE重采样: {len(X_train)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
    
    # 6. 训练参数
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=7, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5)
    ]
    
    # 7. 训练集成模型
    logger.info("=== 训练核心改进模型集成 ===")
    
    models = []
    predictions = []
    
    for i in range(3):
        logger.info(f"=== 训练核心模型{i+1} ===")
        model = create_core_model(X_train_smote.shape[1])
        
        if i == 0:
            model.summary()
        
        model.fit(
            X_train_smote, y_train_smote,
            validation_data=(X_test, y_test),
            epochs=12, batch_size=512,
            callbacks=callbacks,
            verbose=0
        )
        
        pred = model.predict(X_test, verbose=0)
        predictions.append(pred)
        models.append(model)
        
        # 单模型评估
        auc_score = roc_auc_score(y_test, pred)
        pr_auc = calculate_pr_auc(y_test, pred)
        precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
        
        logger.info(f"核心模型{i+1} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
        logger.info(f"核心模型{i+1} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
    
    # 8. 集成预测
    ensemble_pred = np.mean(predictions, axis=0)
    
    # 9. 详细评估
    ensemble_auc = roc_auc_score(y_test, ensemble_pred)
    ensemble_pr_auc = calculate_pr_auc(y_test, ensemble_pred)
    ensemble_precision_at_840, ensemble_recall_at_840 = calculate_precision_recall_at_k(y_test, ensemble_pred, k=840)
    
    logger.info(f"=== 核心改进模型结果 ===")
    logger.info(f"集成AUC: {ensemble_auc:.4f}")
    logger.info(f"集成PR-AUC: {ensemble_pr_auc:.4f}")
    logger.info(f"集成P@840: {ensemble_precision_at_840:.4f}")
    logger.info(f"集成R@840: {ensemble_recall_at_840:.4f}")
    
    # 与基线和早期模型对比
    baseline_auc = 0.8219
    early_model_auc = 0.8756
    early_model_pr_auc = 0.4227
    early_model_recall = 0.9241
    
    auc_vs_baseline = ensemble_auc - baseline_auc
    auc_vs_early = ensemble_auc - early_model_auc
    pr_auc_vs_early = ensemble_pr_auc - early_model_pr_auc
    recall_vs_early = ensemble_recall_at_840 - early_model_recall
    
    logger.info(f"相比基线: AUC {auc_vs_baseline:+.4f}")
    logger.info(f"相比早期模型: AUC {auc_vs_early:+.4f}, PR-AUC {pr_auc_vs_early:+.4f}, Recall@840 {recall_vs_early:+.4f}")
    
    return {
        'optimization': 'core_improvements',
        'ensemble_auc': float(ensemble_auc),
        'ensemble_pr_auc': float(ensemble_pr_auc),
        'precision_at_840': float(ensemble_precision_at_840),
        'recall_at_840': float(ensemble_recall_at_840),
        'feature_count': len(feature_names),
        'baseline_auc': baseline_auc,
        'early_model_auc': early_model_auc,
        'early_model_pr_auc': early_model_pr_auc,
        'early_model_recall': early_model_recall,
        'auc_vs_baseline': float(auc_vs_baseline),
        'auc_vs_early': float(auc_vs_early),
        'pr_auc_vs_early': float(pr_auc_vs_early),
        'recall_vs_early': float(recall_vs_early),
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in predictions],
        'effective': bool(auc_vs_baseline > 0.005 or (auc_vs_early > -0.05 and ensemble_pr_auc > 0.03))
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_core_improvements"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化20完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"集成AUC: {result['ensemble_auc']:.4f}")
    print(f"集成PR-AUC: {result['ensemble_pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: {result['feature_count']}")
    print(f"相比基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"相比早期模型AUC: {result['auc_vs_early']:+.4f}")
    print(f"相比早期模型PR-AUC: {result['pr_auc_vs_early']:+.4f}")
    print(f"相比早期模型Recall@840: {result['recall_vs_early']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
