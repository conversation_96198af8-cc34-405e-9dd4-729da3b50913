"""
EPMMOE_ESM2Net - Extended Progressive Multi-level Mixture of Experts with ESM2 architecture.
Implements ESM2 (Extended Space Multi-task Model) for multi-task sequential probability prediction.
"""

import tensorflow as tf
import tensorflow.keras.layers as layers
import numpy as np
import logging
from src.multitask_learning.esm2_implementation import build_esm2_model
from src.models.layers.layers import CrossLayer
from src.models.layers.cross_layers import create_cross_layer

logger = logging.getLogger(__name__)


class EPMMOE_ESM2Net_Model(tf.keras.Model):
    """
    EPMMOE网络模型与ESM2结合的实现
    实现多任务序列概率预测（例如试驾->预约->购买）
    """
    
    def __init__(self, 
                model_config, 
                default_embedding_dimension=8, 
                default_gru_dimension=32,
                expert_num=8,
                use_cross_layer=True,
                cross_layer_num=3,
                use_multitask=False,
                use_esm2=True,
                use_mixed_precision=True,
                use_time_attention=True,
                time_decay_factor=0.05,
                esm2_task_names=('test_drive_output', 'appointment_output', 'purchase_output')
                ):
        """
        初始化EPMMOE_ESM2Net模型
        
        Args:
            model_config: 模型配置
            default_embedding_dimension: 默认嵌入维度
            default_gru_dimension: 默认GRU维度
            expert_num: 专家网络数量
            use_cross_layer: 是否使用交叉层
            cross_layer_num: 交叉层数量
            use_multitask: 是否使用多任务学习(原始的累积预测)
            use_esm2: 是否使用ESM2模型
            use_mixed_precision: 是否使用混合精度训练
            use_time_attention: 是否使用时间注意力
            time_decay_factor: 时间衰减因子
            esm2_task_names: ESM2任务名称列表
        """
        super(EPMMOE_ESM2Net_Model, self).__init__()
        
        self.model_config = model_config
        self.feature_config = model_config.get("RawFeature", {})
        self.embedding_dim = default_embedding_dimension
        self.gru_dim = default_gru_dimension
        self.expert_num = expert_num
        self.use_cross_layer = use_cross_layer
        self.cross_layer_num = cross_layer_num
        self.use_multitask = use_multitask
        self.use_esm2 = use_esm2
        self.use_mixed_precision = use_mixed_precision
        self.use_time_attention = use_time_attention
        self.time_decay_factor = time_decay_factor
        self.esm2_task_names = esm2_task_names
        self.num_esm2_tasks = len(esm2_task_names)
        
        # Initialize CrossLayer if needed
        if self.use_cross_layer:
            self.cross_layer = CrossLayer(layer_size=128, l2_reg=0.01)
        
        # 设置TensorFlow混合精度策略
        if use_mixed_precision:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
        
        # 初始化特征处理和嵌入层
        self._init_feature_processing()
        
        # 初始化ESM2网络
        if self.use_esm2:
            self._init_esm2_network()
        
        logger.info(f"Initialized EPMMOE_ESM2Net model with {self.num_esm2_tasks} ESM2 tasks: {self.esm2_task_names}")
    
    def _init_feature_processing(self):
        """初始化特征处理组件"""
        # 特征嵌入
        self.embedding_layers = {}
        self.numerical_layers = {}
        
        # 定义分类特征列表
        self.categorical_features = []
        # 定义序列分类特征列表
        self.sequence_categorical_features = []
        
        # 如果feature_config为空，直接返回，确保属性已初始化
        if not self.feature_config:
            logger.warning("Feature config is empty. No features will be processed.")
            return
        
        # 处理类别型特征
        for feature_name, feature_config in self.feature_config.items():
            feature_type = feature_config.get("type", "table")
            feature_dtype = feature_config.get("dtype", "StringLookup")
            
            # 为类别型特征创建嵌入层
            if feature_dtype in ["Embedding", "StringLookup", "IntegerLookup"]:
                vocab_size = feature_config.get("vocab_size", 100)
                embed_dim = feature_config.get("embedding_dim", self.embedding_dim)
                
                self.embedding_layers[feature_name] = tf.keras.layers.Embedding(
                    input_dim=vocab_size,
                    output_dim=embed_dim,
                    name=f"embedding_{feature_name}"
                )
                # 将分类特征名称添加到列表
                self.categorical_features.append(feature_name)
                
                # 检查是否为序列特征（通过特征配置）
                if feature_config.get("is_sequence", False):
                    self.sequence_categorical_features.append(feature_name)
            
            # 为数值型特征创建归一化层
            elif feature_dtype == "Dense":
                self.numerical_layers[feature_name] = tf.keras.layers.Dense(
                    1, activation=None, name=f"numerical_{feature_name}"
                )
    
    def _init_esm2_network(self):
        """初始化ESM2网络组件"""
        # 为ESM2任务配置DNN层
        self.esm2_task_towers = []
        for i, task_name in enumerate(self.esm2_task_names):
            tower = tf.keras.Sequential([
                layers.Dense(64, activation='relu', name=f"esm2_tower_{task_name}_dense1"),
                layers.Dense(32, activation='relu', name=f"esm2_tower_{task_name}_dense2")
            ])
            self.esm2_task_towers.append(tower)
        
        # 为ESM2任务配置逻辑输出层
        self.esm2_logit_layers = []
        for i, task_name in enumerate(self.esm2_task_names):
            layer = layers.Dense(1, use_bias=True, name=f"esm2_logit_{task_name}")
            self.esm2_logit_layers.append(layer)
    
    def call(self, inputs, training=False):
        """Forward pass of the model."""
        # Defensive check to ensure we have inputs
        if not inputs or not isinstance(inputs, dict):
            logger.warning("Empty or invalid inputs provided to the model")
            # Return a default output structure with zeros
            batch_size = 1  # Default batch size
            return {task_name: tf.zeros([batch_size, 1]) for task_name in self.esm2_task_names}
        
        # Process numeric features
        numeric_features = self._process_numeric_features(inputs)
        
        # Process categorical features
        categorical_features = self._process_categorical_features(inputs)
        
        # Combine processed features if they're not empty
        combined_features = []
        
        if numeric_features is not None:
            combined_features.append(numeric_features)
        
        if categorical_features:
            # We need to handle sequence features (3D tensors) differently from regular features (2D tensors)
            regular_features = []
            sequence_features = []
            
            for feature in categorical_features:
                # Check if this is a sequence feature (has 3 dimensions)
                if len(feature.shape) == 3:
                    sequence_features.append(feature)
                else:
                    regular_features.append(feature)
            
            # Concatenate regular features if there are any
            if regular_features:
                if len(regular_features) > 1:
                    embedded_regular = tf.concat(regular_features, axis=1)
                else:
                    embedded_regular = regular_features[0]
                combined_features.append(embedded_regular)
            
            # Process sequence features if there are any
            for seq_feature in sequence_features:
                # Get the dimensions
                batch_size = tf.shape(seq_feature)[0]
                seq_length = tf.shape(seq_feature)[1]
                embed_dim = tf.shape(seq_feature)[2]
                
                # Option 1: Flatten the sequence dimension - just take the mean along sequence dimension
                # This preserves the batch dimension but collapses the sequence
                flattened_seq = tf.reduce_mean(seq_feature, axis=1)
                combined_features.append(flattened_seq)
        
        # If we have features to combine, concatenate them
        if combined_features:
            combined_features = tf.concat(combined_features, axis=1)
        else:
            # If no features, return a zero tensor with appropriate shape
            batch_size = tf.shape(list(inputs.values())[0])[0]
            combined_features = tf.zeros([batch_size, 0], dtype=tf.float32)
        
        # Apply cross layer if enabled
        if self.use_cross_layer:
            cross_features = self.cross_layer(combined_features)
            # Ensure both tensors have the same dtype before concatenation
            combined_dtype = combined_features.dtype
            cross_dtype = cross_features.dtype
            
            if combined_dtype != cross_dtype:
                logger.warning(f"Dtype mismatch in cross layer: combined={combined_dtype}, cross={cross_dtype}. Converting to {combined_dtype}.")
                cross_features = tf.cast(cross_features, combined_dtype)
            
            # 不直接相加，而是拼接，避免维度不匹配问题
            combined_features = tf.concat([combined_features, cross_features], axis=1)
        
        # Apply ESM2 network
        esm2_outputs = self._esm2_network(combined_features, training=training)
        
        return esm2_outputs

    def _process_numeric_features(self, features):
        """Process numerical features.
        
        Args:
            features: Dict of feature tensors.
            
        Returns:
            Processed numerical features.
        """
        numeric_inputs = []
        
        # Extract numerical features
        for feature_name, feature_config in self.feature_config.items():
            # Use get() to safely access the "type" field with a default value
            if feature_config.get("type", "table") == "numerical" and feature_name in features:
                feature = features[feature_name]
                
                # Check if this is a sequence feature
                is_sequence = len(feature.shape) > 2 or (len(feature.shape) == 2 and feature.shape[1] > 1)
                
                # If it's a sequence feature, we need special handling
                if is_sequence:
                    # Reshape to [batch_size, seq_length]
                    feature = tf.reshape(feature, [tf.shape(feature)[0], -1])
                
                # Ensure the feature is at least 2D (batch_size, 1)
                if len(feature.shape) == 1:
                    feature = tf.expand_dims(feature, -1)
                
                # Ensure consistent float type - use float32 for all numeric features
                if feature.dtype != tf.float32:
                    feature = tf.cast(feature, tf.float32)
                    
                # Normalize if needed (assuming we have normalizer attributes set up)
                # This would depend on how normalization is implemented in your model
                
                numeric_inputs.append(feature)
        
        # Concatenate all numerical features if any exist
        if numeric_inputs:
            return tf.concat(numeric_inputs, axis=1)
        return None

    def _process_categorical_features(self, inputs):
        """Process categorical features."""
        embedded_features = []
        for feature_name in self.categorical_features:
            feature_tensor = inputs[feature_name]
            
            # Check tensor dtype and handle string inputs
            tensor_dtype = feature_tensor.dtype
            if tensor_dtype == tf.string:
                # For string inputs, use StringLookup layer to convert to integers
                # This is a workaround for the "Cast string to int32 is not supported" error
                logger.warning(f"Converting string tensor to integer for feature: {feature_name}")
                # Just use a simple hash function for strings
                feature_tensor = tf.strings.to_hash_bucket_fast(feature_tensor, 
                                                                num_buckets=self.embedding_layers[feature_name].input_dim)
            
            # Check if the feature is a sequence feature (has a sequence dimension)
            # Safely check if the attribute exists and if the feature is in it
            is_sequence = hasattr(self, 'sequence_categorical_features') and feature_name in self.sequence_categorical_features
            
            def process_sequence(feature_tensor):
                # Get the shape of the tensor
                feature_shape = tf.shape(feature_tensor)
                tensor_rank = tf.rank(feature_tensor)
                
                # Define functions to handle different tensor ranks
                def has_sequence_dim():
                    # Tensor has at least 2 dimensions, can safely access second dimension
                    seq_length = feature_shape[1]
                    # Flatten the batch and sequence dimensions for embedding lookup
                    batch_size = feature_shape[0]
                    flattened_tensor = tf.reshape(feature_tensor, [-1])
                    # Look up embeddings for all elements in the flattened tensor
                    embeddings = self.embedding_layers[feature_name](flattened_tensor)
                    # Reshape back to [batch_size, seq_length, embedding_dim]
                    reshaped_embeddings = tf.reshape(
                        embeddings, [batch_size, seq_length, self.embedding_dim]
                    )
                    return reshaped_embeddings
                
                def no_sequence_dim():
                    # Tensor is 1D, just apply embedding directly
                    return self.embedding_layers[feature_name](feature_tensor)
                
                # Use tf.cond to conditionally execute the appropriate function based on tensor rank
                return tf.cond(
                    tf.greater_equal(tensor_rank, 2),
                    has_sequence_dim,
                    no_sequence_dim
                )
                
            def process_regular(feature_tensor):
                embeddings = self.embedding_layers[feature_name](feature_tensor)
                # Ensure consistent dtype
                if embeddings.dtype != tf.float32:
                    embeddings = tf.cast(embeddings, tf.float32)
                return embeddings
            
            # Since we're having issues with the cond operation, let's simplify the logic
            # If it's marked as a sequence feature but has rank 1, we'll handle it safely
            if is_sequence:
                tensor_rank = tf.rank(feature_tensor)
                # Only process as sequence if rank >= 2
                if tf.equal(tensor_rank, 1):
                    # Handle as regular feature if it's rank 1 despite being marked as sequence
                    embedded_tensor = process_regular(feature_tensor)
                else:
                    # Handle as sequence feature
                    feature_shape = tf.shape(feature_tensor)
                    batch_size = feature_shape[0]
                    seq_length = feature_shape[1]
                    flattened_tensor = tf.reshape(feature_tensor, [-1])
                    embeddings = self.embedding_layers[feature_name](flattened_tensor)
                    # Ensure consistent dtype
                    if embeddings.dtype != tf.float32:
                        embeddings = tf.cast(embeddings, tf.float32)
                    embedded_tensor = tf.reshape(
                        embeddings, [batch_size, seq_length, self.embedding_dim]
                    )
            else:
                # Regular non-sequence feature
                embedded_tensor = process_regular(feature_tensor)
            
            embedded_features.append(embedded_tensor)
        
        return embedded_features

    def _esm2_network(self, features, training=False):
        """ESM2 network implementation.
        
        Args:
            features: Input features tensor.
            training: Whether in training mode.
            
        Returns:
            Dictionary of task outputs.
        """
        # Defensive check for empty features
        if features is None:
            batch_size = 1
            logger.warning("Received None features in _esm2_network")
            return {task_name: tf.zeros([batch_size, 1]) for task_name in self.esm2_task_names}
        
        # Ensure features is at least 2D with valid shape
        if len(features.shape) < 2:
            features = tf.expand_dims(features, 0)  # Add batch dimension if missing
            logger.warning(f"Features tensor shape was expanded: {features.shape}")
        
        outputs = {}
        
        try:
            # Process each task in the ESM2 model
            for i, task_name in enumerate(self.esm2_task_names):
                # Apply the task tower
                tower_output = self.esm2_task_towers[i](features, training=training)
                # Generate logits
                logits = self.esm2_logit_layers[i](tower_output)
                # Apply sigmoid activation for probability output
                outputs[task_name] = tf.keras.activations.sigmoid(logits)
            
            return outputs
        except Exception as e:
            # If any error occurs, log it and return default outputs
            logger.error(f"Error in _esm2_network: {e}")
            batch_size = tf.shape(features)[0]
            return {task_name: tf.zeros([batch_size, 1]) for task_name in self.esm2_task_names}
        
    def shared_bottom(self, features):
        # Implementation of shared_bottom method
        pass

    def purchase_tower(self, features):
        # Implementation of purchase_tower method
        pass

    def test_drive_tower(self, features):
        # Implementation of test_drive_tower method
        pass

    def purchase_net(self, features):
        """
        Purchase prediction network.
        
        Args:
            features: Combined features
            
        Returns:
            Purchase prediction
        """
        if self.use_esm2:
            # 直接使用task tower和logit layer，而不是调用_esm2_network
            x = self.esm2_task_towers[-1](features)
            purchase_output = self.esm2_logit_layers[-1](x)
            return tf.keras.activations.sigmoid(purchase_output)
        else:
            # Fallback to a simple DNN for purchase prediction
            x = tf.keras.layers.Dense(64, activation='relu', name='purchase_dense_1')(features)
            x = tf.keras.layers.Dense(32, activation='relu', name='purchase_dense_2')(x)
            x = tf.keras.layers.Dense(1, activation='sigmoid', name='purchase_output')(x)
            return x
            
    def test_drive_net(self, features):
        """
        Test drive prediction network.
        
        Args:
            features: Combined features
            
        Returns:
            Test drive prediction
        """
        if self.use_esm2:
            # 直接使用task tower和logit layer，而不是调用_esm2_network
            x = self.esm2_task_towers[0](features)
            test_drive_output = self.esm2_logit_layers[0](x)
            return tf.keras.activations.sigmoid(test_drive_output)
        else:
            # Fallback to a simple DNN for test drive prediction
            x = tf.keras.layers.Dense(64, activation='relu', name='test_drive_dense_1')(features)
            x = tf.keras.layers.Dense(32, activation='relu', name='test_drive_dense_2')(x)
            x = tf.keras.layers.Dense(1, activation='sigmoid', name='test_drive_output')(x)
            return x 