"""
ESM2模型实现 (Extended Space Multi-task Model)
为NIO电动车购买预测场景的改进实现
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, Embedding, Flatten, Concatenate, Multiply, Dropout, BatchNormalization
from tensorflow.keras.models import Model
from tensorflow.keras.regularizers import l2
from keras.saving import register_keras_serializable


@register_keras_serializable(package="esm2_implementation")
class DNN(tf.keras.layers.Layer):
    """DNN层实现，用于构建深度网络"""
    
    def __init__(self, hidden_units, activation='relu', dropout_rate=0, use_bn=False, l2_reg=0, **kwargs):
        """
        初始化DNN层
        
        Args:
            hidden_units: DNN网络隐藏层单元数列表
            activation: 激活函数
            dropout_rate: dropout比率
            use_bn: 是否使用批归一化
            l2_reg: L2正则化系数
        """
        super(DNN, self).__init__(**kwargs)
        self.hidden_units = hidden_units
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.use_bn = use_bn
        self.l2_reg = l2_reg
        
        self.dense_layers = []
        self.dropout_layers = []
        self.bn_layers = []
        
        for units in hidden_units:
            self.dense_layers.append(
                Dense(units, 
                      activation=activation,
                      kernel_regularizer=l2(l2_reg))
            )
            if use_bn:
                self.bn_layers.append(BatchNormalization())
            if dropout_rate > 0:
                self.dropout_layers.append(Dropout(dropout_rate))
    
    def call(self, inputs, training=None):
        """
        前向传播
        
        Args:
            inputs: 输入张量
            training: 是否为训练模式
            
        Returns:
            输出张量
        """
        x = inputs
        count_dense = 0
        
        for i in range(len(self.hidden_units)):
            x = self.dense_layers[i](x)
            count_dense += 1
            
            if self.use_bn:
                x = self.bn_layers[i](x, training=training)
                
            if self.dropout_rate > 0:
                x = self.dropout_layers[i](x, training=training)
        
        return x
        
    def get_config(self):
        """获取配置"""
        config = super(DNN, self).get_config()
        config.update({
            "hidden_units": self.hidden_units,
            "activation": self.activation,
            "dropout_rate": self.dropout_rate,
            "use_bn": self.use_bn,
            "l2_reg": self.l2_reg,
        })
        return config
        
    @classmethod
    def from_config(cls, config):
        """从配置创建实例"""
        return cls(**config)


def build_esm2_model(feature_config, embedding_dim=16, dnn_hidden_units=(256, 128, 64),
         dnn_activation='relu', dnn_dropout=0, dnn_use_bn=False, l2_reg_embedding=0.00001,
         l2_reg_dnn=0, seed=1024, task_names=('test_drive_output', 'appointment_output', 'purchase_output')):
    """
    构建ESM2模型 (Extended Space Multi-task Model)

    Args:
        feature_config: 特征配置信息
        embedding_dim: 嵌入维度
        dnn_hidden_units: 每个任务塔的DNN隐藏层单元数
        dnn_activation: DNN激活函数
        dnn_dropout: DNN的dropout率
        dnn_use_bn: 是否在DNN中使用批归一化
        l2_reg_embedding: 嵌入层的L2正则化系数
        l2_reg_dnn: DNN的L2正则化系数
        seed: 随机种子
        task_names: 有序的任务名称列表/元组，决定了概率链条和输出。
                    默认为 ('test_drive_output', 'appointment_output', 'purchase_output') 
                    对应试驾、预约试驾、购车三个任务

    Returns:
        ESM2模型
    """
    num_tasks = len(task_names)
    if num_tasks < 2:
        raise ValueError("ESM2至少需要定义两个任务 (例如 test_drive 和 purchase)")

    tf.random.set_seed(seed)

    # --- 输入层和嵌入层 ---
    inputs = {}
    numerical_inputs = []
    
    # 提取必要的特征配置
    numerical_cols = feature_config.get('numerical_cols', [])
    categorical_cols = feature_config.get('categorical_cols', [])
    categorical_dims = feature_config.get('categorical_dims', {})
    
    # 处理数值型特征
    for col in numerical_cols:
        inputs[f'numerical_{col}'] = Input(shape=(1,), name=f'numerical_{col}')
        numerical_inputs.append(inputs[f'numerical_{col}'])

    # 处理类别型特征
    embedding_list = []
    for col in categorical_cols:
        inputs[f'categorical_{col}'] = Input(shape=(1,), name=f'categorical_{col}')
        embedding = Embedding(
            input_dim=categorical_dims.get(col, 100),  # 使用默认值以防止key错误
            output_dim=embedding_dim,
            embeddings_regularizer=l2(l2_reg_embedding),
            name=f'embedding_{col}'
        )(inputs[f'categorical_{col}'])
        embedding = Flatten()(embedding)
        embedding_list.append(embedding)

    # 合并所有特征
    if numerical_inputs:
        numerical_features = Concatenate()(numerical_inputs) if len(numerical_inputs) > 1 else numerical_inputs[0]
        shared_features = Concatenate(name="shared_features")([numerical_features] + embedding_list) if embedding_list else numerical_features
    else:
        shared_features = Concatenate(name="shared_features")(embedding_list) if len(embedding_list) > 1 else embedding_list[0]

    # --- 构建 N 个任务塔 ---
    task_towers = []
    for i in range(num_tasks):
        tower = DNN(dnn_hidden_units, dnn_activation, dnn_dropout, dnn_use_bn, l2_reg_dnn, name=f'tower_{task_names[i]}')(shared_features)
        task_towers.append(tower)

    # --- 构建概率输出链 ---
    outputs = []
    last_prob = None

    for i in range(num_tasks):
        logit = Dense(1, use_bias=True, name=f'logit_{task_names[i]}')(task_towers[i])

        # 第一个任务直接计算概率 p_task_1
        if i == 0:
            prob = tf.keras.layers.Activation('sigmoid', name=task_names[i])(logit)
            outputs.append(prob)
            last_prob = prob
        # 后续任务计算条件概率 p_cond_i，然后乘以之前的联合概率 p_task_i = p_task_{i-1} * p_cond_i
        else:
            cond_prob = tf.keras.layers.Activation('sigmoid', name=f'cond_prob_{i}')(logit)
            prob = Multiply(name=task_names[i])([last_prob, cond_prob])
            outputs.append(prob)
            last_prob = prob

    # 构建模型
    # 将输出组织成字典，使用task_names作为键名
    named_outputs = {task_names[i]: outputs[i] for i in range(num_tasks)}
    model = Model(inputs=list(inputs.values()), outputs=named_outputs)

    return model

def compile_esm2_model(model, task_names, learning_rate=0.001, loss_weights=None):
    """
    编译ESM2模型
    
    Args:
        model: ESM2模型
        task_names: 与模型输出顺序一致的任务名称列表/元组。
        learning_rate: 学习率
        loss_weights: 字典，映射任务名称到损失权重。如果为None，则所有权重设为1.0。
        
    Returns:
        编译后的模型
    """
    # Use the provided task_names directly instead of inferring from model.outputs
    output_names = list(task_names) 
    num_tasks = len(output_names)

    if len(model.outputs) != num_tasks:
        raise ValueError(f"模型输出数量 ({len(model.outputs)}) 与提供的 task_names 数量 ({num_tasks}) 不匹配")

    if loss_weights is None:
        loss_weights = {name: 1.0 for name in output_names}
    elif not isinstance(loss_weights, dict) or set(loss_weights.keys()) != set(output_names):
         raise ValueError(f"loss_weights 必须是一个字典，且其键必须与提供的 task_names 完全匹配: {output_names}")

    # Build dictionaries using the provided task_names
    loss_dict = {name: 'binary_crossentropy' for name in output_names}
    metrics_dict = {name: ['AUC', 'accuracy'] for name in output_names}

    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
        loss=loss_dict,
        loss_weights=loss_weights,
        metrics=metrics_dict
    )
    
    return model 