"""
Trainer module for conversion rate prediction models.
"""
import tensorflow as tf
import numpy as np
import pandas as pd
import logging
import json
import importlib
from pathlib import Path
import time


class ModelTrainer:
    """Model trainer class that handles model training and evaluation."""
    
    def __init__(self, model_config, run_name, output_dir=None):
        """
        Initialize model trainer.
        
        Args:
            model_config (dict): Model configuration.
            run_name (str): Name of this training run.
            output_dir (str, optional): Directory to store outputs.
        """
        self.model_config = model_config
        self.run_name = run_name
        self.output_dir = output_dir or run_name
        self.logger = logging.getLogger(__name__)
        
        # Create output directory
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        # Initialize model parameters
        self.network_name = model_config.get("network_name")
        self.predict_method = model_config.get("predict_method", "6m")
        self.mask_label = model_config.get("mask_label", None)
        self.val_metric = model_config.get("val_metric", "loss")
        self.batch_size = model_config.get("batch_size", 8192)
        self.loss_type = model_config.get("loss_type", "standard")
        self.pos_weight = model_config.get("pos_weight", 10.0)
        self.use_month_weights = model_config.get("use_month_weights", False)
        self.use_multitask = model_config.get("use_multitask", False)
        self.use_esmm = model_config.get("use_esmm", False)
        self.use_esm2 = model_config.get("use_esm2", False)
        self.esm2_task_names = model_config.get("esm2_task_names", ('test_drive_output', 'appointment_output', 'purchase_output'))
        self.time_decay_factor = model_config.get("time_decay_factor", 0.05)
        self.use_mixed_precision = model_config.get("use_mixed_precision", True)
        self.model = None
        
        self.logger.info(f"Trainer initialized with network: {self.network_name}")
        self.logger.info(f"Prediction method: {self.predict_method}")
        self.logger.info(f"Validation metric: {self.val_metric}")
        self.logger.info(f"Batch size: {self.batch_size}")
        self.logger.info(f"Loss type: {self.loss_type}")
        self.logger.info(f"Positive class weight: {self.pos_weight}")
        self.logger.info(f"Use month weights: {self.use_month_weights}")
        self.logger.info(f"Use multitask learning: {self.use_multitask}")
        self.logger.info(f"Use ESMM model: {self.use_esmm}")
        self.logger.info(f"Use ESM2 model: {self.use_esm2}")

        # 确保不同的多任务学习模式不会同时启用
        if sum([self.use_multitask, self.use_esmm, self.use_esm2]) > 1:
            raise ValueError("Cannot enable multiple learning modes (use_multitask, use_esmm, use_esm2) simultaneously.")
    
    def _build_loss_function(self):
        """
        Build appropriate loss function.
        
        Returns:
            function: Loss function.
        """
        # 如果使用多任务学习，则使用多任务损失函数
        if self.use_multitask:
            from src.training.losses import multitask_loss
            
            def loss_fn(y_true, y_pred):
                return multitask_loss(y_true, y_pred)
            return loss_fn
            
        # Import necessary loss functions
        elif self.mask_label:
            from src.training.losses import (
                cumsum_mask_loss, weighted_cumsum_mask_loss, 
                asymmetric_focal_mask_loss
            )
            
            # Use appropriate masked loss based on loss_type
            if self.loss_type == "weighted":
                # Create a closure with fixed pos_weight
                def loss_fn(y_true, y_pred):
                    return weighted_cumsum_mask_loss(y_true, y_pred, pos_weight=self.pos_weight, use_month_weights=self.use_month_weights)
                return loss_fn
            elif self.loss_type == "focal":
                def loss_fn(y_true, y_pred):
                    return asymmetric_focal_mask_loss(
                        y_true, y_pred, 
                        gamma_pos=2.0, gamma_neg=4.0, 
                        alpha=0.25, pos_weight=self.pos_weight, 
                        use_month_weights=self.use_month_weights
                    )
                return loss_fn
            else:
                def loss_fn(y_true, y_pred):
                    return cumsum_mask_loss(y_true, y_pred, use_month_weights=self.use_month_weights)
                return loss_fn
        else:
            from src.training.losses import (
                cumsum_loss, weighted_cumsum_loss, 
                focal_cumsum_loss, asymmetric_focal_cumsum_loss
            )
            
            # Use appropriate loss based on loss_type
            if self.loss_type == "weighted":
                # Create a closure with fixed pos_weight
                def loss_fn(y_true, y_pred):
                    return weighted_cumsum_loss(y_true, y_pred, pos_weight=self.pos_weight, use_month_weights=self.use_month_weights)
                return loss_fn
            elif self.loss_type == "focal":
                # Create a closure with fixed parameters
                def loss_fn(y_true, y_pred):
                    return focal_cumsum_loss(y_true, y_pred, alpha=0.35, gamma=2.5, use_month_weights=self.use_month_weights)
                return loss_fn
            elif self.loss_type == "asymmetric_focal":
                # 使用非对称焦点损失
                def loss_fn(y_true, y_pred):
                    return asymmetric_focal_cumsum_loss(
                        y_true, y_pred, 
                        gamma_pos=2.0, gamma_neg=4.0, 
                        alpha=0.25, pos_weight=self.pos_weight, 
                        use_month_weights=self.use_month_weights
                    )
                return loss_fn
            else:
                def loss_fn(y_true, y_pred):
                    return cumsum_loss(y_true, y_pred, use_month_weights=self.use_month_weights)
                return loss_fn
    
    def build_model(self, use_cross_layer=True, use_time_attention=True, time_decay_factor=0.02, use_transformer=False):
        """
        Build model from configuration.
        
        Args:
            use_cross_layer (bool): Whether to use feature cross layer.
            use_time_attention (bool): Whether to use time attention mechanism.
            time_decay_factor (float): Time decay factor for attention.
            use_transformer (bool): Whether to use Transformer instead of GRU.
            
        Returns:
            tf.keras.Model: Built model.
        """
        self.logger.info(f"Building model {self.network_name}")
        self.logger.info(f"Cross layer: {use_cross_layer}, Time attention: {use_time_attention}")
        self.logger.info(f"Time decay factor: {time_decay_factor}")
        self.logger.info(f"Using Transformer architecture: {use_transformer}")
        
        # 使用较低学习率和梯度累积以增强Transformer训练稳定性
        learning_rate = 0.0003 if use_transformer else 0.0005 
        
        # Dynamically import model module
        try:
            # 判断是否使用ESM2模型
            if self.use_esm2 and self.network_name in ["EPMMOENet"]:
                # 对于ESM2模型，我们选择专门针对ESM2开发的网络模型
                model_module = importlib.import_module(f"src.models.networks.EPMMOE_ESM2Net")
                model_class = getattr(model_module, f"EPMMOE_ESM2Net_Model")
                self.logger.info(f"Using ESM2-based model: EPMMOE_ESM2Net_Model")
                
                # 创建ESM2模型实例
                self.model = model_class(
                    self.model_config,
                    use_multitask=self.use_multitask,
                    use_esm2=self.use_esm2,
                    use_cross_layer=use_cross_layer,
                    use_mixed_precision=False,  # 先关闭混合精度
                    use_time_attention=use_time_attention,
                    time_decay_factor=time_decay_factor,
                    esm2_task_names=self.esm2_task_names
                )
            else:
                model_module = importlib.import_module(f"src.models.networks.{self.network_name}")
                
                # Select appropriate model class based on parameters
                if use_transformer:
                    model_class = getattr(model_module, f"{self.network_name}_TransformerModel")
                    self.logger.info(f"Using Transformer-based model: {self.network_name}_TransformerModel")
                    
                    # Create model instance with transformer architecture
                    self.model = model_class(
                        self.model_config,
                        use_multitask=self.use_multitask,
                        use_esmm=self.use_esmm,
                        use_cross_layer=use_cross_layer,
                        use_mixed_precision=False,  # Turn off mixed precision initially
                        use_time_attention=use_time_attention,
                        time_decay_factor=time_decay_factor,
                        num_heads=4,  # Default number of attention heads
                        ff_dim=128    # Default feed-forward dimension
                    )
                else:
                    model_class = getattr(model_module, f"{self.network_name}_Model")
                    self.logger.info(f"Using standard model: {self.network_name}_Model")
                    
                    # Create model instance
                    self.model = model_class(
                        self.model_config,
                        use_multitask=self.use_multitask,
                        use_esmm=self.use_esmm,
                        use_cross_layer=use_cross_layer,
                        use_mixed_precision=False,  # Turn off mixed precision initially
                        use_time_attention=use_time_attention,
                        time_decay_factor=time_decay_factor
                    )
            
            # Configure training settings (loss, optimizer, metrics) which includes compile
            self._configure_training() # Call configure *after* model instantiation

            return self.model
            
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to build model: {e}")
            raise
    
    def train(self, train_dataset, validation_dataset, epochs=50, early_stopping_patience=10):
        """
        Train model with provided datasets.
        
        Args:
            train_dataset (tf.data.Dataset): Training dataset.
            validation_dataset (tf.data.Dataset): Validation dataset.
            epochs (int): Maximum number of training epochs.
            early_stopping_patience (int): Early stopping patience.
            
        Returns:
            tf.keras.callbacks.History: Training history.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            raise ValueError("Model not built. Call build_model() first.")
            
        # Create output path for model weights
        model_path = f"{self.output_dir}/best_model.weights.h5"
        Path(model_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=early_stopping_patience,
                min_delta=0.001,
                mode='min',
                restore_best_weights=True
            ),
            tf.keras.callbacks.ModelCheckpoint(
                model_path,
                monitor='val_loss',
                mode='min',
                save_best_only=True,
                save_weights_only=True
            ),
            # 添加学习率调度回调
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=0.0001,
                verbose=1
            )
        ]
        
        # Log training start
        self.logger.info(f"Starting model training for {epochs} epochs")
        start_time = time.time()
        
        # Train model
        history = self.model.fit(
            train_dataset,
            validation_data=validation_dataset,
            epochs=epochs,
            callbacks=callbacks
        )
        
        # Save training history
        history_path = f"{self.output_dir}/training_history.json"
        with open(history_path, 'w') as f:
            json.dump(history.history, f)
            
        # Save model configuration
        config_path = f"{self.output_dir}/model_config.json"
        with open(config_path, 'w') as f:
            json.dump(self.model_config, f)
            
        # Log training completion
        training_time = time.time() - start_time
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        
        return history
    
    def load_model_weights(self, weights_path=None):
        """
        Load model weights from file.
        
        Args:
            weights_path (str, optional): Path to model weights file.
            
        Returns:
            bool: Whether loading was successful.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            return False
            
        if weights_path is None:
            weights_path = f"{self.output_dir}/best_model.weights.h5"
            
        try:
            self.model.load_weights(weights_path)
            self.logger.info(f"Model weights loaded from {weights_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to load model weights: {e}")
            return False
    
    def inference(self, df, raw_features, batch_size=10000):
        """
        Run model inference on dataframe.
        
        Args:
            df (pd.DataFrame): Input dataframe.
            raw_features (dict): Raw feature configuration.
            batch_size (int): Maximum batch size for inference.
            
        Returns:
            np.ndarray: Model predictions.
        """
        if self.model is None:
            self.logger.error("Model not built. Call build_model() first.")
            raise ValueError("Model not built. Call build_model() first.")
            
        from src.features.builder import FeatureBuilder
        feature_builder = FeatureBuilder()
        
        # For smaller datasets, run inference in one batch
        if len(df) <= batch_size:
            features = feature_builder.generate_dataset(df, raw_features)
            predictions = self.model(features)
            
            # Handle different output formats
            if self.use_esmm:
                # For ESMM, we get a dict with 'test_drive_output' and 'purchase_output'
                # We use the purchase_output as our primary prediction
                predictions = predictions['purchase_output'].numpy()
            elif self.use_esm2:
                # For ESM2, we get a dict with all task outputs 
                # We use the final task output (usually purchase) as primary prediction
                predictions = predictions[self.esm2_task_names[-1]].numpy()
            else:
                predictions = predictions.numpy()
        # For larger datasets, run inference in batches
        else:
            self.logger.info(f"Running batched inference with batch size {batch_size}")
            predictions_list = []
            inference_batches = int(len(df) / batch_size) + 1
            
            for idx in range(inference_batches):
                start, end = idx * batch_size, min((idx + 1) * batch_size, len(df))
                self.logger.info(f"Processing batch {idx+1}/{inference_batches}: {start}-{end}")
                
                batch_df = df.iloc[start:end]
                batch_features = feature_builder.generate_dataset(batch_df, raw_features)
                batch_predictions = self.model(batch_features)
                
                # Handle different output formats
                if self.use_esmm:
                    # For ESMM, we get a dict with 'test_drive_output' and 'purchase_output'
                    # We use the purchase_output as our primary prediction
                    batch_predictions = batch_predictions['purchase_output'].numpy()
                elif self.use_esm2:
                    # For ESM2, we get a dict with all task outputs
                    # We use the final task output (usually purchase) as primary prediction
                    batch_predictions = batch_predictions[self.esm2_task_names[-1]].numpy()
                else:
                    batch_predictions = batch_predictions.numpy()
                    
                predictions_list.append(batch_predictions)
                
            predictions = np.concatenate(predictions_list, axis=0)
            
        # If ESMM or ESM2 mode and output is a single column, expand it to the expected 6-month array format
        if (self.use_esmm or self.use_esm2) and len(predictions.shape) > 0 and predictions.shape[-1] == 1:
            # Replicate the purchase probability across all 6 months
            # This maintains compatibility with the standard model output format
            predictions = np.repeat(predictions, 6, axis=-1)
        
        # Post-process predictions to ensure sequential constraint
        for i in range(1, predictions.shape[1]):
            predictions[:, i] = np.maximum(predictions[:, i], predictions[:, i-1])
        
        # Ensure predictions are in valid range
        predictions = np.clip(predictions, 0.0, 0.999999)
        
        # Calculate incremental predictions if needed
        incremental = np.concatenate(
            (predictions[:, 0:1], np.clip(np.diff(predictions, axis=-1), 0.0, 1.0)),
            axis=-1
        )
        
        return predictions

    def _configure_training(self):
        """Configure optimizer, loss, and metrics."""
        # Determine learning rate based on model type
        learning_rate = 0.0003 if self.model_config.get("use_transformer", False) else 0.0005 
        self.learning_rate = learning_rate # Store it for potential later use

        if self.use_mixed_precision:
            # Use mixed precision optimizer
            self.optimizer = tf.keras.mixed_precision.LossScaleOptimizer(
                tf.keras.optimizers.Adam(learning_rate=self.learning_rate, epsilon=1e-07)
            )
        else:
            self.optimizer = tf.keras.optimizers.Adam(learning_rate=self.learning_rate, epsilon=1e-07)

        # Define loss and metrics based on mode
        if self.use_esm2:
            # ESM2 mode: multiple task outputs configured by esm2_task_names
            self.loss = {name: tf.keras.losses.BinaryCrossentropy() for name in self.esm2_task_names}
            self.loss_weights = {name: 1.0 for name in self.esm2_task_names}
            self.metrics = {
                name: [tf.keras.metrics.AUC(name=f'auc_{name}'), tf.keras.metrics.BinaryAccuracy(name=f'acc_{name}')]
                for name in self.esm2_task_names
            }
            self.logger.info(f"Configured training for ESM2 mode with tasks: {self.esm2_task_names}")
        elif self.use_esmm:
            # ESMM mode: two outputs - test_drive_output, purchase_output
            self.loss = {
                'test_drive_output': tf.keras.losses.BinaryCrossentropy(),
                'purchase_output': tf.keras.losses.BinaryCrossentropy()
            }
            self.loss_weights = {
                'test_drive_output': 1.0, # Example weight, adjust as needed
                'purchase_output': 1.0  # Example weight, adjust as needed
            }
            self.metrics = {
                'test_drive_output': [tf.keras.metrics.AUC(name='auc_test_drive'), tf.keras.metrics.BinaryAccuracy(name='acc_test_drive')],
                'purchase_output': [tf.keras.metrics.AUC(name='auc_purchase'), tf.keras.metrics.BinaryAccuracy(name='acc_purchase')]
            }
            self.logger.info("Configured training for ESMM mode (test drive & purchase).")

        elif self.use_multitask:
            # Original multitask mode (time-based)
            # You should have specific loss/metrics configuration here for the original multitask mode
            # This part seems incomplete in the previous code state
            self.logger.warning("Original multitask loss/metrics configuration might be missing/incomplete.")
            # Placeholder: adapt based on your original multitask implementation needs
            loss_func = self._build_loss_function() # Use the previously defined logic if applicable
            self.loss = loss_func 
            self.metrics = [tf.keras.metrics.AUC(name='auc_multitask')] # Example metric
            self.loss_weights = None 
            self.logger.info("Configured training for original time-based multitask mode.")
            
        else:
            # Original single-task mode
            # Make sure _build_loss_function covers the single-task case correctly
            loss_func = self._build_loss_function() 
            self.loss = loss_func
            self.metrics = [tf.keras.metrics.AUC(name='auc_single_task')] # Example metric
            self.loss_weights = None # No specific weights for single task loss function
            self.logger.info("Configured training for single-task mode.")

        # --- Compile the model here --- 
        if self.model:
            self.model.compile(
                optimizer=self.optimizer,
                loss=self.loss,
                loss_weights=self.loss_weights,
                metrics=self.metrics
            )
            self.logger.info("Model compiled successfully.")
        else:
            self.logger.error("Attempted to compile model before it was built.")

    def _prepare_dataset(self, dataset, batch_size, is_training=True):
        """
        Prepare dataset for training or evaluation.
        
        Args:
            dataset (tf.data.Dataset): Input dataset.
            batch_size (int): Batch size.
            is_training (bool): Whether in training mode.
            
        Returns:
            tf.data.Dataset: Prepared dataset.
        """
        def map_fn(features, label):
            # 检查数据集是否已经为ESM2模式正确格式化
            if self.use_esm2 and isinstance(label, dict) and all(name in label for name in self.esm2_task_names):
                # 数据集已经具有正确的ESM2标签格式，无需更改
                return features, label
            elif self.use_esm2:
                # 需要将标签转换为ESM2格式
                # 对于此实现使用NIO数据：
                # - test_drive_output: 用户是否会进行试驾
                # - appointment_output: 用户是否会预约试驾
                # - purchase_output: 用户是否会购买车辆
                
                # 使用标准转换标签作为购买标签（二元转换指标）
                purchase_label = label
                
                # 如果purchase_label是多维的（例如6个月），将其简化为二进制
                if tf.rank(purchase_label) > 1:
                    purchase_label = tf.cast(tf.reduce_any(purchase_label > 0, axis=1), tf.float32)
                    
                # 为测试驾驶创建一个代理标签，假设任何购买车辆的人肯定进行了测试驾驶
                # 加上一些随机的额外测试驾驶样本
                test_drive_prob = tf.random.uniform(tf.shape(purchase_label)) < 0.4  # 较高的试驾比率
                test_drive_label = tf.cast(
                    tf.logical_or(
                        purchase_label > 0,  # 任何购买的人一定进行了试驾
                        test_drive_prob  # 加上一些额外的试驾
                    ), 
                    tf.float32
                )
                
                # 为预约试驾创建一个代理标签，假设所有试驾的人都进行了预约
                # 加上一些随机的额外预约样本
                appoint_prob = tf.random.uniform(tf.shape(purchase_label)) < 0.5
                appointment_label = tf.cast(
                    tf.logical_or(
                        test_drive_label > 0,  # 任何试驾的人一定进行了预约
                        appoint_prob  # 加上一些额外的预约
                    ),
                    tf.float32
                )
                
                # 创建ESM2模型期望的标签字典
                labels = {}
                for i, name in enumerate(self.esm2_task_names):
                    if name == 'test_drive_output':
                        labels[name] = test_drive_label
                    elif name == 'appointment_output':
                        labels[name] = appointment_label
                    elif name == 'purchase_output':
                        labels[name] = purchase_label
                    else:
                        # 对于其他任务名称，使用更随机的标签
                        if i == 0:  # 第一个任务
                            labels[name] = tf.cast(tf.random.uniform(tf.shape(purchase_label)) < 0.6, tf.float32)
                        else:  # 后续任务
                            prev_task = self.esm2_task_names[i-1]
                            prev_label = labels[prev_task]
                            # 后续任务基于前一个任务，但有条件概率
                            rand_prob = tf.random.uniform(tf.shape(purchase_label)) < 0.7
                            labels[name] = tf.cast(
                                tf.logical_and(
                                    prev_label > 0,
                                    rand_prob
                                ),
                                tf.float32
                            )
                
                return features, labels
            # Check if the dataset is already properly formatted for ESMM mode
            elif self.use_esmm and isinstance(label, dict) and 'test_drive_output' in label and 'purchase_output' in label:
                # Dataset already has the proper ESMM label format, no changes needed
                return features, label
            elif self.use_esmm:
                # Need to convert the label to ESMM format
                # For this implementation using NIO data:
                # - test_drive_output: Whether user will book a test drive (could use test_drive label if available)
                # - purchase_output: Whether user will purchase a car
                
                # Using the standard conversion label for purchase (binary conversion indicator)
                purchase_label = label
                
                # If purchase_label is multi-dimensional (e.g., 6 months), reduce it to binary
                if tf.rank(purchase_label) > 1:
                    purchase_label = tf.cast(tf.reduce_any(purchase_label > 0, axis=1), tf.float32)
                    
                # For test drive, we'll create a proxy label assuming test drive is a prerequisite for purchase
                # If there's already a specific test_drive column in the dataset, we should use that instead
                test_drive_label = tf.cast(
                    tf.logical_or(
                        purchase_label > 0,  # Anyone who purchases definitely did a test drive
                        tf.random.uniform(tf.shape(purchase_label)) < 0.3  # Plus some additional test drives
                    ), 
                    tf.float32
                )
                
                # Create the dictionary of labels expected by the ESMM model
                labels = {
                    'test_drive_output': test_drive_label,
                    'purchase_output': purchase_label
                }
                return features, labels
            elif self.mask_label:
                # Original multitask or single task with mask
                mask = tf.cast(tf.greater(label, -1), tf.float32) # Mask for valid labels
                return features, label, mask
            else:
                # Original single task without mask
                return features, label
        
        dataset = dataset.map(map_fn, num_parallel_calls=tf.data.AUTOTUNE)
        
        # Batch and prefetch for efficient data loading
        if is_training:
            dataset = dataset.shuffle(10000).batch(batch_size).prefetch(tf.data.AUTOTUNE)
        else:
            dataset = dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)
            
        return dataset