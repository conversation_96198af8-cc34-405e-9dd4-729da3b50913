#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化19: 简化版早期模型核心模式
专注于早期模型最核心的成功要素：分桶特征 + 多时间窗口 + 简单架构
基线: AUC 0.8219 (SMOTE + 集成)
目标: 提取早期模型的核心成功要素，避免过度复杂化
"""
import sys
import os
import json
import logging
import pandas as pd
import numpy as np
import tensorflow as tf
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_recall_curve, auc
from sklearn.preprocessing import StandardScaler, KBinsDiscretizer
from imblearn.over_sampling import SMOTE

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from data.nio_loader import NioDataLoader

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_pr_auc(y_true, y_pred_proba):
    """计算PR-AUC"""
    precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
    pr_auc = auc(recall, precision)
    return pr_auc

def calculate_precision_recall_at_k(y_true, y_pred_proba, k=840):
    """计算Precision@K和Recall@K"""
    sorted_indices = np.argsort(y_pred_proba.flatten())[::-1]
    top_k_indices = sorted_indices[:k]
    
    true_positives = np.sum(y_true[top_k_indices])
    precision_at_k = true_positives / k
    
    total_positives = np.sum(y_true)
    recall_at_k = true_positives / total_positives if total_positives > 0 else 0
    
    return precision_at_k, recall_at_k

def simplified_early_feature_engineering(df):
    """简化版早期模型特征工程 - 专注核心要素"""
    logger.info("简化版早期模型特征工程...")
    
    # 1. 基础数值特征
    numeric_cols = [col for col in df.columns 
                   if df[col].dtype in ['int64', 'float64'] 
                   and col not in ['user_id', 'datetime', 'purchase_days_nio_new_car_total']]
    
    # 选择前150个特征作为基础
    base_features = numeric_cols[:150]
    df_features = df[base_features].fillna(0)
    
    logger.info(f"基础特征数: {len(base_features)}")
    
    # 2. 核心改进1: 多时间窗口聚合特征 (学习早期模型最重要的特征)
    time_windows = ['1d', '7d', '30d', '90d']
    
    for window in time_windows:
        window_cols = [col for col in base_features if f'{window}_cnt' in col]
        if window_cols:
            # 时间窗口总行为数 (最重要的特征)
            df_features[f'total_actions_{window}'] = df_features[window_cols].sum(axis=1)
            
            # 时间窗口行为多样性
            df_features[f'action_diversity_{window}'] = (df_features[window_cols] > 0).sum(axis=1)
            
            # 时间窗口行为强度
            df_features[f'action_intensity_{window}'] = df_features[window_cols].max(axis=1)
    
    # 3. 核心改进2: 分桶特征 (学习早期模型的bin_boundaries)
    # 选择高方差的特征进行分桶
    high_variance_cols = []
    for col in df_features.columns:
        if df_features[col].std() > 1 and df_features[col].max() > 10:
            high_variance_cols.append(col)
    
    # 对前10个高方差特征进行分桶
    logger.info(f"对{min(10, len(high_variance_cols))}个高方差特征进行分桶...")
    
    for col in high_variance_cols[:10]:
        try:
            # 使用分位数分桶 (模拟早期模型的bin_boundaries)
            discretizer = KBinsDiscretizer(n_bins=5, encode='ordinal', strategy='quantile')
            col_values = df_features[col].values.reshape(-1, 1)
            
            # 分桶特征
            df_features[f'{col}_bucket'] = discretizer.fit_transform(col_values).flatten()
            
            # 零值指示特征
            df_features[f'{col}_is_zero'] = (df_features[col] == 0).astype(int)
            
        except Exception as e:
            logger.warning(f"分桶处理失败 {col}: {e}")
            continue
    
    # 4. 核心改进3: DSLA特征 (时间衰减)
    action_cols = [col for col in base_features if 'action' in col and 'cnt' in col]
    if action_cols:
        for col in action_cols[:10]:  # 对前10个行为特征计算DSLA
            # 简化的时间衰减权重
            df_features[f'{col}_dsla'] = np.where(
                df_features[col] > 0,
                1.0 / (1.0 + 0.1 * df_features[col]),  # 简化的时间衰减
                0.0
            )
    
    # 5. 核心改进4: 行为趋势特征 (时间窗口间变化)
    if 'total_actions_1d' in df_features.columns and 'total_actions_7d' in df_features.columns:
        df_features['trend_7d_1d'] = df_features['total_actions_7d'] - df_features['total_actions_1d']
        df_features['ratio_7d_1d'] = df_features['total_actions_7d'] / (df_features['total_actions_1d'] + 1)
    
    if 'total_actions_30d' in df_features.columns and 'total_actions_7d' in df_features.columns:
        df_features['trend_30d_7d'] = df_features['total_actions_30d'] - df_features['total_actions_7d']
        df_features['ratio_30d_7d'] = df_features['total_actions_30d'] / (df_features['total_actions_7d'] + 1)
    
    # 6. 核心改进5: 业务特征 (购车相关)
    car_keywords = ['book_td', 'exp_td', 'lock_ncar', 'pay_ncar', 'order']
    car_action_cols = [col for col in base_features 
                      if any(keyword in col for keyword in car_keywords)]
    
    if car_action_cols:
        df_features['total_car_actions'] = df_features[car_action_cols].sum(axis=1)
        df_features['car_action_diversity'] = (df_features[car_action_cols] > 0).sum(axis=1)
    
    # 7. 核心改进6: 核心行为特征
    core_action_cols = [col for col in base_features if 'user_core' in col and 'cnt' in col]
    if core_action_cols:
        df_features['total_core_actions'] = df_features[core_action_cols].sum(axis=1)
        df_features['core_action_diversity'] = (df_features[core_action_cols] > 0).sum(axis=1)
    
    # 8. 核心改进7: 关键交互特征
    if 'total_core_actions' in df_features.columns and 'total_car_actions' in df_features.columns:
        df_features['core_car_interaction'] = df_features['total_core_actions'] * df_features['total_car_actions']
        df_features['purchase_intent_score'] = (
            df_features['total_car_actions'] * 2 + 
            df_features['total_core_actions'] * 1
        )
    
    # 9. 基础统计特征
    df_features['feature_sum'] = df_features.sum(axis=1)
    df_features['feature_mean'] = df_features.mean(axis=1)
    df_features['nonzero_feature_count'] = (df_features > 0).sum(axis=1)
    
    logger.info(f"简化特征工程完成: {df_features.shape[1]}个特征")
    
    return df_features.values, list(df_features.columns)

def create_simplified_early_model_1(input_dim):
    """创建简化版早期模型1 - 专注核心架构"""
    model = tf.keras.Sequential([
        # 输入层
        tf.keras.layers.Dense(256, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        # 特征交互层 (模拟早期模型的CrossLayer)
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        # 深度特征提取
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        # 预测层
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def create_simplified_early_model_2(input_dim):
    """创建简化版早期模型2 - 更深的网络"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(512, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.4),
        
        tf.keras.layers.Dense(256, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(32, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def create_simplified_early_model_3(input_dim):
    """创建简化版早期模型3 - 宽网络"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(384, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(384, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.3),
        
        tf.keras.layers.Dense(192, activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(96, activation='relu'),
        tf.keras.layers.Dropout(0.2),
        
        tf.keras.layers.Dense(1, activation='sigmoid')
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['auc']
    )
    return model

def train_and_evaluate():
    """训练和评估简化版早期模型"""
    # 1. 加载数据
    data_loader = NioDataLoader()
    df = data_loader.load_data()
    
    # 2. 简化版早期模型特征工程
    X, feature_names = simplified_early_feature_engineering(df)
    y = data_loader.prepare_labels(df)
    
    # 3. 特征标准化
    logger.info("特征标准化...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 4. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. 应用SMOTE重采样
    logger.info("应用SMOTE重采样...")
    smote = SMOTE(sampling_strategy=0.05, random_state=42, k_neighbors=5)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    logger.info(f"SMOTE重采样: {len(X_train)} -> {len(X_train_smote)}, 正样本比例: {y_train_smote.mean():.4f}")
    
    # 6. 训练参数
    callbacks = [
        tf.keras.callbacks.EarlyStopping(patience=7, restore_best_weights=True),
        tf.keras.callbacks.ReduceLROnPlateau(patience=3, factor=0.5, min_lr=1e-6)
    ]
    
    # 7. 训练简化版早期模型
    logger.info("=== 训练简化版早期模型集成 ===")
    
    models = []
    predictions = []
    model_creators = [create_simplified_early_model_1, create_simplified_early_model_2, create_simplified_early_model_3]
    
    for i, create_model in enumerate(model_creators, 1):
        logger.info(f"=== 训练简化早期模型{i} ===")
        model = create_model(X_train_smote.shape[1])
        
        # 打印第一个模型摘要
        if i == 1:
            model.summary()
        
        model.fit(
            X_train_smote, y_train_smote,
            validation_data=(X_test, y_test),
            epochs=12, batch_size=512,
            callbacks=callbacks,
            verbose=0
        )
        
        pred = model.predict(X_test, verbose=0)
        predictions.append(pred)
        models.append(model)
        
        # 详细评估单模型
        auc_score = roc_auc_score(y_test, pred)
        pr_auc = calculate_pr_auc(y_test, pred)
        precision_at_840, recall_at_840 = calculate_precision_recall_at_k(y_test, pred, k=840)
        
        logger.info(f"简化早期模型{i} - AUC: {auc_score:.4f}, PR-AUC: {pr_auc:.4f}")
        logger.info(f"简化早期模型{i} - P@840: {precision_at_840:.4f}, R@840: {recall_at_840:.4f}")
    
    # 8. 集成预测
    ensemble_pred = np.mean(predictions, axis=0)
    
    # 9. 详细评估
    ensemble_auc = roc_auc_score(y_test, ensemble_pred)
    ensemble_pr_auc = calculate_pr_auc(y_test, ensemble_pred)
    ensemble_precision_at_840, ensemble_recall_at_840 = calculate_precision_recall_at_k(y_test, ensemble_pred, k=840)
    
    logger.info(f"=== 简化版早期模型结果 ===")
    logger.info(f"集成AUC: {ensemble_auc:.4f}")
    logger.info(f"集成PR-AUC: {ensemble_pr_auc:.4f}")
    logger.info(f"集成P@840: {ensemble_precision_at_840:.4f}")
    logger.info(f"集成R@840: {ensemble_recall_at_840:.4f}")
    
    # 与基线和早期模型对比
    baseline_auc = 0.8219
    early_model_auc = 0.8756
    early_model_pr_auc = 0.4227
    early_model_recall = 0.9241
    
    auc_vs_baseline = ensemble_auc - baseline_auc
    auc_vs_early = ensemble_auc - early_model_auc
    pr_auc_vs_early = ensemble_pr_auc - early_model_pr_auc
    recall_vs_early = ensemble_recall_at_840 - early_model_recall
    
    logger.info(f"相比基线: AUC {auc_vs_baseline:+.4f}")
    logger.info(f"相比早期模型: AUC {auc_vs_early:+.4f}, PR-AUC {pr_auc_vs_early:+.4f}, Recall@840 {recall_vs_early:+.4f}")
    
    return {
        'optimization': 'simplified_early_patterns',
        'ensemble_auc': float(ensemble_auc),
        'ensemble_pr_auc': float(ensemble_pr_auc),
        'precision_at_840': float(ensemble_precision_at_840),
        'recall_at_840': float(ensemble_recall_at_840),
        'feature_count': len(feature_names),
        'baseline_auc': baseline_auc,
        'early_model_auc': early_model_auc,
        'early_model_pr_auc': early_model_pr_auc,
        'early_model_recall': early_model_recall,
        'auc_vs_baseline': float(auc_vs_baseline),
        'auc_vs_early': float(auc_vs_early),
        'pr_auc_vs_early': float(pr_auc_vs_early),
        'recall_vs_early': float(recall_vs_early),
        'individual_aucs': [float(roc_auc_score(y_test, pred)) for pred in predictions],
        'effective': bool(auc_vs_baseline > 0.01 or (auc_vs_early > -0.03 and ensemble_pr_auc > 0.05))
    }

if __name__ == "__main__":
    result = train_and_evaluate()
    
    # 创建实验目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = f"logs/{timestamp}_simplified_early"
    os.makedirs(exp_dir, exist_ok=True)
    
    # 保存结果
    with open(f'{exp_dir}/results.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n优化19完成: {'✅ 有效' if result['effective'] else '❌ 无效'}")
    print(f"集成AUC: {result['ensemble_auc']:.4f}")
    print(f"集成PR-AUC: {result['ensemble_pr_auc']:.4f}")
    print(f"P@840: {result['precision_at_840']:.4f}")
    print(f"R@840: {result['recall_at_840']:.4f}")
    print(f"特征数: {result['feature_count']}")
    print(f"相比基线AUC: {result['auc_vs_baseline']:+.4f}")
    print(f"相比早期模型AUC: {result['auc_vs_early']:+.4f}")
    print(f"相比早期模型PR-AUC: {result['pr_auc_vs_early']:+.4f}")
    print(f"相比早期模型Recall@840: {result['recall_vs_early']:+.4f}")
    print(f"结果保存到: {exp_dir}/results.json")
