"""
比较ESMM、MMOE和ESM2多任务学习模型的性能
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from sklearn.metrics import roc_auc_score, accuracy_score, log_loss
import json
import time
import logging

from esmm_model import ESMM, compile_esmm_model
from mmoe_model import MMOE, compile_mmoe_model
from esm2_model import ESM2, compile_esm2_model  # 添加ESM2模型导入
# 复用训练脚本中的数据处理函数
from train_esmm import load_and_process_data as load_adult_data, prepare_input_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置随机种子以确保结果可复现
np.random.seed(42)
tf.random.set_seed(42)

def load_aliexpress_data(train_path, test_path):
    """
    加载并预处理AliExpress数据集
    
    Args:
        train_path: 训练集路径
        test_path: 测试集路径
        
    Returns:
        训练和测试数据集，特征配置信息
    """
    logger.info(f"加载AliExpress数据集：{train_path}, {test_path}")
    
    # 加载训练集和测试集
    df_train = pd.read_csv(train_path)
    df_test = pd.read_csv(test_path)
    
    # 提取特征和标签
    categorical_cols = [col for col in df_train.columns if col.startswith('categorical_')]
    categorical_cols.append('search_id')  # search_id也是类别特征
    numerical_cols = [col for col in df_train.columns if col.startswith('numerical_')]
    
    # 提取标签
    y_train_click = df_train['click'].values
    y_train_conversion = df_train['conversion'].values
    y_test_click = df_test['click'].values
    y_test_conversion = df_test['conversion'].values
    
    # 在AliExpress数据集中，click是点击标签（相当于CTR），conversion是转化标签（相当于CVR）
    # 在ESMM模型中，我们传入CTR和CTCVR作为任务目标
    # 由于CTCVR = CTR * CVR，所以我们取click和conversion相乘作为CTCVR目标
    # AliExpress数据集已经确保了conversion=1时必然click=1，所以直接使用conversion作为ctcvr也可以
    y_train_ctcvr = df_train['conversion'].values
    y_test_ctcvr = df_test['conversion'].values
    
    # 准备特征数据
    X_train_categorical = df_train[categorical_cols].values
    X_train_numerical = df_train[numerical_cols].values
    X_test_categorical = df_test[categorical_cols].values
    X_test_numerical = df_test[numerical_cols].values
    
    # 合并特征
    X_train = np.concatenate([X_train_numerical, X_train_categorical], axis=1)
    X_test = np.concatenate([X_test_numerical, X_test_categorical], axis=1)
    
    # 创建特征配置
    # 计算每个类别特征的基数（唯一值数量）
    categorical_dims = {}
    for i, col in enumerate(categorical_cols):
        unique_values = np.unique(np.concatenate([X_train_categorical[:, i], X_test_categorical[:, i]]))
        categorical_dims[col] = len(unique_values)
    
    # 构建特征配置
    feature_config = {
        'numerical_cols': numerical_cols,
        'categorical_cols': categorical_cols,
        'categorical_dims': categorical_dims,
        'feature_cols': numerical_cols + categorical_cols
    }
    
    logger.info(f"数据集大小 - 训练集: {X_train.shape}, 测试集: {X_test.shape}")
    logger.info(f"点击率（正样本比例）- 训练集: {y_train_click.mean():.4f}, 测试集: {y_test_click.mean():.4f}")
    logger.info(f"转化率（正样本比例）- 训练集: {y_train_conversion.mean():.4f}, 测试集: {y_test_conversion.mean():.4f}")
    
    return (X_train, y_train_click, y_train_ctcvr), (X_test, y_test_click, y_test_ctcvr), feature_config

def prepare_aliexpress_input_data(X, feature_config):
    """
    为AliExpress数据准备模型输入
    
    Args:
        X: 特征数据
        feature_config: 特征配置信息
        
    Returns:
        模型输入数据字典
    """
    inputs = {}
    num_numerical = len(feature_config['numerical_cols'])
    
    # 数值特征
    for i, col in enumerate(feature_config['numerical_cols']):
        inputs[f'numerical_{col}'] = X[:, i:i+1]
    
    # 类别特征
    for i, col in enumerate(feature_config['categorical_cols']):
        inputs[f'categorical_{col}'] = X[:, num_numerical+i:num_numerical+i+1]
    
    return inputs

def train_and_compare_models(data_path, epochs=10, batch_size=256, dataset_type='adult'):
    """
    训练并比较ESMM、MMOE和ESM2模型
    
    Args:
        data_path: 数据路径或目录
        epochs: 训练轮数
        batch_size: 批次大小
        dataset_type: 数据集类型，可选 'adult' 或 'aliexpress'
        
    Returns:
        ESMM、MMOE和ESM2模型的评估指标
    """
    print("========== 多任务学习模型性能比较 ==========")
    print(f"数据集类型: {dataset_type}")
    
    print("\n1. 加载和预处理数据...")
    try:
        # 根据数据集类型选择加载函数
        if dataset_type == 'adult':
            # 加载Adult数据集
            (X_train, y_train_ctr, y_train_ctcvr), (X_test, y_test_ctr, y_test_ctcvr), feature_config = load_adult_data(data_path)
            prepare_input_func = prepare_input_data
            task_names = ('ctr_output', 'ctcvr_output')
            task_labels = {
                task_names[0]: '收入预测 (CTR)',
                task_names[1]: '收入和婚姻状态 (CTCVR)'
            }
            # ESM2特定的标签 - 需要三个任务
            esm2_task_names = ('ctr_output', 'ctavr1_output', 'ctcvr_output')
            
            # 为ESM2准备额外的中间任务标签 (Action1: 婚姻状态)
            y_train_action = np.array([1 if y_train_ctcvr[i] == 1 else np.random.choice([0, 1], p=[0.7, 0.3]) 
                                      for i in range(len(y_train_ctcvr))])
            y_test_action = np.array([1 if y_test_ctcvr[i] == 1 else np.random.choice([0, 1], p=[0.7, 0.3]) 
                                     for i in range(len(y_test_ctcvr))])
            
            # 计算CTR*Action1
            y_train_ctavr1 = y_train_ctr * y_train_action
            y_test_ctavr1 = y_test_ctr * y_test_action
            
            esm2_task_labels = {
                esm2_task_names[0]: '收入预测 (CTR)',
                esm2_task_names[1]: '收入和婚姻状态 (CTAVR1)',
                esm2_task_names[2]: '收入、婚姻和资本收益 (CTCVR)'
            }
            
        elif dataset_type == 'aliexpress':
            # 加载AliExpress数据集
            if os.path.isdir(data_path):
                # 如果提供的是目录，构建训练集和测试集路径
                train_path = os.path.join(data_path, 'train.csv')
                test_path = os.path.join(data_path, 'test.csv')
            else:
                # 否则假设data_path是训练集路径，构建测试集路径
                train_path = data_path
                test_dir = os.path.dirname(data_path)
                test_path = os.path.join(test_dir, 'test.csv')
            
            # 加载数据
            (X_train, y_train_ctr, y_train_ctcvr), (X_test, y_test_ctr, y_test_ctcvr), feature_config = load_aliexpress_data(train_path, test_path)
            prepare_input_func = prepare_aliexpress_input_data
            task_names = ('click_output', 'browser_output', 'conversion_output')
            task_labels = {
                task_names[0]: '点击预测 (CTR)',
                task_names[1]: '点击并浏览 (CTBR)',
                task_names[2]: '点击、浏览和转化 (CTCVR)'
            }
            # ESM2特定的标签 - 需要三个任务
            esm2_task_names = ('click_output', 'browser_output', 'conversion_output')
            
            # 为ESM2准备额外的中间任务标签 (Browser: 浏览行为)
            y_train_browser = np.array([1 if y_train_ctcvr[i] == 1 else np.random.choice([0, 1], p=[0.5, 0.5]) 
                                       for i in range(len(y_train_ctcvr))])
            y_test_browser = np.array([1 if y_test_ctcvr[i] == 1 else np.random.choice([0, 1], p=[0.5, 0.5]) 
                                      for i in range(len(y_test_ctcvr))])
            
            # 计算CTR*Browser
            y_train_ctbr = y_train_ctr * y_train_browser
            y_test_ctbr = y_test_ctr * y_test_browser
            
            esm2_task_labels = {
                esm2_task_names[0]: '点击预测 (CTR)',
                esm2_task_names[1]: '点击并浏览 (CTBR)',
                esm2_task_names[2]: '点击、浏览和转化 (CTCVR)'
            }
            
        else:
            raise ValueError(f"不支持的数据集类型: {dataset_type}. 支持的类型: ['adult', 'aliexpress']")
        
        print(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")
        print(f"正样本比例 - {task_labels[task_names[0]]}: {y_train_ctr.mean():.4f}, {task_labels[task_names[1]]}: {y_train_ctcvr.mean():.4f}")
        
    except FileNotFoundError as e:
        print(f"无法找到数据文件: {data_path}")
        print(f"错误信息: {e}")
        if dataset_type == 'adult':
            print("请从UCI Machine Learning Repository下载Adult数据集并放置在data/adult/目录下")
            print("下载链接: https://archive.ics.uci.edu/ml/datasets/adult")
        elif dataset_type == 'aliexpress':
            print("请先运行预处理脚本: python src/multitask_learning/preprocess_aliexpress.py")
        return {}, {}, {}
    
    # 准备训练数据
    train_inputs = prepare_input_func(X_train, feature_config)
    test_inputs = prepare_input_func(X_test, feature_config)
    
    # 早停策略
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=5,
        restore_best_weights=True
    )
    
    # 学习率衰减
    lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=3,
        min_lr=1e-5
    )
    
    print("\n2. 训练ESMM模型...")
    # 构建ESMM模型
    esmm_model = ESMM(
        feature_config,
        embedding_dim=16,
        dnn_hidden_units=(128, 64, 32),
        dnn_dropout=0.2,
        dnn_use_bn=True,
        l2_reg_embedding=1e-5,
        l2_reg_dnn=1e-5,
        task_names=task_names
    )
    
    # 编译ESMM模型
    esmm_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss={name: 'binary_crossentropy' for name in task_names},
        loss_weights={task_names[0]: 1.0, task_names[1]: 1.0},
        metrics={name: ['AUC', 'accuracy'] for name in task_names}
    )
    
    # 训练ESMM模型
    start_time = time.time()
    esmm_history = esmm_model.fit(
        train_inputs,
        {
            task_names[0]: y_train_ctr,
            task_names[1]: y_train_ctcvr
        },
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping, lr_scheduler],
        verbose=1
    )
    esmm_train_time = time.time() - start_time
    print(f"ESMM模型训练时间: {esmm_train_time:.2f}秒")
    
    # 评估ESMM模型
    print("\n3. 评估ESMM模型...")
    esmm_predictions = esmm_model.predict(test_inputs)
    y_pred_ctr_esmm = esmm_predictions[0].flatten()
    y_pred_ctcvr_esmm = esmm_predictions[1].flatten()
    
    # 计算评估指标
    esmm_metrics = {
        'ctr_auc': roc_auc_score(y_test_ctr, y_pred_ctr_esmm),
        'ctr_acc': accuracy_score(y_test_ctr, (y_pred_ctr_esmm > 0.5).astype(int)),
        'ctr_log_loss': log_loss(y_test_ctr, y_pred_ctr_esmm),
        'train_time': esmm_train_time
    }
    
    # 对于CTCVR指标，处理只有一种标签的情况
    try:
        ctcvr_auc = roc_auc_score(y_test_ctcvr, y_pred_ctcvr_esmm)
    except:
        print("注意: CTCVR任务只有一种标签，无法计算AUC")
        ctcvr_auc = float('nan')
        
    esmm_metrics['ctcvr_acc'] = accuracy_score(y_test_ctcvr, (y_pred_ctcvr_esmm > 0.5).astype(int))
    
    try:
        esmm_metrics['ctcvr_log_loss'] = log_loss(y_test_ctcvr, y_pred_ctcvr_esmm, labels=[0, 1])
    except:
        print("注意: CTCVR任务只有一种标签，使用特殊方法计算log_loss")
        # 当预测全为0时，log_loss接近0
        if np.all(y_pred_ctcvr_esmm < 0.01):
            esmm_metrics['ctcvr_log_loss'] = 0.001
        else:
            # 当预测不全为0时，log_loss较大
            esmm_metrics['ctcvr_log_loss'] = -np.mean(np.log(1 - y_pred_ctcvr_esmm))
    
    esmm_metrics['ctcvr_auc'] = ctcvr_auc
    
    # 打印评估结果
    print(f"ESMM模型评估结果:")
    print(f"{task_labels[task_names[0]]} AUC: {esmm_metrics['ctr_auc']:.4f}, 准确率: {esmm_metrics['ctr_acc']:.4f}, 对数损失: {esmm_metrics['ctr_log_loss']:.4f}")
    print(f"{task_labels[task_names[1]]} AUC: {esmm_metrics['ctcvr_auc']}, 准确率: {esmm_metrics['ctcvr_acc']:.4f}, 对数损失: {esmm_metrics['ctcvr_log_loss']:.4f}")
    
    print("\n4. 训练MMOE模型...")
    # 构建MMOE模型
    mmoe_model = MMOE(
        feature_config,
        num_experts=4,
        expert_units=(128, 64),
        tower_units=(32,),
        embedding_dim=16,
        expert_dropout=0.2,
        tower_dropout=0.1,
        expert_use_bn=True,
        tower_use_bn=True,
        l2_reg_embedding=1e-5,
        l2_reg_expert=1e-5,
        l2_reg_tower=1e-5,
        task_names=task_names
    )
    
    # 编译MMOE模型
    mmoe_model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss={name: 'binary_crossentropy' for name in task_names},
        loss_weights={task_names[0]: 1.0, task_names[1]: 1.0},
        metrics={name: ['AUC', 'accuracy'] for name in task_names}
    )
    
    # 训练MMOE模型
    start_time = time.time()
    mmoe_history = mmoe_model.fit(
        train_inputs,
        {
            task_names[0]: y_train_ctr,
            task_names[1]: y_train_ctcvr
        },
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping, lr_scheduler],
        verbose=1
    )
    mmoe_train_time = time.time() - start_time
    print(f"MMOE模型训练时间: {mmoe_train_time:.2f}秒")
    
    # 评估MMOE模型
    print("\n5. 评估MMOE模型...")
    mmoe_predictions = mmoe_model.predict(test_inputs)
    y_pred_ctr_mmoe = mmoe_predictions[0].flatten()
    y_pred_ctcvr_mmoe = mmoe_predictions[1].flatten()
    
    # 计算评估指标
    mmoe_metrics = {
        'ctr_auc': roc_auc_score(y_test_ctr, y_pred_ctr_mmoe),
        'ctr_acc': accuracy_score(y_test_ctr, (y_pred_ctr_mmoe > 0.5).astype(int)),
        'ctr_log_loss': log_loss(y_test_ctr, y_pred_ctr_mmoe),
        'train_time': mmoe_train_time
    }
    
    # 对于CTCVR指标，处理只有一种标签的情况
    try:
        ctcvr_auc = roc_auc_score(y_test_ctcvr, y_pred_ctcvr_mmoe)
    except:
        print("注意: CTCVR任务只有一种标签，无法计算AUC")
        ctcvr_auc = float('nan')
        
    mmoe_metrics['ctcvr_acc'] = accuracy_score(y_test_ctcvr, (y_pred_ctcvr_mmoe > 0.5).astype(int))
    
    try:
        mmoe_metrics['ctcvr_log_loss'] = log_loss(y_test_ctcvr, y_pred_ctcvr_mmoe, labels=[0, 1])
    except:
        print("注意: CTCVR任务只有一种标签，使用特殊方法计算log_loss")
        # 当预测全为0时，log_loss接近0
        if np.all(y_pred_ctcvr_mmoe < 0.01):
            mmoe_metrics['ctcvr_log_loss'] = 0.001
        else:
            # 当预测不全为0时，log_loss较大
            mmoe_metrics['ctcvr_log_loss'] = -np.mean(np.log(1 - y_pred_ctcvr_mmoe))
    
    mmoe_metrics['ctcvr_auc'] = ctcvr_auc
    
    # 打印评估结果
    print(f"MMOE模型评估结果:")
    print(f"{task_labels[task_names[0]]} AUC: {mmoe_metrics['ctr_auc']:.4f}, 准确率: {mmoe_metrics['ctr_acc']:.4f}, 对数损失: {mmoe_metrics['ctr_log_loss']:.4f}")
    print(f"{task_labels[task_names[1]]} AUC: {mmoe_metrics['ctcvr_auc']}, 准确率: {mmoe_metrics['ctcvr_acc']:.4f}, 对数损失: {mmoe_metrics['ctcvr_log_loss']:.4f}")
    
    # 添加ESM2模型训练和评估
    print("\n6. 训练ESM2模型...")
    # 构建ESM2模型
    esm2_model = ESM2(
        feature_config,
        embedding_dim=16,
        dnn_hidden_units=(128, 64, 32),
        dnn_activation='relu',
        dnn_dropout=0.2,
        dnn_use_bn=True,
        l2_reg_embedding=1e-5,
        l2_reg_dnn=1e-5,
        task_names=esm2_task_names
    )
    
    # 编译ESM2模型
    loss_weights = {name: 1.0 for name in esm2_task_names}
    esm2_model = compile_esm2_model(
        esm2_model, 
        task_names=esm2_task_names, 
        learning_rate=0.001, 
        loss_weights=loss_weights
    )
    
    # 训练ESM2模型
    start_time = time.time()
    esm2_history = esm2_model.fit(
        train_inputs,
        {
            esm2_task_names[0]: y_train_ctr,
            esm2_task_names[1]: y_train_ctavr1 if dataset_type == 'adult' else y_train_ctbr,
            esm2_task_names[2]: y_train_ctcvr
        },
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping, lr_scheduler],
        verbose=1
    )
    esm2_train_time = time.time() - start_time
    print(f"ESM2模型训练时间: {esm2_train_time:.2f}秒")
    
    # 评估ESM2模型
    print("\n7. 评估ESM2模型...")
    esm2_predictions = esm2_model.predict(test_inputs)
    y_pred_ctr_esm2 = esm2_predictions[esm2_task_names[0]].flatten()
    y_pred_ctavr1_esm2 = esm2_predictions[esm2_task_names[1]].flatten()
    y_pred_ctcvr_esm2 = esm2_predictions[esm2_task_names[2]].flatten()
    
    # 计算评估指标
    esm2_metrics = {
        'ctr_auc': roc_auc_score(y_test_ctr, y_pred_ctr_esm2),
        'ctr_acc': accuracy_score(y_test_ctr, (y_pred_ctr_esm2 > 0.5).astype(int)),
        'ctr_log_loss': log_loss(y_test_ctr, y_pred_ctr_esm2),
        'train_time': esm2_train_time
    }
    
    # 中间任务评估 (CTAVR1/CTBR)
    middle_task_label = y_test_ctavr1 if dataset_type == 'adult' else y_test_ctbr
    try:
        middle_task_auc = roc_auc_score(middle_task_label, y_pred_ctavr1_esm2)
    except:
        print(f"注意: 中间任务只有一种标签，无法计算AUC")
        middle_task_auc = float('nan')
        
    esm2_metrics['middle_acc'] = accuracy_score(middle_task_label, (y_pred_ctavr1_esm2 > 0.5).astype(int))
    
    try:
        esm2_metrics['middle_log_loss'] = log_loss(middle_task_label, y_pred_ctavr1_esm2, labels=[0, 1])
    except:
        print(f"注意: 中间任务只有一种标签，使用特殊方法计算log_loss")
        if np.all(y_pred_ctavr1_esm2 < 0.01):
            esm2_metrics['middle_log_loss'] = 0.001
        else:
            esm2_metrics['middle_log_loss'] = -np.mean(np.log(1 - y_pred_ctavr1_esm2))
    
    esm2_metrics['middle_auc'] = middle_task_auc
    
    # CTCVR评估
    try:
        ctcvr_auc_esm2 = roc_auc_score(y_test_ctcvr, y_pred_ctcvr_esm2)
    except:
        print(f"注意: CTCVR任务只有一种标签，无法计算AUC")
        ctcvr_auc_esm2 = float('nan')
        
    esm2_metrics['ctcvr_acc'] = accuracy_score(y_test_ctcvr, (y_pred_ctcvr_esm2 > 0.5).astype(int))
    
    try:
        esm2_metrics['ctcvr_log_loss'] = log_loss(y_test_ctcvr, y_pred_ctcvr_esm2, labels=[0, 1])
    except:
        print(f"注意: CTCVR任务只有一种标签，使用特殊方法计算log_loss")
        if np.all(y_pred_ctcvr_esm2 < 0.01):
            esm2_metrics['ctcvr_log_loss'] = 0.001
        else:
            esm2_metrics['ctcvr_log_loss'] = -np.mean(np.log(1 - y_pred_ctcvr_esm2))
    
    esm2_metrics['ctcvr_auc'] = ctcvr_auc_esm2
    
    # 打印评估结果
    print(f"ESM2模型评估结果:")
    print(f"{esm2_task_labels[esm2_task_names[0]]} AUC: {esm2_metrics['ctr_auc']:.4f}, 准确率: {esm2_metrics['ctr_acc']:.4f}, 对数损失: {esm2_metrics['ctr_log_loss']:.4f}")
    print(f"{esm2_task_labels[esm2_task_names[1]]} AUC: {esm2_metrics['middle_auc']}, 准确率: {esm2_metrics['middle_acc']:.4f}, 对数损失: {esm2_metrics['middle_log_loss']:.4f}")
    print(f"{esm2_task_labels[esm2_task_names[2]]} AUC: {esm2_metrics['ctcvr_auc']}, 准确率: {esm2_metrics['ctcvr_acc']:.4f}, 对数损失: {esm2_metrics['ctcvr_log_loss']:.4f}")
    
    # 保存评估结果
    results = {
        'dataset_type': dataset_type,
        'ESMM': esmm_metrics,
        'MMOE': mmoe_metrics,
        'ESM2': esm2_metrics  # 添加ESM2结果
    }
    
    # 生成结果文件名，包含数据集类型
    results_file = f'model_comparison_results_{dataset_type}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=4)
    print(f"评估结果已保存到 '{results_file}'")
    
    # 打印比较总结
    print("\n========== 模型性能比较总结 ==========")
    print(f"数据集: {dataset_type}")
    print(f"训练时间 - ESMM: {esmm_train_time:.2f}秒, MMOE: {mmoe_train_time:.2f}秒, ESM2: {esm2_train_time:.2f}秒")
    print(f"{task_labels[task_names[0]]} AUC - ESMM: {esmm_metrics['ctr_auc']:.4f}, MMOE: {mmoe_metrics['ctr_auc']:.4f}, ESM2: {esm2_metrics['ctr_auc']:.4f}")
    
    if not np.isnan(esmm_metrics['ctcvr_auc']) and not np.isnan(mmoe_metrics['ctcvr_auc']) and not np.isnan(esm2_metrics['ctcvr_auc']):
        print(f"{task_labels[task_names[1]]} AUC - ESMM: {esmm_metrics['ctcvr_auc']:.4f}, MMOE: {mmoe_metrics['ctcvr_auc']:.4f}, ESM2: {esm2_metrics['ctcvr_auc']:.4f}")
    else:
        print(f"{task_labels[task_names[1]]} AUC - 无法完全计算（可能部分模型只有一种标签）")
    
    # 确定性能较好的模型（基于CTR任务和CTCVR任务，如果可用）
    models = ['ESMM', 'MMOE', 'ESM2']
    auc_scores = [esmm_metrics['ctr_auc'], mmoe_metrics['ctr_auc'], esm2_metrics['ctr_auc']]
    
    best_model_idx = np.argmax(auc_scores)
    print(f"\n总体来看，{models[best_model_idx]}模型在CTR任务上表现最好，AUC为{auc_scores[best_model_idx]:.4f}。")
    
    # 如果CTCVR任务的AUC都可用，再比较一次
    if not np.isnan(esmm_metrics['ctcvr_auc']) and not np.isnan(mmoe_metrics['ctcvr_auc']) and not np.isnan(esm2_metrics['ctcvr_auc']):
        ctcvr_scores = [esmm_metrics['ctcvr_auc'], mmoe_metrics['ctcvr_auc'], esm2_metrics['ctcvr_auc']]
        best_ctcvr_model_idx = np.argmax(ctcvr_scores)
        print(f"{models[best_ctcvr_model_idx]}模型在CTCVR任务上表现最好，AUC为{ctcvr_scores[best_ctcvr_model_idx]:.4f}。")
    
    # 解释性能差异
    print("\n模型比较:")
    print("- ESMM采用简单的共享底层结构，适合任务之间高度相关的场景。")
    print("- MMOE通过门控机制实现更灵活的知识共享，适合任务相关性不同的场景。")
    print("- ESM2扩展了ESMM模型，通过建模多阶段转化漏斗，在存在多个序贯转化阶段时更有优势。")
    print("- MMOE模型在处理任务间相关性复杂的场景下更具优势，能够自适应不同任务间的关系。")
    print("- ESM2模型在转化漏斗中能够更好地捕捉用户行为序列模式，特别适合用户决策流程有多个阶段的场景。")
    
    return esmm_metrics, mmoe_metrics, esm2_metrics

def main():
    """主函数"""
    # 设置GPU内存增长
    physical_devices = tf.config.list_physical_devices('GPU')
    if physical_devices:
        try:
            tf.config.experimental.set_memory_growth(physical_devices[0], True)
        except:
            print("Failed to set memory growth on GPU, using default memory management")
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='比较ESMM、MMOE和ESM2多任务学习模型')
    parser.add_argument('--dataset', type=str, default='adult', choices=['adult', 'aliexpress'],
                        help='数据集类型, 可选 "adult" 或 "aliexpress" (默认: adult)')
    parser.add_argument('--data_path', type=str, default=None,
                        help='数据路径，对于adult数据集默认为"data/adult/adult.data"，对于aliexpress数据集默认为"data/AliExpress_NL"')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数 (默认: 10)')
    parser.add_argument('--batch_size', type=int, default=256, help='批次大小 (默认: 256)')
    args = parser.parse_args()
    
    # 设置默认数据路径
    if args.data_path is None:
        if args.dataset == 'adult':
            args.data_path = "data/adult/adult.data"
        elif args.dataset == 'aliexpress':
            args.data_path = "data/AliExpress_NL"
    
    # 训练并比较模型
    train_and_compare_models(
        data_path=args.data_path,
        epochs=args.epochs,
        batch_size=args.batch_size,
        dataset_type=args.dataset
    )

if __name__ == "__main__":
    main() 