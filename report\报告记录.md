# NIO新车购买倾向预测模型数据分析报告

## 1. 数据集概览

本次分析基于NIO新车购买倾向预测模型的训练数据，包括以下数据集：

- 训练集日期分区: ['20240430']
- 测试集日期分区: ['20240531']
- 评估集: 20240531日期分区的1%随机采样数据

| 数据源 | 分区/文件 | 样本数量 | 占比 | 正样本率 |
|--------|-----------|----------|------|----------|
| 训练集 | datetime=20240430 | 56,464 | 20.25% | 4.20% |
| 测试集 | datetime=20240531 | 9,800 | 3.51% | 3.91% |
| 评估集 | 20240531_随机采样1%.parquet | 212,611 | 76.24% | 0.24% |
| 总计 | | 278,875 | 100% | 1.17% |


数据特征总量: 355个特征
标签列: ["purchase_days_nio_new_car_total"]

| 特征类型 | 数据处理方式 | 数量 | 比例 |
|----------|--------------|------|------|
| 表格特征(table) | StringLookup (类别特征) | 22 | 6.2% |
| 表格特征(table) | Bucket (数值分桶特征) | 328 | 92.4% |
| 变长序列(VarLen) | 序列特征 | 5 | 1.4% |
| 总计 | | 355 | 100% |

**数据规模**：总共分析了278,875条数据，其中训练集56,464条(20.25%)，测试集9,800条(3.51%)，评估集212,611条(76.24%)

## 2. 数据内容介绍

### 2.1 数据来源

数据集来源于NIO用户的行为数据和购买记录，主要包括：

1. 用户基础信息
2. 应用内交互行为
3. 销售沟通记录
4. 购车相关行为
5. 历史购买记录

### 2.2 数据划分

数据按照时间进行划分：
- 训练集: 2024年4月30日分区数据
- 测试集: 2024年5月31日分区数据
- 评估集: 2024年5月31日1%随机采样数据

### 2.3 样本分布

训练集和测试集采用了固定负采样因子采样策略，评估集则是随机抽样，保证了评估结果的可靠性。

- **总体正样本率**：仅1.17%的用户在未来6个月内购买新车，存在严重类别不平衡
- **各数据集正样本率**：
  - 训练集：4.20%
  - 测试集：3.91%
  - 评估集：0.24%（随机采样集正样本率明显低于固定因子采样的训练/测试集）

## 3. 特征体系

### 3.1 特征分类

模型的特征主要分为以下几类：

1. **序列特征(VarLen)**：记录用户行为的时序信息，共5个
   - 行为发生天序列
   - 行为类型序列
   - 车型偏好序列
   - 交互内容序列

2. **类别特征(StringLookup)**：离散型特征，共22个
   - 用户属性特征
   - 行为类型特征
   - 车型偏好特征
   - 渠道来源特征

3. **数值特征(Bucket)**：连续型特征，共328个
   - 行为次数统计
   - 行为间隔统计
   - 时间窗口特征
   - 交互强度特征

### 3.2 特征分布特点

- **序列特征**：
  - 用户行为序列特征长度分布不均，大多数用户序列较短
  - 行为序列和车型序列是重要的意向表现特征
  
- **类别特征**：
  - 用户身份、意向阶段和决策角色等特征分布不均衡
  - 城市分布呈现长尾特征，少数城市占据大部分样本
  
- **数值特征**：
  - 行为次数类特征大多呈偏态分布，大量用户行为次数为0或极少
  - 时间相关特征(如距上次行为天数)分布差异较大

### 3.3 特征工程

特征处理的主要步骤包括：

1. **序列特征处理**
   - 最大长度截断: 不同序列特征采用不同的最大长度限制
   - 序列填充: 对短序列使用特定的填充值（如0或空字符串）补齐
   - 序列编码: 对类别型序列进行编码处理

2. **类别特征处理**
   - 低频过滤: 将出现频率低于万分之一的类别聚合为"LowFreq"
   - 词表生成: 自动生成类别词表，确保测试数据和训练数据使用一致的编码
   - 类别编码: 将类别转换为整数编码

3. **数值特征处理**
   - 异常值处理: 对异常值进行截断或替换
   - 特征分桶: 采用分桶策略将连续特征离散化
   - 缺失值填充: 对缺失的数值特征进行默认值填充

## 4. 标签设计

### 4.1 标签定义

模型预测的是用户未来6个月内购买新车的可能性，标签设计如下：

1. **总购买标签**: `purchase_days_nio_new_car_total`
   - 表示用户在未来6个月内是否购买新车

2. **月度购买标签**: `m_purchase_days_nio_new_car`
   - 表示用户在未来每个月是否购买新车
   - 格式为长度为6的数组，每个元素代表对应月份是否购买

3. **标签掩码**: `mask_label`
   - 用于处理标签在某些月份数据不完整的情况
   - 在计算损失函数时使用，只考虑有效的标签数据

### 4.2 标签分布

标签总体呈现以下特点：

1. 正样本较少，存在严重的类别不平衡
2. 短期预测(1-2个月)的正样本比例相对较高
3. 长期预测(5-6个月)的正样本比例明显降低
4. 购买行为存在连续性，一旦购买后短期内不太可能再次购买

### 4.3 月度正样本率

- 第1个月：1.29%
- 第2个月：0.83%
- 第3个月：0.65%
- 第4个月：0.67%
- 第5个月：0.56%
- 第6个月：0.38%
- 远期月份的正样本率显著低于近期月份

### 4.4 购买模式分析

**最常见购买模式**：
- 无购买(000000)：95.71%
- 仅第1个月购买(100000)：1.27%
- 仅第2个月购买(010000)：0.80%
- 仅第4个月购买(000100)：0.64%
- 仅第3个月购买(001000)：0.62%

**购买特点**：
- 用户购买行为多为单月购买，连续多月购买的情况极少
- 早期月份(1-2月)购买意愿明显高于远期月份(5-6月)
- 99%以上的购买模式是单月购买，表明购买行为的离散性

## 5. 模型结构

模型采用EPMMOENet网络结构，主要特点：

1. **基于MOE(Mixture of Experts)架构**：对不同类型的特征使用不同的专家网络进行处理
2. **序列特征处理**：使用注意力机制和CNN结构捕捉序列中的时序模式
3. **多目标预测**：同时预测未来6个月的购买可能性，采用累积概率约束
4. **掩码机制**：使用mask_label处理标签不完整问题

### 5.1 模型结构图

```mermaid
graph TD
    subgraph 输入层
        A1[表格特征] --> |350个特征| B1
        A2[序列特征] --> |5个特征| B2
    end
    
    subgraph 特征处理层
        B1[特征Embedding] --> C1
        B2[序列Embedding] --> C2
        C2 --> |注意力机制| C3[序列表示]
    end
    
    subgraph 混合专家模型
        C1 --> D1[表格特征专家]
        C3 --> D2[序列特征专家]
        D1 --> E1
        D2 --> E1
        E1[特征融合] --> E2[混合专家门控]
        E2 --> |专家1| F1[专家网络1]
        E2 --> |专家2| F2[专家网络2]
        E2 --> |专家3| F3[专家网络3]
        E2 --> |专家n| F4[专家网络n]
        F1 --> G[加权融合]
        F2 --> G
        F3 --> G
        F4 --> G
    end
    
    subgraph 输出层
        G --> H1[月度购买概率]
        H1 --> |单调约束| H2[累积概率模型]
        H2 --> |掩码处理| I1[月度预测1]
        H2 --> |掩码处理| I2[月度预测2]
        H2 --> |掩码处理| I3[月度预测3]
        H2 --> |掩码处理| I4[月度预测4]
        H2 --> |掩码处理| I5[月度预测5]
        H2 --> |掩码处理| I6[月度预测6]
    end
```

## 6. 训练与评估

### 6.1 训练策略

1. **损失函数**：采用掩码累积损失函数(cumsum_mask_loss)
2. **优化器**：Adam优化器，学习率0.001
3. **早停策略**：验证集损失10轮无改善停止训练
4. **批量大小**：8192

### 6.2 评估指标

评估采用多种指标进行综合衡量：

1. **ROC-AUC**：衡量模型区分正负样本的能力
2. **PR-AUC**：在正样本稀少情况下更有参考价值
3. **Precision@K**：在K个高分样本中的精确率
4. **Recall@K**：在K个高分样本中的召回率

## 7. 实验结果分析

从现有实验结果来看：

1. **总体表现**：
   - 模型在测试集和评估集上的ROC-AUC分别达到0.80和0.80，表现良好
   - PR-AUC较低，说明在正样本稀少的情况下，精确率和召回率的平衡仍有改进空间

2. **月度表现**：
   - 第1个月预测效果最好，ROC-AUC约0.91
   - 随着预测月份的增加，模型表现逐渐下降
   - 第6个月预测效果最差，ROC-AUC约0.67-0.71

3. **精确率和召回率**：
   - 前几个月的Precision@K和Recall@K较高
   - 远期月份的预测精度明显下降

## 8. 优化建议

### 8.1 数据层面优化

1. **样本平衡处理**：
   - 采用更精细的负采样策略，减轻类别不平衡问题
   - 考虑针对远期月份使用更低的负采样因子
   - 使用样本权重调整不同月份预测的重要性

2. **特征工程**：
   - 强化序列特征的表达能力，如增加序列特征的交互项
   - 增加更多的用户意向强度特征
   - 增加用户对特定车型的偏好特征
   - 加入用户财务能力相关特征
   - 增强用户近期行为的表达能力
   - 针对购买决策链路的各环节构建更精细的特征

3. **标签优化**：
   - 考虑将标签从二分类扩展为多分类(购买概率区间)
   - 引入辅助标签，如关注特定车型、询价等弱信号

### 8.2 模型层面优化

1. **网络结构**：
   - 优化序列特征的注意力机制
   - 为远期预测增加专门的网络分支
   - 考虑引入时间衰减因子
   - 改进序列特征的处理方式，如采用更高级的注意力机制
   - 考虑多任务学习框架，同时预测不同时间窗口的购买可能性

2. **损失函数**：
   - 设计针对类别不平衡的损失函数
   - 为不同月份设置不同权重
   - 考虑多任务学习框架
   - 强化远期月份预测的损失贡献

3. **训练策略**：
   - 采用学习率衰减策略
   - 尝试不同的优化器
   - 增加模型集成方法

### 8.3 评估体系优化

**多维度评估**：
- 关注不同月份预测的精确率和召回率平衡
- 重点评估高意向用户的识别准确性
- 增加业务指标评估，如每个月Top-K用户的实际转化率

## 9. 应用场景

模型可应用于以下场景：

1. **精准营销**：根据预测的购买可能性进行精准用户触达
2. **销售资源分配**：优先将销售资源分配给高意向用户
3. **个性化推荐**：基于用户偏好和购买可能性推荐合适的车型
4. **活动策划**：针对不同购买可能性的用户群体设计差异化活动

## 10. 总结

NIO新车购买倾向预测模型通过分析用户的历史行为和属性特征，预测用户未来6个月内购买新车的可能性。模型整体表现良好，特别是在短期预测方面。

数据具有明显的类别不平衡特征，并且远期预测比近期预测更具挑战性。通过优化数据处理、特征工程和模型结构，可以提高模型，特别是远期月份的预测性能。

重点应放在提升正样本识别能力和改善各月份预测的平衡性上：

1. 提高远期月份的预测准确性
2. 优化类别特征的表示方法
3. 增强序列特征的表达能力
4. 平衡样本分布，提高模型泛化能力

## 11. 下一步发展方向

### 11.1 模型架构改进

1. **分层预测架构**：
   - 第一层：预测用户是否在6个月内购买新车
   - 第二层：针对有购买可能的用户，预测具体购买月份

2. **自适应特征选择**：
   - 为不同月份预测动态选择最相关的特征子集
   - 使用特征重要性反馈机制动态调整特征权重

3. **时序依赖建模**：
   - 引入GRU/LSTM等网络捕捉月度预测之间的依赖关系
   - 构建专门的时序衰减模块处理远期预测

### 11.2 先进技术引入

1. **对比学习策略**：
   - 引入对比学习帮助模型更好区分高意向与低意向用户
   - 构建正样本对，增强稀疏信号的学习效果

2. **分布校准技术**：
   - 引入分布校准层，解决训练集与真实场景分布不一致问题
   - 针对不同月份设计不同的校准策略

3. **模型蒸馏**：
   - 训练月份特定专家模型后，蒸馏到统一模型
   - 利用知识蒸馏减少模型复杂度，保持预测性能

通过这些改进，期望模型能在保持短期预测优势的同时，显著提升远期月份的预测精度，为NIO的销售和营销决策提供更可靠的支持。

## 12. 项目文件结构与功能说明

为了便于后续开发迭代，本节对demo目录下的各个文件及其功能进行说明。

### 12.1 核心代码文件

1. **demo.py** (18KB)
   - 主程序入口文件
   - 实现模型训练、评估和预测的完整流程
   - 包含数据加载、特征处理、模型训练和评估指标计算

2. **model_utils.py** (25KB)
   - 工具函数库
   - `preprocess_all_feature`: 特征预处理函数，补齐缺失值
   - `preprocess_varlen`: 序列特征处理函数
   - `generate_ds`: 生成训练数据集的函数
   - `cumsum_loss`/`cumsum_mask_loss`: 累积损失函数
   - `gene_purchase_array`: 购买标签生成函数
   - 多个样本采样函数(`get_sample_data_v1/v2/v3/v4`)

3. **EPMMOENet.py** (13KB)
   - 模型网络定义文件
   - 实现`EPMMOENet_Model`类，继承自`tf.keras.Model`
   - 包含模型的构建逻辑、层定义和前向传播函数
   - 处理不同类型特征的输入和融合机制

4. **dataset_analyze.py** (24KB)
   - 数据分析脚本
   - 实现对数据集和特征的统计分析
   - 生成各类数据统计和可视化结果
   - 输出分析报告到`analysis_results`目录

### 12.2 配置文件

1. **sample_20250311_v7-20250311.json** (106KB)
   - 模型参数配置文件
   - 定义了网络结构、特征处理方式等配置
   - 包含各类特征的详细配置信息
   - 训练和测试数据集划分配置

2. **dataset_nio_new_car_v15.json** (111KB)
   - 数据集配置文件
   - 定义了特征预处理参数
   - 包含特征类型、默认值等配置

### 12.3 数据和结果目录

1. **dataset_nio_new_car_v15/**
   - 数据集目录
   - 包含按日期分区的训练和测试数据
   - `datetime=20240430/`: 训练集分区
   - `datetime=20240531/`: 测试集分区
   - `20240531_随机采样1%.parquet`: 评估集文件

2. **20250317_v1/**
   - 模型训练结果目录
   - 包含模型权重、训练结果和评估指标
   - `ckpt_by_month.weights.h5`: 保存的模型权重
   - `*_metrics.json`: 模型评估指标
   - `test_result.parquet`/`evalute_result.parquet`: 测试和评估结果

3. **analysis_results/**
   - 数据分析结果目录
   - 包含各类统计结果和可视化图表
   - 特征分布统计和可视化
   - 标签分布和模式分析

### 12.4 文件依赖关系

```mermaid
graph TD
    A[demo.py] --> B[model_utils.py]
    A --> C[EPMMOENet.py]
    A --> D[sample_20250311_v7-20250311.json]
    A --> E[dataset_nio_new_car_v15.json]
    F[dataset_analyze.py] --> D
    F --> E
    F --> G[analysis_results/]
    A --> H[20250317_v1/]
    A --> I[dataset_nio_new_car_v15/]
```

### 12.5 文件使用指南

1. **模型训练**:
   ```bash
   python demo.py -model_code sample_20250311_v7-20250311 -run_name 20250317_v1 -dataset_code dataset_nio_new_car_v15 -evaluate_file 20240531_随机采样1%.parquet
   ```

2. **数据分析**:
   ```bash
   python dataset_analyze.py
   ```

3. **模型结构修改**:
   - 修改`EPMMOENet.py`文件中的网络结构
   - 调整`__init__`和`call`方法实现新的网络结构

4. **特征工程改进**:
   - 在`model_utils.py`中添加新的特征处理函数
   - 在`sample_20250311_v7-20250311.json`中配置新特征

### 12.6 开发建议

1. **代码组织优化**:
   - 考虑将特征处理、模型定义和训练评估逻辑更清晰地分离
   - 优化`model_utils.py`中的函数组织结构，提高复用性

2. **特征处理优化**:
   - 统一特征处理接口，方便后续添加新特征
   - 将序列特征处理逻辑封装为独立模块

3. **模型结构优化**:
   - 基于11章中的改进方向，调整`EPMMOENet.py`
   - 添加新的模型架构变体进行对比实验

4. **评估体系完善**:
   - 增强模型评估模块，支持更多评估指标
   - 添加模型解释功能，分析特征重要性

后续开发时，可基于现有文件结构进行迭代，重点关注特征工程优化、模型结构改进和训练策略调整，以提升模型的整体表现，特别是远期月份的预测精度。 