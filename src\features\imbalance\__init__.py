"""
Imbalanced learning module for handling class imbalance problems.

This module provides various techniques for dealing with imbalanced datasets:
- Resampling techniques (SMOTE, SMOTE-ENN)
- Balanced dataset creation
- Oversampling utilities
"""

# Import key functions from submodules
from .samplers import apply_smote, apply_smote_enn, apply_optimized_smote_enn
from .balanced_dataset import create_balanced_dataset, create_dynamic_balanced_dataset, oversample_positive_samples

__all__ = [
    'apply_smote', 
    'apply_smote_enn', 
    'apply_optimized_smote_enn',
    'create_balanced_dataset', 
    'create_dynamic_balanced_dataset',
    'oversample_positive_samples'
] 