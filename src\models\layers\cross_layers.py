"""
特征交叉层模块
"""
import tensorflow as tf
from tensorflow.keras.layers import Den<PERSON>, <PERSON><PERSON>ly, Add, Lambda, Concatenate

def create_cross_layer(inputs, units=16, activation='relu', num_crosses=1):
    """
    创建交叉层 - 用于特征交叉
    
    Args:
        inputs: 输入张量
        units: 交叉层输出维度，如果设置为-1则自动匹配输入维度
        activation: 激活函数
        num_crosses: 交叉层数量
    
    Returns:
        交叉层输出
    """
    
    # 打印输入形状进行调试
    print(f"Cross layer input shape: {inputs.shape}")
    
    # 如果units为-1，自动设置为与输入维度匹配
    if units == -1:
        units = inputs.shape[-1]
        print(f"Auto-setting units to match input dimension: {units}")
    
    # 初始化为输入
    cross = inputs
    
    # 应用多次交叉层
    for i in range(num_crosses):
        # 输入变换
        transformed = Dense(units, activation=activation)(cross)
        print(f"Cross layer {i+1} transformed shape: {transformed.shape}")
        
        # 交叉操作
        cross = Dense(units, activation=None)(transformed)
        print(f"Cross layer {i+1} cross shape: {cross.shape}")
        
        # 残差连接
        # 注意：这里我们确保cross和inputs具有相同的维度
        if units != inputs.shape[-1]:
            # 如果维度不匹配，则将cross投影到与inputs相同的维度
            cross = Dense(inputs.shape[-1], activation=None)(cross)
            print(f"Cross layer {i+1} resized to: {cross.shape}")
        
        # 残差连接 - 将交叉结果与输入相加
        cross = Add()([cross, inputs])
        print(f"Cross layer {i+1} with residual shape: {cross.shape}")
    
    return cross

def create_feature_interaction(features, interaction_type='FM'):
    """
    创建特征交互层
    
    Args:
        features (tf.Tensor): 输入特征
        interaction_type (str): 交互类型，可选'FM'(因子分解机)或'DCN'(深度交叉网络)
        
    Returns:
        tf.Tensor: 特征交互结果
    """
    if interaction_type == 'FM':
        # 实现因子分解机交互
        square_sum = tf.square(tf.reduce_sum(features, axis=1, keepdims=True))
        sum_square = tf.reduce_sum(tf.square(features), axis=1, keepdims=True)
        interaction = 0.5 * tf.subtract(square_sum, sum_square)
        return interaction
    
    elif interaction_type == 'DCN':
        # 实现深度交叉网络交互
        cross_output = features
        for i in range(3):  # 3层交叉
            # x0 * (w * x + b) + x
            cross_weight = tf.Variable(
                tf.random.normal([1, features.shape[1]]),
                trainable=True,
                name=f"dcn_weight_{i}"
            )
            cross_bias = tf.Variable(
                tf.zeros([features.shape[1]]),
                trainable=True,
                name=f"dcn_bias_{i}"
            )
            
            # 计算x' = x0 * (x^T * w + b) + x
            cross = tf.matmul(cross_output, cross_weight, transpose_b=True)
            cross = cross + cross_bias
            cross = Multiply()([features, cross])
            cross_output = Add()([cross, cross_output])
            
        return cross_output
    
    else:
        raise ValueError(f"不支持的交互类型: {interaction_type}") 