#!/bin/bash

# 修正后的embedding提取命令
# 需要根据训练时的实际配置调整参数

echo "=== 修正embedding提取命令 ==="
echo "问题：维度不匹配 387 vs 379"
echo "原因：训练和推理时配置不一致"
echo ""

echo "原始命令（有问题）："
echo "python embedding_v2/embedding_model_train.py --output-dir my_test_run --mode extract_embedding --test-date 20240531"
echo ""

echo "修正后的命令（请根据训练时的实际配置调整）："

# 情况1：如果训练时使用了增强模型 + 静态特征
echo "# 情况1：增强模型 + 静态特征"
echo "python embedding/embedding_model_train.py \\"
echo "  --output-dir my_test_run \\"
echo "  --mode extract_embeddings \\"
echo "  --test-date 20240531 \\"
echo "  --use-enhanced-model \\"
echo "  --include-static-features \\"
echo "  --additional-sequences veh_model_seq reg_channel_seq td_type_seq \\"
echo "  --maxlen 64 \\"
echo "  --embed-dim 256"
echo ""

# 情况2：如果训练时使用了标准模型 + 静态特征
echo "# 情况2：标准模型 + 静态特征" 
echo "python embedding/embedding_model_train.py \\"
echo "  --output-dir my_test_run \\"
echo "  --mode extract_embeddings \\"
echo "  --test-date 20240531 \\"
echo "  --include-static-features \\"
echo "  --maxlen 64 \\"
echo "  --embed-dim 256"
echo ""

# 情况3：如果训练时只使用了序列特征
echo "# 情况3：仅序列特征"
echo "python embedding/embedding_model_train.py \\"
echo "  --output-dir my_test_run \\"
echo "  --mode extract_embeddings \\"
echo "  --test-date 20240531 \\"
echo "  --maxlen 64 \\"
echo "  --embed-dim 256"
echo ""

echo "=== 诊断步骤 ==="
echo "1. 检查训练输出目录中的training_config.json文件"
echo "2. 查看该文件中的配置参数"
echo "3. 使用相同的参数运行extract_embeddings" 