"""
特征处理模块
"""
import tensorflow as tf
from tensorflow.keras.layers import Dense, Concatenate

def process_numerical_features(numerical_inputs):
    """
    处理数值特征
    
    Args:
        numerical_inputs (dict): 数值输入字典
        
    Returns:
        tf.Tensor: 处理后的数值特征
    """
    numerical_outputs = []
    
    # 处理每个数值特征
    for feature_name, input_tensor in numerical_inputs.items():
        # 简单的线性变换
        processed = Dense(1, activation=None, name=f"process_{feature_name}")(input_tensor)
        numerical_outputs.append(processed)
    
    # 如果有多个数值特征，拼接它们
    if len(numerical_outputs) > 1:
        return Concatenate(name="numerical_concat")(numerical_outputs)
    elif len(numerical_outputs) == 1:
        return numerical_outputs[0]
    else:
        return None

def process_categorical_features(embedding_list):
    """
    处理类别特征的嵌入
    
    Args:
        embedding_list (list): 嵌入向量列表
        
    Returns:
        tf.Tensor: 处理后的类别特征
    """
    # 如果有多个嵌入，拼接它们
    if len(embedding_list) > 1:
        return Concatenate(name="categorical_concat")(embedding_list)
    elif len(embedding_list) == 1:
        return embedding_list[0]
    else:
        return None 